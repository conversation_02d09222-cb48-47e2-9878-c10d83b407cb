import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/storage/hive_service.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

class MockHiveService extends Mock implements HiveService {}

class MockAuthRepository extends Mock implements AuthRepository {}

class MockFaceDetectionRepository extends Mock
    implements FaceDetectionRepository {}

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

class MockVideoValidationService extends Mock
    implements VideoValidationService {}

class MockVideoGalleryRepository extends Mock
    implements VideoGalleryRepository {}

void main() {
  group('Injection', () {
    late GetIt getIt;

    setUp(() {
      getIt = GetIt.instance;
    });

    tearDown(() async {
      await getIt.reset();
    });

    group('static class', () {
      test('should not be instantiable', () {
        // Test that Injection is a static class and cannot be instantiated
        // Since it has a private constructor, we test that the class exists
        expect(Injection.initialize, isA<Function>());
        expect(Injection.reset, isA<Function>());
      });
    });

    group('mocked dependency registration', () {
      test('should register mocked dependencies successfully', () async {
        // Register mocked dependencies manually to test the structure
        final mockHiveService = MockHiveService();
        final mockAuthRepository = MockAuthRepository();
        final mockFaceDetectionRepository = MockFaceDetectionRepository();
        final mockVideoStorageRepository = MockVideoStorageRepository();
        final mockVideoValidationService = MockVideoValidationService();
        final mockVideoGalleryRepository = MockVideoGalleryRepository();

        getIt
          ..registerSingleton<HiveService>(mockHiveService)
          ..registerLazySingleton<AuthRepository>(() => mockAuthRepository)
          ..registerLazySingleton<FaceDetectionRepository>(
            () => mockFaceDetectionRepository,
          )
          ..registerLazySingleton<VideoStorageRepository>(
            () => mockVideoStorageRepository,
          )
          ..registerLazySingleton<VideoValidationService>(
            () => mockVideoValidationService,
          )
          ..registerLazySingleton<VideoGalleryRepository>(
            () => mockVideoGalleryRepository,
          )
          ..registerLazySingleton<AuthCubit>(
            () => AuthCubit(getIt<AuthRepository>()),
          )
          ..registerFactory<FaceVideoCaptureBloc>(
            () => FaceVideoCaptureBloc(
              faceDetectionRepository: getIt<FaceDetectionRepository>(),
              videoStorageRepository: getIt<VideoStorageRepository>(),
              videoValidationService: getIt<VideoValidationService>(),
            ),
          );

        // Verify that all expected dependencies are registered
        expect(getIt.isRegistered<HiveService>(), isTrue);
        expect(getIt.isRegistered<AuthRepository>(), isTrue);
        expect(getIt.isRegistered<AuthCubit>(), isTrue);
        expect(getIt.isRegistered<FaceDetectionRepository>(), isTrue);
        expect(getIt.isRegistered<VideoStorageRepository>(), isTrue);
        expect(getIt.isRegistered<VideoValidationService>(), isTrue);
        expect(getIt.isRegistered<VideoGalleryRepository>(), isTrue);
        expect(getIt.isRegistered<FaceVideoCaptureBloc>(), isTrue);
      });
    });

    group('dependency registration patterns', () {
      test('should register singletons correctly', () async {
        final mockHiveService = MockHiveService();

        getIt.registerSingleton<HiveService>(mockHiveService);

        final hiveService1 = getIt<HiveService>();
        final hiveService2 = getIt<HiveService>();

        expect(hiveService1, same(hiveService2));
        expect(hiveService1, same(mockHiveService));
      });

      test('should register lazy singletons correctly', () async {
        final mockAuthRepository = MockAuthRepository();

        getIt.registerLazySingleton<AuthRepository>(() => mockAuthRepository);

        final authRepo1 = getIt<AuthRepository>();
        final authRepo2 = getIt<AuthRepository>();

        expect(authRepo1, same(authRepo2));
        expect(authRepo1, same(mockAuthRepository));
      });

      test('should register factories correctly', () async {
        final mockFaceDetectionRepository = MockFaceDetectionRepository();
        final mockVideoStorageRepository = MockVideoStorageRepository();
        final mockVideoValidationService = MockVideoValidationService();

        // Setup mock returns
        when(mockFaceDetectionRepository.dispose).thenAnswer((_) async {});
        when(mockVideoStorageRepository.dispose).thenAnswer((_) async {});

        getIt
          ..registerLazySingleton<FaceDetectionRepository>(
            () => mockFaceDetectionRepository,
          )
          ..registerLazySingleton<VideoStorageRepository>(
            () => mockVideoStorageRepository,
          )
          ..registerLazySingleton<VideoValidationService>(
            () => mockVideoValidationService,
          )
          ..registerFactory<FaceVideoCaptureBloc>(
            () => FaceVideoCaptureBloc(
              faceDetectionRepository: getIt<FaceDetectionRepository>(),
              videoStorageRepository: getIt<VideoStorageRepository>(),
              videoValidationService: getIt<VideoValidationService>(),
            ),
          );

        final bloc1 = getIt<FaceVideoCaptureBloc>();
        final bloc2 = getIt<FaceVideoCaptureBloc>();

        // Factory should create new instances
        expect(bloc1, isNot(same(bloc2)));

        // Clean up
        await bloc1.close();
        await bloc2.close();
      });
    });

    group('dependency resolution', () {
      test('should resolve dependencies correctly', () async {
        final mockAuthRepository = MockAuthRepository();
        final mockFaceDetectionRepository = MockFaceDetectionRepository();
        final mockVideoStorageRepository = MockVideoStorageRepository();
        final mockVideoValidationService = MockVideoValidationService();

        // Setup mock returns
        when(() => mockAuthRepository.user).thenAnswer(
          (_) => const Stream.empty(),
        );
        when(mockFaceDetectionRepository.dispose).thenAnswer((_) async {});
        when(mockVideoStorageRepository.dispose).thenAnswer((_) async {});

        getIt
          ..registerLazySingleton<AuthRepository>(() => mockAuthRepository)
          ..registerLazySingleton<FaceDetectionRepository>(
            () => mockFaceDetectionRepository,
          )
          ..registerLazySingleton<VideoStorageRepository>(
            () => mockVideoStorageRepository,
          )
          ..registerLazySingleton<VideoValidationService>(
            () => mockVideoValidationService,
          )
          ..registerLazySingleton<AuthCubit>(
            () => AuthCubit(getIt<AuthRepository>()),
          )
          ..registerFactory<FaceVideoCaptureBloc>(
            () => FaceVideoCaptureBloc(
              faceDetectionRepository: getIt<FaceDetectionRepository>(),
              videoStorageRepository: getIt<VideoStorageRepository>(),
              videoValidationService: getIt<VideoValidationService>(),
            ),
          );

        // Test that AuthCubit gets the correct AuthRepository
        final authCubit = getIt<AuthCubit>();
        expect(authCubit, isA<AuthCubit>());

        // Test that FaceVideoCaptureBloc gets all required dependencies
        final faceBloc = getIt<FaceVideoCaptureBloc>();
        expect(faceBloc, isA<FaceVideoCaptureBloc>());

        await faceBloc.close();
      });
    });

    group('reset', () {
      test('should reset all dependencies', () async {
        final mockHiveService = MockHiveService();
        final mockAuthRepository = MockAuthRepository();

        getIt
          ..registerSingleton<HiveService>(mockHiveService)
          ..registerLazySingleton<AuthRepository>(() => mockAuthRepository);

        // Verify dependencies are registered
        expect(getIt.isRegistered<HiveService>(), isTrue);
        expect(getIt.isRegistered<AuthRepository>(), isTrue);

        await getIt.reset();

        // Verify dependencies are no longer registered
        expect(getIt.isRegistered<HiveService>(), isFalse);
        expect(getIt.isRegistered<AuthRepository>(), isFalse);
      });

      test('should handle reset when no dependencies registered', () async {
        // Don't register anything, just try to reset
        await expectLater(
          getIt.reset,
          returnsNormally,
        );
      });
    });

    group('global getIt instance', () {
      test('should provide access to global GetIt instance', () {
        expect(getIt, isA<GetIt>());
        expect(getIt, same(GetIt.instance));
      });
    });
  });
}
