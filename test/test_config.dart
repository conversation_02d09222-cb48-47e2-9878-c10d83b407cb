import 'package:flutter/widgets.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Global test configuration that ensures Flutter bindings are initialized
/// exactly once for all tests in the project.
/// 
/// This should be imported in every test file that needs Flutter widget testing.
void setupFlutterTestBindings() {
  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();
  });
}
