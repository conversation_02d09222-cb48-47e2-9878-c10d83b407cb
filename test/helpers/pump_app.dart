import 'package:bloomg_flutter/l10n/arb/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Legacy extension on [WidgetTester] for backward compatibility.
/// 
/// @deprecated Use [TestHelpers.pumpWidgetWithMaterial] instead.
/// This extension is maintained for backward compatibility with existing tests.
extension PumpApp on WidgetTester {
  /// Pumps a widget wrapped in a MaterialApp with localization support.
  /// 
  /// @deprecated Use [pumpWidgetWithMaterial] from [TestHelpers] instead.
  /// This method is maintained for backward compatibility.
  Future<void> pumpApp(Widget widget) {
    return pumpWidget(
      MaterialApp(
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: Scaffold(
          body: widget,
        ),
      ),
    );
  }
}
