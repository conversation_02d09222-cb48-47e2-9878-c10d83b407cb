import 'dart:async';

import 'package:fake_async/fake_async.dart';
import 'package:flutter_test/flutter_test.dart';

/// Utilities for testing BLoC behaviors, timers, and async code.
///
/// This class provides helpers to handle timers, flush microtasks, and simulate
/// frame streams in a deterministic way for testing.
///
/// Usage:
/// ```dart
/// testWidgets('timer test', (tester) async {
///   await BlocTestUtils.pumpWithTimers(tester, Duration(seconds: 1));
///   // Test timer-based behavior
/// });
/// ```
class BlocTestUtils {
  /// Pumps the event loop to allow timers to advance and microtasks to
  /// complete.
  ///
  /// This is a blocking call that will flush all pending timers and
  /// microtasks. It should be called when you need the system to catch up
  /// on all async work.
  static Future<void> flushMicrotasksAndTimers(WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pump();
      await Future<void>.delayed(Duration.zero);
    });
  }

  /// Pumps the widget tree and advances timers by the specified duration.
  ///
  /// This is useful for testing timer-based behaviors in widgets.
  /// It combines [WidgetTester.pump] with timer advancement.
  ///
  /// [tester] The widget tester instance.
  /// [duration] The duration to advance timers by.
  static Future<void> pumpWithTimers(
    WidgetTester tester,
    Duration duration,
  ) async {
    await tester.pump(duration);
    await flushMicrotasksAndTimers(tester);
  }

  /// Pumps the widget tree and settles all animations and timers.
  ///
  /// This is useful when you need to ensure all animations and timers
  /// have completed before making assertions.
  static Future<void> pumpAndSettle(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    await tester.pumpAndSettle(timeout);
    await flushMicrotasksAndTimers(tester);
  }

  /// Simulates a frame stream with a given duration between frames.
  ///
  /// This is useful when you need a specific frame timing for your tests,
  /// such as testing animations or periodic updates.
  ///
  /// [tester] The widget tester instance.
  /// [interval] The duration between frames.
  /// [frameCount] The number of frames to simulate (default: 60).
  static Future<void> simulateFrameStream(
    WidgetTester tester,
    Duration interval, {
    int frameCount = 60,
  }) async {
    for (var i = 0; i < frameCount; i++) {
      await tester.pump(interval);
    }
  }

  /// Simulates a countdown timer by advancing time in discrete steps.
  ///
  /// This is particularly useful for testing countdown timers in the UI.
  ///
  /// [tester] The widget tester instance.
  /// [totalDuration] The total duration of the countdown.
  /// [stepDuration] The duration of each step (default: 1 second).
  static Future<void> simulateCountdown(
    WidgetTester tester,
    Duration totalDuration, {
    Duration stepDuration = const Duration(seconds: 1),
  }) async {
    final steps = totalDuration.inMilliseconds ~/ stepDuration.inMilliseconds;
    for (var i = 0; i < steps; i++) {
      await pumpWithTimers(tester, stepDuration);
    }
  }

  /// Wraps a test body with [FakeAsync] to allow control over time advancement.
  ///
  /// Use this to test time-dependent logic without real time passing. This 
  /// function should be called in your tests to wrap any code that relies on
  /// timers or needs to manually advance time.
  ///
  /// Example:
  /// ```dart
  /// test('timer test', () {
  ///   BlocTestUtils.runFakeAsync((fakeAsync) {
  ///     // Setup code
  ///     fakeAsync.elapse(Duration(seconds: 1));
  ///     // Assertions
  ///   });
  /// });
  /// ```
  static void runFakeAsync(void Function(FakeAsync) callback) {
    fakeAsync(callback);
  }
  
  /// Advances the clock in a [FakeAsync] environment by a given [Duration].
  ///
  /// This can be used to test the effects of time passing on code that depends
  /// on timers within a [FakeAsync.run] environment.
  ///
  /// [fakeAsync] The fake async instance.
  /// [duration] The duration to advance by.
  static void advanceTimeBy(FakeAsync fakeAsync, Duration duration) {
    fakeAsync.elapse(duration);
  }

  /// Advances the clock in a [FakeAsync] environment by small increments.
  ///
  /// This is useful for testing behaviors that depend on gradual time passage.
  ///
  /// [fakeAsync] The fake async instance.
  /// [totalDuration] The total duration to advance.
  /// [stepDuration] The duration of each step (default: 100ms).
  static void advanceTimeGradually(
    FakeAsync fakeAsync,
    Duration totalDuration, {
    Duration stepDuration = const Duration(milliseconds: 100),
  }) {
    final steps = totalDuration.inMilliseconds ~/ stepDuration.inMilliseconds;
    for (var i = 0; i < steps; i++) {
      fakeAsync.elapse(stepDuration);
    }
  }

  /// Flushes all pending microtasks in a [FakeAsync] environment.
  ///
  /// This is useful when you need to ensure all microtasks have been processed
  /// before making assertions.
  static void flushMicrotasks(FakeAsync fakeAsync) {
    fakeAsync.flushMicrotasks();
  }

  /// Flushes all pending timers in a [FakeAsync] environment.
  ///
  /// This is useful when you need to ensure all timers have been fired
  /// before making assertions.
  static void flushTimers(FakeAsync fakeAsync) {
    fakeAsync.flushTimers();
  }

  /// Creates a stream that emits values at regular intervals.
  ///
  /// This is useful for testing stream-based behaviors in BLoCs.
  ///
  /// [values] The values to emit.
  /// [interval] The interval between emissions.
  /// 
  /// Returns a stream that emits the values at the specified interval.
  static Stream<T> createPeriodicStream<T>(
    List<T> values,
    Duration interval,
  ) {
    final controller = StreamController<T>();
    
    var index = 0;
    Timer.periodic(interval, (timer) {
      if (index < values.length) {
        controller.add(values[index]);
        index++;
      } else {
        timer.cancel();
        controller.close();
      }
    });
    
    return controller.stream;
  }

  /// Creates a stream that emits a single value after a delay.
  ///
  /// This is useful for testing delayed operations in BLoCs.
  ///
  /// [value] The value to emit.
  /// [delay] The delay before emitting the value.
  /// 
  /// Returns a stream that emits the value after the specified delay.
  static Stream<T> createDelayedStream<T>(T value, Duration delay) {
    return Stream<T>.fromFuture(
      Future<T>.delayed(delay, () => value),
    );
  }

  /// Creates a stream that emits values and then throws an error.
  ///
  /// This is useful for testing error handling in BLoCs.
  ///
  /// [values] The values to emit before the error.
  /// [error] The error to throw.
  /// [interval] The interval between emissions.
  /// 
  /// Returns a stream that emits the values and then throws the error.
  static Stream<T> createErrorStream<T>(
    List<T> values,
    Object error, {
    Duration interval = const Duration(milliseconds: 100),
  }) {
    final controller = StreamController<T>();
    
    var index = 0;
    Timer.periodic(interval, (timer) {
      if (index < values.length) {
        controller.add(values[index]);
        index++;
      } else {
        timer.cancel();
        controller
          ..addError(error)
          ..close();
      }
    });
    
    return controller.stream;
  }
}


