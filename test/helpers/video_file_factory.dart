import 'dart:io';
import 'dart:typed_data';

/// Factory for creating different types of MP4 files for testing purposes.
///
/// This class provides utilities to generate valid, empty, and corrupted MP4 files
/// that can be used in tests without requiring actual video files.
///
/// Usage:
/// ```dart
/// // Create a valid MP4 file
/// final validFile = await VideoFileFactory.createValidMp4File();
///
/// // Create a corrupted MP4 file
/// final corruptedFile = await VideoFileFactory.createCorruptedMp4File();
/// ```
class VideoFileFactory {
  /// Creates a valid MP4 file with minimal content.
  ///
  /// This generates a file with valid MP4 headers and minimal content
  /// that can be used for testing file operations.
  ///
  /// [filename] The name of the file to create (without extension).
  /// [directory] The directory to create the file in (defaults to system temp).
  /// 
  /// Returns a [File] instance pointing to the created MP4 file.
  static Future<File> createValidMp4File([
    String filename = 'test_video',
    Directory? directory,
  ]) async {
    final dir = directory ?? Directory.systemTemp;
    final file = File('${dir.path}/$filename.mp4');
    
    // Create a minimal valid MP4 file with proper headers
    final bytes = _createValidMp4Bytes();
    await file.writeAsBytes(bytes);
    
    return file;
  }

  /// Creates an empty MP4 file (0 bytes).
  ///
  /// This generates a file with no content, useful for testing
  /// error handling when dealing with empty files.
  ///
  /// [filename] The name of the file to create (without extension).
  /// [directory] The directory to create the file in (defaults to system temp).
  /// 
  /// Returns a [File] instance pointing to the created empty MP4 file.
  static Future<File> createEmptyMp4File([
    String filename = 'empty_video',
    Directory? directory,
  ]) async {
    final dir = directory ?? Directory.systemTemp;
    final file = File('${dir.path}/$filename.mp4');
    
    // Create an empty file
    await file.writeAsBytes(<int>[]);
    
    return file;
  }

  /// Creates a corrupted MP4 file with invalid headers.
  ///
  /// This generates a file with corrupted MP4 headers that can be used
  /// for testing error handling in video processing.
  ///
  /// [filename] The name of the file to create (without extension).
  /// [directory] The directory to create the file in (defaults to system temp).
  /// [corruptionType] The type of corruption to apply.
  /// 
  /// Returns a [File] instance pointing to the created corrupted MP4 file.
  static Future<File> createCorruptedMp4File([
    String filename = 'corrupted_video',
    Directory? directory,
    Mp4CorruptionType corruptionType = Mp4CorruptionType.invalidHeader,
  ]) async {
    final dir = directory ?? Directory.systemTemp;
    final file = File('${dir.path}/$filename.mp4');
    
    final bytes = _createCorruptedMp4Bytes(corruptionType);
    await file.writeAsBytes(bytes);
    
    return file;
  }

  /// Creates a large MP4 file for testing file size limits.
  ///
  /// This generates a file with valid headers but large content
  /// for testing file size restrictions.
  ///
  /// [filename] The name of the file to create (without extension).
  /// [directory] The directory to create the file in (defaults to system temp).
  /// [sizeInMB] The approximate size of the file in megabytes.
  /// 
  /// Returns a [File] instance pointing to the created large MP4 file.
  static Future<File> createLargeMp4File([
    String filename = 'large_video',
    Directory? directory,
    int sizeInMB = 10,
  ]) async {
    final dir = directory ?? Directory.systemTemp;
    final file = File('${dir.path}/$filename.mp4');
    
    final bytes = _createLargeMp4Bytes(sizeInMB);
    await file.writeAsBytes(bytes);
    
    return file;
  }

  /// Creates a temporary MP4 file that will be automatically deleted.
  ///
  /// This creates a temporary file that can be used for testing
  /// and will be cleaned up automatically.
  ///
  /// [filename] The name of the file to create (without extension).
  /// [type] The type of MP4 file to create.
  /// 
  /// Returns a [File] instance pointing to the created temporary MP4 file.
  static Future<File> createTempMp4File([
    String filename = 'temp_video',
    Mp4FileType type = Mp4FileType.valid,
  ]) async {
    final tempDir = await Directory.systemTemp.createTemp('video_test_');
    
    switch (type) {
      case Mp4FileType.valid:
        return createValidMp4File(filename, tempDir);
      case Mp4FileType.empty:
        return createEmptyMp4File(filename, tempDir);
      case Mp4FileType.corrupted:
        return createCorruptedMp4File(filename, tempDir);
      case Mp4FileType.large:
        return createLargeMp4File(filename, tempDir);
    }
  }

  /// Cleans up temporary files created during testing.
  ///
  /// This method removes all temporary files and directories
  /// created by the factory methods.
  ///
  /// [files] A list of files to clean up. If not provided, 
  /// cleans up all temporary files.
  static Future<void> cleanupTempFiles([List<File>? files]) async {
    if (files != null) {
      for (final file in files) {
        if (await file.exists()) {
          await file.delete();
        }
      }
    } else {
      // Clean up all temporary video test directories
      final tempDir = Directory.systemTemp;
      final entities = await tempDir.list().toList();
      
      for (final entity in entities) {
        if (entity is Directory && entity.path.contains('video_test_')) {
          await entity.delete(recursive: true);
        }
      }
    }
  }

  /// Creates a minimal valid MP4 byte sequence.
  ///
  /// This generates the minimal bytes needed for a valid MP4 file.
  /// The structure includes:
  /// - ftyp box (file type)
  /// - mdat box (media data)
  static Uint8List _createValidMp4Bytes() {
    final buffer = <int>[];
    
    // ftyp box (file type box)
    buffer.addAll([
      0x00, 0x00, 0x00, 0x20, // box size (32 bytes)
      0x66, 0x74, 0x79, 0x70, // box type 'ftyp'
      0x69, 0x73, 0x6F, 0x6D, // major brand 'isom'
      0x00, 0x00, 0x02, 0x00, // minor version
      0x69, 0x73, 0x6F, 0x6D, // compatible brand 'isom'
      0x69, 0x73, 0x6F, 0x32, // compatible brand 'iso2'
      0x61, 0x76, 0x63, 0x31, // compatible brand 'avc1'
      0x6D, 0x70, 0x34, 0x31, // compatible brand 'mp41'
    ]);
    
    // mdat box (media data box) - minimal content
    buffer.addAll([
      0x00, 0x00, 0x00, 0x08, // box size (8 bytes)
      0x6D, 0x64, 0x61, 0x74, // box type 'mdat'
    ]);
    
    return Uint8List.fromList(buffer);
  }

  /// Creates corrupted MP4 bytes based on the corruption type.
  static Uint8List _createCorruptedMp4Bytes(Mp4CorruptionType corruptionType) {
    switch (corruptionType) {
      case Mp4CorruptionType.invalidHeader:
        // Invalid magic bytes
        return Uint8List.fromList([
          0xFF, 0xFF, 0xFF, 0xFF, // invalid size
          0x00, 0x00, 0x00, 0x00, // invalid type
          0x69, 0x73, 0x6F, 0x6D, // some valid data
        ]);
      
      case Mp4CorruptionType.truncatedFile:
        // Start of valid MP4 but truncated
        return Uint8List.fromList([
          0x00, 0x00, 0x00, 0x20, // box size (32 bytes)
          0x66, 0x74, 0x79, 0x70, // box type 'ftyp'
          0x69, 0x73, 0x6F, 0x6D, // major brand 'isom'
          // File ends abruptly here
        ]);
      
      case Mp4CorruptionType.randomData:
        // Random bytes that don't form a valid MP4
        return Uint8List.fromList(
          List.generate(100, (index) => index % 256),
        );
    }
  }

  /// Creates large MP4 bytes for testing file size limits.
  static Uint8List _createLargeMp4Bytes(int sizeInMB) {
    final buffer = <int>[];
    
    // Add valid MP4 header
    buffer.addAll(_createValidMp4Bytes());
    
    // Add padding to reach desired size
    final targetSize = sizeInMB * 1024 * 1024;
    final remainingSize = targetSize - buffer.length;
    
    if (remainingSize > 0) {
      // Create a large mdat box with padding
      final paddingSize = remainingSize - 8; // 8 bytes for box header
      buffer.addAll([
        (paddingSize >> 24) & 0xFF,
        (paddingSize >> 16) & 0xFF,
        (paddingSize >> 8) & 0xFF,
        paddingSize & 0xFF,
        0x6D, 0x64, 0x61, 0x74, // 'mdat'
      ]);
      
      // Add padding data
      buffer.addAll(List.filled(paddingSize - 4, 0x00));
    }
    
    return Uint8List.fromList(buffer);
  }
}

/// Types of MP4 files that can be generated.
enum Mp4FileType {
  /// A valid MP4 file with proper headers.
  valid,
  
  /// An empty MP4 file (0 bytes).
  empty,
  
  /// A corrupted MP4 file with invalid headers.
  corrupted,
  
  /// A large MP4 file for testing size limits.
  large,
}

/// Types of corruption that can be applied to MP4 files.
enum Mp4CorruptionType {
  /// Invalid MP4 header with wrong magic bytes.
  invalidHeader,
  
  /// Truncated file that ends unexpectedly.
  truncatedFile,
  
  /// Random data that doesn't form a valid MP4.
  randomData,
}
