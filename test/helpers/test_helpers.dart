import 'package:bloomg_flutter/l10n/arb/app_localizations.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Extension on [WidgetTester] providing reusable widget testing utilities.
///
/// This extension solves common widget testing issues by providing:
/// - Proper Material Design ancestor context
/// - Cupertino ancestor context
/// - MediaQuery with default values
/// - Localization support
/// - Theme support
/// - Directionality support
/// - Navigator support
extension TestHelpers on WidgetTester {
  /// Pumps a widget wrapped in a complete Material app context.
  ///
  /// This method solves common widget testing issues by providing:
  /// - [MaterialApp] ancestor for Material widgets
  /// - [MediaQuery] with default values
  /// - [Theme] with default Material theme
  /// - [Directionality] with left-to-right text direction
  /// - [Navigator] for route-based widgets
  /// - [Localizations] with app-specific localization delegates
  ///
  /// Use this for testing widgets that:
  /// - Use Material Design components
  /// - Need Material theme context
  /// - Require MediaQuery data
  /// - Use Material navigation
  ///
  /// Example:
  /// ```dart
  /// testWidgets('my widget test', (tester) async {
  ///   await tester.pumpWidgetWithMaterial(
  ///     const MyWidget(),
  ///   );
  ///
  ///   expect(find.byType(MyWidget), findsOneWidget);
  /// });
  /// ```
  Future<void> pumpWidgetWithMaterial(
    Widget widget, {
    ThemeData? theme,
    bool useMaterial3 = true,
    Locale? locale,
    List<Locale>? supportedLocales,
    List<LocalizationsDelegate<dynamic>>? localizationsDelegates,
    Duration? duration,
  }) {
    return pumpWidget(
      MaterialApp(
        theme: theme ??
            ThemeData(
              useMaterial3: useMaterial3,
            ),
        locale: locale,
        localizationsDelegates:
            localizationsDelegates ?? AppLocalizations.localizationsDelegates,
        supportedLocales: supportedLocales ?? AppLocalizations.supportedLocales,
        home: Scaffold(
          body: widget,
        ),
      ),
      duration: duration,
    );
  }

  /// Pumps a widget wrapped in a complete Cupertino app context.
  ///
  /// This method solves common widget testing issues by providing:
  /// - [CupertinoApp] ancestor for Cupertino widgets
  /// - [MediaQuery] with default values
  /// - [CupertinoTheme] with default Cupertino theme
  /// - [Directionality] with left-to-right text direction
  /// - [Navigator] for route-based widgets
  /// - [Localizations] with app-specific localization delegates
  ///
  /// Use this for testing widgets that:
  /// - Use Cupertino Design components
  /// - Need Cupertino theme context
  /// - Require MediaQuery data
  /// - Use Cupertino navigation
  ///
  /// Example:
  /// ```dart
  /// testWidgets('my cupertino widget test', (tester) async {
  ///   await tester.pumpWidgetWithCupertino(
  ///     const MyCupertinoWidget(),
  ///   );
  ///
  ///   expect(find.byType(MyCupertinoWidget), findsOneWidget);
  /// });
  /// ```
  Future<void> pumpWidgetWithCupertino(
    Widget widget, {
    CupertinoThemeData? theme,
    Locale? locale,
    List<Locale>? supportedLocales,
    List<LocalizationsDelegate<dynamic>>? localizationsDelegates,
    Duration? duration,
  }) {
    return pumpWidget(
      CupertinoApp(
        theme: theme,
        locale: locale,
        localizationsDelegates:
            localizationsDelegates ?? AppLocalizations.localizationsDelegates,
        supportedLocales: supportedLocales ?? AppLocalizations.supportedLocales,
        home: CupertinoPageScaffold(
          child: widget,
        ),
      ),
      duration: duration,
    );
  }

  /// Pumps a widget wrapped in a complete app context with custom configuration.
  ///
  /// This method provides maximum flexibility for widget testing by allowing
  /// you to specify both Material and Cupertino themes, custom media query data,
  /// and other app-level configuration.
  ///
  /// Use this for testing widgets that:
  /// - Need specific MediaQuery configuration
  /// - Require custom theme configuration
  /// - Need both Material and Cupertino design support
  /// - Require specific localization setup
  ///
  /// Example:
  /// ```dart
  /// testWidgets('my app widget test', (tester) async {
  ///   await tester.pumpWidgetWithApp(
  ///     const MyWidget(),
  ///     mediaQueryData: const MediaQueryData(
  ///       size: Size(400, 800),
  ///       devicePixelRatio: 2.0,
  ///     ),
  ///     materialTheme: ThemeData.light(),
  ///     cupertinoTheme: const CupertinoThemeData(
  ///       brightness: Brightness.light,
  ///     ),
  ///   );
  ///
  ///   expect(find.byType(MyWidget), findsOneWidget);
  /// });
  /// ```
  Future<void> pumpWidgetWithApp(
    Widget widget, {
    MediaQueryData? mediaQueryData,
    ThemeData? materialTheme,
    CupertinoThemeData? cupertinoTheme,
    Locale? locale,
    List<Locale>? supportedLocales,
    List<LocalizationsDelegate<dynamic>>? localizationsDelegates,
    TextDirection textDirection = TextDirection.ltr,
    Duration? duration,
  }) {
    // Default MediaQuery data for testing
    const defaultMediaQuery = MediaQueryData(
      size: Size(800, 600),
      textScaler: TextScaler.linear(1),
    );

    var wrappedWidget = widget;

    // Wrap with MediaQuery if custom data is provided
    if (mediaQueryData != null) {
      wrappedWidget = MediaQuery(
        data: mediaQueryData,
        child: wrappedWidget,
      );
    }

    // Wrap with themes if provided
    if (materialTheme != null) {
      wrappedWidget = Theme(
        data: materialTheme,
        child: wrappedWidget,
      );
    }

    if (cupertinoTheme != null) {
      wrappedWidget = CupertinoTheme(
        data: cupertinoTheme,
        child: wrappedWidget,
      );
    }

    // Wrap with Directionality
    wrappedWidget = Directionality(
      textDirection: textDirection,
      child: wrappedWidget,
    );

    // Wrap with Localizations
    wrappedWidget = Localizations(
      locale: locale ?? const Locale('en', 'US'),
      delegates:
          localizationsDelegates ?? AppLocalizations.localizationsDelegates,
      child: wrappedWidget,
    );

    // Wrap with MediaQuery (using default if not provided)
    wrappedWidget = MediaQuery(
      data: mediaQueryData ?? defaultMediaQuery,
      child: wrappedWidget,
    );

    return pumpWidget(
      wrappedWidget,
      duration: duration,
    );
  }

  /// Pumps a widget with minimal context for simple widget testing.
  ///
  /// This method provides a lightweight wrapper that includes only the
  /// essential contexts needed for most widgets to render properly.
  ///
  /// Use this for testing widgets that:
  /// - Are simple and don't require complex app context
  /// - Need basic MediaQuery and Directionality
  /// - Don't use Material or Cupertino design components
  ///
  /// Example:
  /// ```dart
  /// testWidgets('my simple widget test', (tester) async {
  ///   await tester.pumpWidgetWithMinimalContext(
  ///     const Text('Hello World'),
  ///   );
  ///
  ///   expect(find.text('Hello World'), findsOneWidget);
  /// });
  /// ```
  Future<void> pumpWidgetWithMinimalContext(
    Widget widget, {
    MediaQueryData? mediaQueryData,
    TextDirection textDirection = TextDirection.ltr,
    Duration? duration,
  }) {
    const defaultMediaQuery = MediaQueryData(
      size: Size(800, 600),
      textScaler: TextScaler.linear(1),
    );

    return pumpWidget(
      MediaQuery(
        data: mediaQueryData ?? defaultMediaQuery,
        child: Directionality(
          textDirection: textDirection,
          child: widget,
        ),
      ),
      duration: duration,
    );
  }
}
