import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:mocktail/mocktail.dart';

/// Common mock registrations for frequently used types in tests.
/// 
/// This class provides centralized mock registrations to avoid repeating
/// `registerFallbackValue` calls across multiple test files.
/// 
/// Usage:
/// ```dart
/// void main() {
///   setUpAll(() {
///     MockHelpers.registerCommonFallbacks();
///   });
/// 
///   // Your tests here...
/// }
/// ```
class MockHelpers {
  /// Registers all common fallback values for mocktail.
  /// 
  /// This method should be called in `setUpAll` to ensure that all
  /// commonly used types have proper fallback values registered.
  static void registerCommonFallbacks() {
    registerDurationFallbacks();
    registerVideoCaptureConfigFallbacks();
    registerFileFallbacks();
    registerCommonTypeFallbacks();
  }

  /// Registers fallback values for [Duration] objects.
  /// 
  /// Provides common duration fallbacks used throughout the app:
  /// - Default duration (1 second)
  /// - Recording duration (9 seconds)
  /// - Countdown duration (3 seconds)
  /// - Face detection interval (300ms)
  static void registerDurationFallbacks() {
    registerFallbackValue(const Duration(seconds: 1));
    registerFallbackValue(const Duration(seconds: 9));
    registerFallbackValue(const Duration(seconds: 3));
    registerFallbackValue(const Duration(milliseconds: 300));
  }

  /// Registers fallback values for [VideoCaptureConfig] objects.
  /// 
  /// Provides default video capture configuration for testing.
  static void registerVideoCaptureConfigFallbacks() {
    registerFallbackValue(const VideoCaptureConfig());
    registerFallbackValue(
      const VideoCaptureConfig(
        recordingDuration: Duration(seconds: 5),
        countdownDuration: Duration(seconds: 2),
        minimumFaceCoverage: 70.0,
        videoQuality: VideoQuality.medium,
        enableAudio: false,
        cameraLens: CameraLens.back,
      ),
    );
  }

  /// Registers fallback values for [File] objects.
  /// 
  /// Provides common file fallbacks for testing file operations.
  static void registerFileFallbacks() {
    registerFallbackValue(File('test_video.mp4'));
    registerFallbackValue(File('test_audio.mp3'));
    registerFallbackValue(File('test_image.jpg'));
    registerFallbackValue(File('test_data.json'));
  }

  /// Registers fallback values for other commonly used types.
  /// 
  /// Includes fallbacks for basic types that are frequently mocked.
  static void registerCommonTypeFallbacks() {
    // String fallbacks
    registerFallbackValue('');
    registerFallbackValue('test_string');
    registerFallbackValue('test_id');
    registerFallbackValue('test_path');
    registerFallbackValue('test_url');
    
    // Numeric fallbacks
    registerFallbackValue(0);
    registerFallbackValue(1);
    registerFallbackValue(0.0);
    registerFallbackValue(1.0);
    
    // Boolean fallbacks
    registerFallbackValue(true);
    registerFallbackValue(false);
    
    // List fallbacks
    registerFallbackValue(<String>[]);
    registerFallbackValue(<int>[]);
    registerFallbackValue(<double>[]);
    
    // Map fallbacks
    registerFallbackValue(<String, dynamic>{});
    registerFallbackValue(<String, String>{});
    registerFallbackValue(<String, int>{});
  }

  /// Creates a mock [VideoCaptureConfig] with common default values.
  /// 
  /// This is useful for tests that need a [VideoCaptureConfig] instance
  /// but don't care about the specific values.
  /// 
  /// Returns a [VideoCaptureConfig] with:
  /// - Recording duration: 5 seconds
  /// - Countdown duration: 2 seconds
  /// - Face detection interval: 200ms
  /// - Minimum face coverage: 75%
  /// - Video quality: medium
  /// - Audio enabled: true
  /// - Camera lens: front
  static VideoCaptureConfig createMockVideoCaptureConfig() {
    return const VideoCaptureConfig(
      recordingDuration: Duration(seconds: 5),
      countdownDuration: Duration(seconds: 2),
      faceDetectionInterval: Duration(milliseconds: 200),
      minimumFaceCoverage: 75.0,
      videoQuality: VideoQuality.medium,
      enableAudio: true,
      cameraLens: CameraLens.front,
    );
  }

  /// Creates a mock [File] with the specified path.
  /// 
  /// This is useful for tests that need [File] instances with specific paths.
  /// 
  /// [path] The file path to use for the mock file.
  static File createMockFile(String path) {
    return File(path);
  }

  /// Creates a mock video [File] with a standard MP4 extension.
  /// 
  /// [filename] The filename without extension. Defaults to 'test_video'.
  static File createMockVideoFile([String filename = 'test_video']) {
    return File('$filename.mp4');
  }

  /// Creates a mock audio [File] with a standard MP3 extension.
  /// 
  /// [filename] The filename without extension. Defaults to 'test_audio'.
  static File createMockAudioFile([String filename = 'test_audio']) {
    return File('$filename.mp3');
  }

  /// Creates a mock image [File] with a standard JPG extension.
  /// 
  /// [filename] The filename without extension. Defaults to 'test_image'.
  static File createMockImageFile([String filename = 'test_image']) {
    return File('$filename.jpg');
  }

  /// Common duration values used throughout the app.
  /// 
  /// These can be used directly in tests without creating new instances.
  static const Duration shortDuration = Duration(milliseconds: 100);
  static const Duration mediumDuration = Duration(seconds: 1);
  static const Duration longDuration = Duration(seconds: 5);
  static const Duration recordingDuration = Duration(seconds: 9);
  static const Duration countdownDuration = Duration(seconds: 3);
  static const Duration faceDetectionInterval = Duration(milliseconds: 300);

  /// Common video quality values for testing.
  static const VideoQuality lowQuality = VideoQuality.low;
  static const VideoQuality mediumQuality = VideoQuality.medium;
  static const VideoQuality highQuality = VideoQuality.high;
  static const VideoQuality ultraHighQuality = VideoQuality.ultraHigh;

  /// Common camera lens values for testing.
  static const CameraLens frontCamera = CameraLens.front;
  static const CameraLens backCamera = CameraLens.back;
}
