import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('[ONBOARDING] OnboardingService', () {
    late OnboardingService service;

    setUp(() {
      service = OnboardingService();
    });

    group('[ONBOARDING] Action: Profile validation', () {
      test('[ONBOARDING] Action: Validates complete profile successfully', () {
        // Arrange
        final completeProfile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 180,
          weightKg: 75,
          smokingStatus: SmokingStatus.never,
          exerciseFrequency: 3,
          sleepHours: 8,
          stressLevel: 5,
          energyLevel: 7,
          moodRating: 8,
          isOnboardingComplete: true,
        );

        // Act
        final result = service.validateProfile(completeProfile);

        // Assert
        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
        expect(result.warnings, isEmpty);
      });

      test('[ONBOARDING] Action: Validates incomplete profile with errors', () {
        // Arrange
        const incompleteProfile = AsvsUserProfile();

        // Act
        final result = service.validateProfile(incompleteProfile);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors, isNotEmpty);
        expect(result.errors, contains('First name is required'));
        expect(result.errors, contains('Last name is required'));
        expect(result.errors, contains('Date of birth is required'));
      });

      test('[ONBOARDING] Action: Validates profile with invalid age', () {
        // Arrange
        final futureProfile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime.now().add(const Duration(days: 1)),
          gender: Gender.male,
        );

        // Act
        final result = service.validateProfile(futureProfile);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors, contains('User must be at least 13 years old'));
      });

      test('[ONBOARDING] Action: Validates profile with invalid measurements',
          () {
        // Arrange
        final invalidProfile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 0, // Invalid height
          weightKg: -10, // Invalid weight
        );

        // Act
        final result = service.validateProfile(invalidProfile);

        // Assert
        expect(result.isValid, isFalse);
        expect(
          result.errors,
          contains('Height must be between 50cm and 300cm'),
        );
        expect(
          result.errors,
          contains('Weight must be between 20kg and 500kg'),
        );
      });

      test('[ONBOARDING] Action: Validates profile with invalid ratings', () {
        // Arrange
        final invalidProfile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 180,
          weightKg: 75,
          smokingStatus: SmokingStatus.never,
          exerciseFrequency: -1, // Invalid
          sleepHours: 25, // Invalid
          stressLevel: 11, // Invalid
          energyLevel: -1, // Invalid
          moodRating: 11, // Invalid
        );

        // Act
        final result = service.validateProfile(invalidProfile);

        // Assert
        expect(result.isValid, isFalse);
        expect(
          result.errors,
          contains('Exercise frequency must be between 0 and 7 days per week'),
        );
        expect(
          result.errors,
          contains('Sleep hours must be between 1 and 24 hours'),
        );
        expect(
          result.errors,
          contains('Stress level must be between 1 and 10'),
        );
        expect(
          result.errors,
          contains('Energy level must be between 1 and 10'),
        );
        expect(
          result.errors,
          contains('Mood rating must be between 1 and 10'),
        );
      });
    });

    group('[ONBOARDING] Action: BMI calculations', () {
      test('[ONBOARDING] Action: Calculates BMI correctly for metric units',
          () {
        // Arrange
        const profile = AsvsUserProfile(
          heightCm: 180,
          weightKg: 75,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.bmi, closeTo(23.15, 0.01));
      });

      test('[ONBOARDING] Action: Calculates BMI correctly for imperial units',
          () {
        // Arrange
        const profile = AsvsUserProfile(
          heightCm: 180, // Will be converted from feet/inches
          weightKg: 75, // Will be converted from pounds
          unitPreference: UnitPreference.imperial,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.bmi, isNotNull);
        expect(result.bmi! > 0, isTrue);
      });

      test('[ONBOARDING] Action: Handles zero height gracefully', () {
        // Arrange
        const profile = AsvsUserProfile(
          heightCm: 0,
          weightKg: 75,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.bmi, equals(double.infinity));
      });

      test('[ONBOARDING] Action: Handles zero weight gracefully', () {
        // Arrange
        const profile = AsvsUserProfile(
          heightCm: 180,
          weightKg: 0,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.bmi, equals(0.0));
      });
    });

    group('[ONBOARDING] Action: Age calculations', () {
      test('[ONBOARDING] Action: Calculates age correctly', () {
        // Arrange
        final now = DateTime.now();
        final birthDate = DateTime(now.year - 25, now.month, now.day);
        final profile = AsvsUserProfile(
          dateOfBirth: birthDate,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.age, equals(25));
      });

      test('[ONBOARDING] Action: Handles null birth date', () {
        // Arrange
        const profile = AsvsUserProfile();

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.age, isNull);
      });

      test('[ONBOARDING] Action: Calculates age for leap year birth', () {
        // Arrange
        final leapYearBirth = DateTime(2000, 2, 29); // Leap year
        final profile = AsvsUserProfile(
          dateOfBirth: leapYearBirth,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.age, isNotNull);
        expect(result.age! >= 20, isTrue); // Should be at least 20 years old
      });
    });

    group('[ONBOARDING] Action: Profile completion logic', () {
      test('[ONBOARDING] Action: Identifies complete profile correctly', () {
        // Arrange
        final completeProfile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 180,
          weightKg: 75,
          smokingStatus: SmokingStatus.never,
          exerciseFrequency: 3,
          sleepHours: 8,
          stressLevel: 5,
          energyLevel: 7,
          moodRating: 8,
          isOnboardingComplete: true,
        );

        // Act
        final isComplete = service.isOnboardingComplete(completeProfile);

        // Assert
        expect(isComplete, isTrue);
      });

      test('[ONBOARDING] Action: Identifies incomplete profile correctly', () {
        // Arrange
        const incompleteProfile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
        );

        // Act
        final isComplete = service.isOnboardingComplete(incompleteProfile);

        // Assert
        expect(isComplete, isFalse);
      });

      test('[ONBOARDING] Action: Validates profile with errors', () {
        // Arrange
        final invalidProfile = AsvsUserProfile(
          firstName: '',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 500, // Invalid height
          weightKg: 75,
        );

        // Act
        final result = service.validateProfile(invalidProfile);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors, isNotEmpty);
        expect(result.errors, contains('First name is required'));
        expect(
          result.errors,
          contains('Height must be between 50cm and 300cm'),
        );
      });

      test('[ONBOARDING] Action: Creates profile with calculations', () {
        // Arrange
        final profile = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 180,
          weightKg: 75,
          smokingStatus: SmokingStatus.never,
          exerciseFrequency: 3,
          sleepHours: 8,
          stressLevel: 5,
          energyLevel: 7,
          moodRating: 8,
        );

        // Act
        final result = service.createProfileWithCalculations(profile);

        // Assert
        expect(result.bmi, isNotNull);
        expect(result.createdAt, isNotNull);
        expect(result.updatedAt, isNotNull);
        expect(result.isOnboardingComplete, isTrue);
      });

      test('[ONBOARDING] Action: Assesses health risk correctly', () {
        // Arrange
        final profile = AsvsUserProfile(
          dateOfBirth: DateTime(1950), // Age > 65
          heightCm: 180,
          weightKg: 100, // BMI > 30
          smokingStatus: SmokingStatus.current,
          exerciseFrequency: 1, // < 2 days
          sleepHours: 5, // < 6 hours
          stressLevel: 8, // >= 7
        );

        // Act
        final assessment = service.assessHealthRisk(profile);

        // Assert
        expect(assessment.riskLevel, isNotNull);
        expect(assessment.riskScore, greaterThan(0));
        expect(assessment.riskFactors, isNotEmpty);
      });
    });

    group('[ONBOARDING] Action: Next step determination', () {
      test('[ONBOARDING] Action: Returns personalInfo for empty profile', () {
        // Arrange
        const emptyProfile = AsvsUserProfile();

        // Act
        final nextStep = service.getNextStep(emptyProfile);

        // Assert
        expect(nextStep, equals(OnboardingStep.personalInfo));
      });

      test(
          '[ONBOARDING] Action: Returns physicalMeasurements after personal info',
          () {
        // Arrange
        final profileWithPersonalInfo = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
        );

        // Act
        final nextStep = service.getNextStep(profileWithPersonalInfo);

        // Assert
        expect(nextStep, equals(OnboardingStep.physicalMeasurements));
      });

      test('[ONBOARDING] Action: Returns healthInfo after measurements', () {
        // Arrange
        final profileWithMeasurements = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 180,
          weightKg: 75,
        );

        // Act
        final nextStep = service.getNextStep(profileWithMeasurements);

        // Assert
        expect(nextStep, equals(OnboardingStep.healthInfo));
      });

      test('[ONBOARDING] Action: Returns asvsRatings for complete health info',
          () {
        // Arrange
        final profileWithHealthInfo = AsvsUserProfile(
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: DateTime(1990),
          gender: Gender.male,
          heightCm: 180,
          weightKg: 75,
          smokingStatus: SmokingStatus.never,
          exerciseFrequency: 3,
          sleepHours: 8,
        );

        // Act
        final nextStep = service.getNextStep(profileWithHealthInfo);

        // Assert
        expect(nextStep, equals(OnboardingStep.asvsRatings));
      });
    });
  });
}
