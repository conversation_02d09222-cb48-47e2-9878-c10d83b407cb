import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockAsvsProfileRepository extends Mock implements AsvsProfileRepository {}

class MockOnboardingService extends Mock implements OnboardingService {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(const AsvsUserProfile());
    registerFallbackValue(OnboardingStep.personalInfo);
    registerFallbackValue(<String, dynamic>{});
    registerFallbackValue(
      const ValidationResult(
        isValid: true,
        errors: [],
        warnings: [],
      ),
    );
  });

  group('[ONBOARDING] OnboardingBloc', () {
    late OnboardingBloc bloc;
    late MockAsvsProfileRepository mockRepository;
    late MockOnboardingService mockOnboardingService;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
      isOnboardingComplete: true,
    );

    setUp(() {
      mockRepository = MockAsvsProfileRepository();
      mockOnboardingService = MockOnboardingService();

      // Default mock behaviors
      when(() => mockRepository.initialize()).thenAnswer((_) async {});
      when(() => mockRepository.loadProfile()).thenAnswer((_) async => null);
      when(() => mockRepository.saveProfile(any())).thenAnswer((_) async {});
      when(() => mockOnboardingService.validateProfile(any())).thenReturn(
        const ValidationResult(
          isValid: true,
          errors: [],
          warnings: [],
        ),
      );
      when(() => mockOnboardingService.isOnboardingComplete(any()))
          .thenReturn(false);
      when(() => mockOnboardingService.getNextStep(any()))
          .thenReturn(OnboardingStep.personalInfo);
      when(() => mockOnboardingService.createProfileWithCalculations(any()))
          .thenAnswer(
        (invocation) => invocation.positionalArguments[0] as AsvsUserProfile,
      );

      bloc = OnboardingBloc(
        repository: mockRepository,
        onboardingService: mockOnboardingService,
      );
    });

    tearDown(() {
      bloc.close();
    });

    test('[ONBOARDING] Action: Initial state verification', () {
      expect(bloc.state, isA<OnboardingInitial>());
    });

    group('[ONBOARDING] Action: Start onboarding', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingLoading, OnboardingInProgress] when starting fresh '
        'onboarding',
        build: () => bloc,
        act: (bloc) => bloc.add(const StartOnboarding()),
        expect: () => [
          isA<OnboardingLoading>(),
          isA<OnboardingInProgress>()
              .having(
                (s) => s.currentStep,
                'currentStep',
                OnboardingStep.personalInfo,
              )
              .having((s) => s.stepIndex, 'stepIndex', 0)
              .having(
                (s) => s.totalSteps,
                'totalSteps',
                OnboardingStep.values.length - 1,
              ),
        ],
        verify: (_) {
          verify(() => mockRepository.initialize()).called(1);
          verify(() => mockOnboardingService.getNextStep(any())).called(1);
        },
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingLoading, OnboardingCompleted] when profile already '
        'complete',
        build: () {
          // Mock loading a completed profile
          when(() => mockRepository.loadProfile()).thenAnswer(
            (_) async => testProfile.copyWith(isOnboardingComplete: true),
          );
          return bloc;
        },
        act: (bloc) => bloc.add(const StartOnboarding()),
        expect: () => [
          isA<OnboardingLoading>(),
          isA<OnboardingCompleted>().having(
            (s) => s.message,
            'message',
            'Welcome back! Your profile is already set up.',
          ),
        ],
      );
    });

    group('[ONBOARDING] Action: Load existing profile', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingLoading, OnboardingInProgress] when profile exists '
        'but incomplete',
        build: () {
          when(() => mockRepository.loadProfile()).thenAnswer(
            (_) async => testProfile.copyWith(
              isOnboardingComplete: false,
            ),
          );
          when(() => mockOnboardingService.getNextStep(any()))
              .thenReturn(OnboardingStep.personalInfo);
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadExistingProfile()),
        expect: () => [
          isA<OnboardingLoading>(),
          isA<OnboardingInProgress>()
              .having(
                (s) => s.currentStep,
                'currentStep',
                OnboardingStep.personalInfo,
              )
              .having((s) => s.profile.firstName, 'profile.firstName', 'John'),
        ],
        verify: (_) {
          verify(() => mockRepository.loadProfile()).called(1);
          verify(() => mockOnboardingService.getNextStep(any())).called(1);
        },
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingLoading, OnboardingCompleted] when profile exists '
        'and complete',
        build: () {
          when(() => mockRepository.loadProfile())
              .thenAnswer((_) async => testProfile);
          when(() => mockOnboardingService.getNextStep(any()))
              .thenReturn(OnboardingStep.complete);
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadExistingProfile()),
        expect: () => [
          isA<OnboardingLoading>(),
          isA<OnboardingCompleted>()
              .having((s) => s.profile.firstName, 'profile.firstName', 'John')
              .having(
                (s) => s.message,
                'message',
                'Profile loaded successfully',
              ),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingLoading, OnboardingInitial] when no profile exists',
        build: () {
          when(() => mockRepository.loadProfile())
              .thenAnswer((_) async => null);
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadExistingProfile()),
        expect: () => [
          isA<OnboardingLoading>(),
          isA<OnboardingInitial>(),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingLoading, OnboardingError] when loading fails',
        build: () {
          when(() => mockRepository.loadProfile())
              .thenThrow(Exception('Database error'));
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadExistingProfile()),
        expect: () => [
          isA<OnboardingLoading>(),
          isA<OnboardingError>().having(
            (s) => s.error,
            'error',
            contains('Failed to load profile'),
          ),
        ],
      );
    });

    group('[ONBOARDING] Action: Update profile', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'updates profile data correctly',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          stepIndex: 1,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(
          const UpdateProfile({
            'firstName': 'Jane',
            'lastName': 'Smith',
            'gender': Gender.female,
          }),
        ),
        expect: () => [
          isA<OnboardingInProgress>()
              .having((s) => s.profile.firstName, 'profile.firstName', 'Jane')
              .having((s) => s.profile.lastName, 'profile.lastName', 'Smith')
              .having((s) => s.profile.gender, 'profile.gender', Gender.female),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'handles invalid update data gracefully',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          stepIndex: 1,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(
          const UpdateProfile({
            'invalidField': 'value',
          }),
        ),
        expect: () => [
          isA<OnboardingInProgress>(),
        ],
      );
    });

    group('[ONBOARDING] Action: Navigation', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'moves to next step when NextStep event is added',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          stepIndex: 0,
          totalSteps: OnboardingStep.values.length - 1,
          profile: testProfile, // Profile with basic info completed
        ),
        act: (bloc) => bloc.add(const NextStep()),
        expect: () => [
          isA<OnboardingInProgress>()
              .having(
                (s) => s.currentStep,
                'currentStep',
                OnboardingStep.physicalMeasurements,
              )
              .having((s) => s.stepIndex, 'stepIndex', 1),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'completes onboarding when at last step',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.asvsRatings,
          stepIndex: 3,
          totalSteps: OnboardingStep.values.length - 1,
          profile: testProfile, // Complete profile
        ),
        act: (bloc) => bloc.add(const NextStep()),
        expect: () => [
          isA<OnboardingCompleting>(),
          isA<OnboardingCompleted>(),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'moves to previous step when PreviousStep event is added',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.physicalMeasurements,
          stepIndex: 1,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const PreviousStep()),
        expect: () => [
          isA<OnboardingInProgress>()
              .having(
                (s) => s.currentStep,
                'currentStep',
                OnboardingStep.personalInfo,
              )
              .having((s) => s.stepIndex, 'stepIndex', 0),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'goes to specific step when GoToStep event is added',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          stepIndex: 0,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const GoToStep(OnboardingStep.healthInfo)),
        expect: () => [
          isA<OnboardingInProgress>()
              .having(
                (s) => s.currentStep,
                'currentStep',
                OnboardingStep.healthInfo,
              )
              .having((s) => s.stepIndex, 'stepIndex', 2),
        ],
      );
    });

    group('[ONBOARDING] Action: Complete onboarding', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingCompleted] when completion succeeds',
        build: () {
          when(() => mockOnboardingService.validateProfile(any())).thenReturn(
            const ValidationResult(
              isValid: true,
              errors: [],
              warnings: [],
            ),
          );
          when(() => mockOnboardingService.createProfileWithCalculations(any()))
              .thenReturn(testProfile);
          when(() => mockRepository.saveProfile(any()))
              .thenAnswer((_) async {});
          return bloc;
        },
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.asvsRatings,
          profile: testProfile.copyWith(isOnboardingComplete: false),
          stepIndex: 3,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const CompleteOnboarding()),
        expect: () => [
          isA<OnboardingCompleting>(),
          isA<OnboardingCompleted>()
              .having(
                (s) => s.profile.isOnboardingComplete,
                'profile.isOnboardingComplete',
                true,
              )
              .having(
                (s) => s.message,
                'message',
                contains('successfully'),
              ),
        ],
        verify: (_) {
          verify(() => mockOnboardingService.validateProfile(any())).called(1);
          verify(
            () => mockOnboardingService.createProfileWithCalculations(any()),
          ).called(1);
          verify(() => mockRepository.saveProfile(any())).called(1);
        },
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingError] when validation fails',
        build: () {
          when(() => mockOnboardingService.validateProfile(any())).thenReturn(
            const ValidationResult(
              isValid: false,
              errors: ['Profile incomplete'],
              warnings: [],
            ),
          );
          return bloc;
        },
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.asvsRatings,
          stepIndex: 3,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const CompleteOnboarding()),
        expect: () => [
          isA<OnboardingCompleting>(),
          isA<OnboardingError>().having(
            (s) => s.error,
            'error',
            contains('Profile incomplete'),
          ),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingError] when save fails',
        build: () {
          when(() => mockOnboardingService.validateProfile(any())).thenReturn(
            const ValidationResult(
              isValid: true,
              errors: [],
              warnings: [],
            ),
          );
          when(() => mockOnboardingService.createProfileWithCalculations(any()))
              .thenReturn(testProfile);
          when(() => mockRepository.saveProfile(any()))
              .thenThrow(Exception('Save failed'));
          return bloc;
        },
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.asvsRatings,
          profile: testProfile.copyWith(isOnboardingComplete: false),
          stepIndex: 3,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const CompleteOnboarding()),
        expect: () => [
          isA<OnboardingCompleting>(),
          isA<OnboardingError>().having(
            (s) => s.error,
            'error',
            contains('Failed to complete onboarding'),
          ),
        ],
      );
    });

    group('[ONBOARDING] Action: Profile updates', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'handles profile updates with enum values correctly',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          stepIndex: 0,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(
          const UpdateProfile({
            'unitPreference': UnitPreference.imperial,
            'smokingStatus': SmokingStatus.never,
          }),
        ),
        expect: () => [
          isA<OnboardingInProgress>()
              .having(
                (s) => s.profile.unitPreference,
                'profile.unitPreference',
                UnitPreference.imperial,
              )
              .having(
                (s) => s.profile.smokingStatus,
                'profile.smokingStatus',
                SmokingStatus.never,
              ),
        ],
      );
    });

    group('[ONBOARDING] Action: Reset onboarding', () {
      blocTest<OnboardingBloc, OnboardingState>(
        'resets to initial state without clearing data',
        build: () => bloc,
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          profile: testProfile,
          stepIndex: 1,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const ResetOnboarding()),
        expect: () => [
          isA<OnboardingInitial>(),
        ],
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'resets and clears existing data when requested',
        build: () {
          when(() => mockRepository.deleteProfile()).thenAnswer((_) async {});
          return bloc;
        },
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          profile: testProfile,
          stepIndex: 1,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const ResetOnboarding(clearExistingData: true)),
        expect: () => [
          isA<OnboardingInitial>(),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteProfile()).called(1);
        },
      );

      blocTest<OnboardingBloc, OnboardingState>(
        'emits [OnboardingError] when delete fails',
        build: () {
          when(() => mockRepository.deleteProfile())
              .thenThrow(Exception('Delete failed'));
          return bloc;
        },
        seed: () => OnboardingInProgress(
          currentStep: OnboardingStep.personalInfo,
          profile: testProfile,
          stepIndex: 1,
          totalSteps: OnboardingStep.values.length - 1,
        ),
        act: (bloc) => bloc.add(const ResetOnboarding(clearExistingData: true)),
        expect: () => [
          isA<OnboardingError>().having(
            (s) => s.error,
            'error',
            contains('Failed to reset onboarding'),
          ),
        ],
      );
    });
  });
}
