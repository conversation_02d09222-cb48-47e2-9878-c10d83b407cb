import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/physical_measurements_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_input_widgets.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockOnboardingBloc extends MockBloc<OnboardingEvent, OnboardingState>
    implements OnboardingBloc {}

void main() {
  group('[ONBOARDING] PhysicalMeasurementsScreen Widget Tests', () {
    late MockOnboardingBloc mockOnboardingBloc;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
    );

    setUp(() {
      mockOnboardingBloc = MockOnboardingBloc();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => mockOnboardingBloc,
        child: const PhysicalMeasurementsScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(PhysicalMeasurementsScreen), findsOneWidget);
      });

      testWidgets('displays physical measurements title', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingStepHeader), findsOneWidget);
        expect(find.text('Physical Measurements'), findsOneWidget);
      });

      testWidgets('displays progress indicator with correct step',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(2));
      });
    });

    group('[ONBOARDING] Action: Unit preference selection', () {
      testWidgets('displays unit preference dropdown', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Measurement Units'), findsOneWidget);
        expect(
          find.byType(OnboardingDropdownField<UnitPreference>),
          findsOneWidget,
        );
        // Default profile has metric units, so should show metric
        expect(find.text('Metric (kg, cm)'), findsOneWidget);
      });

      testWidgets('shows metric units when metric is selected', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile:
                testProfile.copyWith(unitPreference: UnitPreference.metric),
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Height (cm)'), findsOneWidget);
        expect(find.text('Weight (kg)'), findsOneWidget);
      });

      testWidgets('shows imperial units when imperial is selected',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile.copyWith(
              unitPreference: UnitPreference.imperial,
            ),
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Height (inches)'), findsOneWidget);
        expect(find.text('Weight (lbs)'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Form fields', () {
      testWidgets('displays height input field', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Height (cm)'), findsOneWidget);
        expect(find.byType(TextField), findsWidgets);
      });

      testWidgets('displays weight input field', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Weight (kg)'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: User interactions', () {
      testWidgets('updates height when text is entered', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.enterText(
          find.byType(TextField).first,
          '175',
        );
        await tester.pump();

        // Assert
        expect(find.text('175'), findsOneWidget);
      });

      testWidgets('allows text input interaction', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(
          find.byType(TextField),
          findsNWidgets(2),
        ); // Height and weight fields
      });
    });

    group('[ONBOARDING] Action: BMI calculation', () {
      testWidgets('displays BMI when height and weight are provided',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('BMI'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Form validation', () {
      testWidgets('shows validation errors for invalid height', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.enterText(
          find.byType(TextField).first,
          '0',
        );
        await tester.pump();

        // Assert
        expect(find.textContaining('Height must be between'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Data persistence', () {
      testWidgets('pre-fills form with existing profile data', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.physicalMeasurements,
            profile: testProfile,
            stepIndex: 2,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('180.0'), findsOneWidget);
        expect(find.text('75.0'), findsOneWidget);
      });
    });
  });
}
