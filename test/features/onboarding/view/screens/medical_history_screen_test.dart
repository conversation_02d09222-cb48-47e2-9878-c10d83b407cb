import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';

import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/medical_history_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_list_input.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockOnboardingBloc extends MockBloc<OnboardingEvent, OnboardingState>
    implements OnboardingBloc {}

void main() {
  group('[ONBOARDING] MedicalHistoryScreen Widget Tests', () {
    late MockOnboardingBloc mockOnboardingBloc;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
    );

    setUp(() {
      mockOnboardingBloc = MockOnboardingBloc();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => mockOnboardingBloc,
        child: const MedicalHistoryScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(MedicalHistoryScreen), findsOneWidget);
      });

      testWidgets('displays medical history title', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingStepHeader), findsOneWidget);
        expect(find.text('Medical History'), findsOneWidget);
      });

      testWidgets('displays progress indicator with correct step',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(4));
      });
    });

    group('[ONBOARDING] Action: Chronic conditions', () {
      testWidgets('displays chronic conditions question', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Medical Conditions'), findsOneWidget);
        expect(find.textContaining('Common options'), findsWidgets);
      });

      testWidgets('displays predefined medical conditions options',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Hypertension'), findsWidgets);
        expect(find.textContaining('Diabetes'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Medications', () {
      testWidgets('displays medications question', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Current Medications'), findsOneWidget);
      });

      testWidgets('allows selection of predefined medications', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.tap(find.text('Aspirin').first);
        await tester.pump();

        // Assert
        expect(find.byType(OnboardingPredefinedListInput), findsWidgets);
        expect(find.text('Aspirin'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Additional details', () {
      testWidgets('shows additional fields when chronic conditions is yes',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Common options'), findsWidgets);
      });

      testWidgets('shows additional fields when medications is yes',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Common options'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: User interactions', () {
      testWidgets('displays navigation buttons', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(MedicalHistoryScreen), findsOneWidget);
        expect(find.text('Medical History'), findsOneWidget);
      });

      testWidgets('displays medical disclaimer', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Medical Disclaimer'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Form validation', () {
      testWidgets('displays form fields correctly', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Current Medications'), findsOneWidget);
        expect(find.text('Known Allergies'), findsOneWidget);
        expect(find.text('Medical Conditions'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Data persistence', () {
      testWidgets('pre-fills form with existing profile data', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        // Check that the predefined list inputs are present
        expect(find.byType(OnboardingPredefinedListInput), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Layout and accessibility', () {
      testWidgets('has proper accessibility labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('adapts to different screen sizes', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 4,
            totalSteps: 7,
          ),
        );

        // Test with small screen
        await tester.binding.setSurfaceSize(const Size(400, 600));
        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byType(MedicalHistoryScreen), findsOneWidget);

        // Test with large screen
        await tester.binding.setSurfaceSize(const Size(800, 1200));
        await tester.pump();

        expect(find.byType(MedicalHistoryScreen), findsOneWidget);
      });
    });
  });
}
