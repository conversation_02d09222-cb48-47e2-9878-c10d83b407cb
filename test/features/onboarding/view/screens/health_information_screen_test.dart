import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/health_information_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_input_widgets.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockOnboardingBloc extends MockBloc<OnboardingEvent, OnboardingState>
    implements OnboardingBloc {}

void main() {
  group('[ONBOARDING] HealthInformationScreen Widget Tests', () {
    late MockOnboardingBloc mockOnboardingBloc;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
    );

    setUp(() {
      mockOnboardingBloc = MockOnboardingBloc();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => mockOnboardingBloc,
        child: const HealthInformationScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(HealthInformationScreen), findsOneWidget);
      });

      testWidgets('displays health information title', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingStepHeader), findsOneWidget);
        expect(find.text('Health Information'), findsOneWidget);
      });

      testWidgets('displays progress indicator with correct step',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(3));
      });
    });

    group('[ONBOARDING] Action: Smoking status selection', () {
      testWidgets('displays smoking status dropdown', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Smoking Status'), findsOneWidget);
        expect(find.byType(OnboardingDropdownField<SmokingStatus>), findsOneWidget);
      });

      // TODO: Add dropdown interaction test once we figure out proper dropdown2 testing
      // The dropdown displays correctly (verified by previous test)
    });

    group('[ONBOARDING] Action: Exercise frequency', () {
      testWidgets('displays exercise frequency field', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Exercise Frequency (days per week)'), findsOneWidget);
        expect(find.textContaining('days per week'), findsAtLeastNWidgets(1));
      });

      testWidgets('allows exercise frequency input', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        
        // Find the text field by its hint text since the exercise frequency field should have the hint
        final exerciseField = find.byType(TextField).first;
        await tester.enterText(exerciseField, '5');
        await tester.pump();

        // Assert
        expect(find.text('5'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Sleep hours', () {
      testWidgets('displays sleep hours field', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Average Sleep Hours (per night)'), findsOneWidget);
        expect(find.textContaining('hours per night'), findsOneWidget);
      });

      testWidgets('allows sleep hours input', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        
        // Find the sleep hours text field (second TextField on the screen)
        final sleepField = find.byType(TextField).last;
        await tester.enterText(sleepField, '7');
        await tester.pump();

        // Assert
        expect(find.text('7'), findsOneWidget);
      });
    });

    // Note: Navigation buttons are not part of this screen - they are in the parent onboarding flow

    group('[ONBOARDING] Action: Form validation', () {
      testWidgets('shows validation errors for invalid exercise frequency',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        final exerciseField = find.byType(TextField).first;
        await tester.enterText(exerciseField, '8');
        await tester.pump();

        // Assert - validation should happen automatically
        expect(find.textContaining('between 0 and 7'), findsAtLeastNWidgets(1));
      });

      testWidgets('shows validation errors for invalid sleep hours',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        final sleepField = find.byType(TextField).last;
        await tester.enterText(sleepField, '25');
        await tester.pump();

        // Assert - validation should happen automatically
        expect(find.textContaining('between 4 and 12'), findsAtLeastNWidgets(1));
      });
    });

    group('[ONBOARDING] Action: Data persistence', () {
      testWidgets('pre-fills form with existing profile data', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('3'), findsOneWidget); // Exercise frequency
        expect(find.text('8'), findsOneWidget); // Sleep hours
      });
    });

    group('[ONBOARDING] Action: Layout and accessibility', () {
      testWidgets('has proper accessibility labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.healthInfo,
            profile: testProfile,
            stepIndex: 3,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(Semantics), findsWidgets);
      });
    });
  });
}
