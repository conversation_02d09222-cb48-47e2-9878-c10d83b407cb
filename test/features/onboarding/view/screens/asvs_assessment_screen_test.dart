import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';

import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/asvs_assessment_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockOnboardingBloc extends MockBloc<OnboardingEvent, OnboardingState>
    implements OnboardingBloc {}

void main() {
  group('[ONBOARDING] AsvsAssessmentScreen Widget Tests', () {
    late MockOnboardingBloc mockOnboardingBloc;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
    );

    setUp(() {
      mockOnboardingBloc = MockOnboardingBloc();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => mockOnboardingBloc,
        child: const AsvsAssessmentScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(AsvsAssessmentScreen), findsOneWidget);
      });

      testWidgets('displays ASVS assessment title', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingStepHeader), findsOneWidget);
        expect(find.text('ASVS Assessment'), findsOneWidget);
      });

      testWidgets('displays progress indicator with correct step',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(5));
      });
    });

    group('[ONBOARDING] Action: Stress level assessment', () {
      testWidgets('displays stress level slider', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Current Stress Level'), findsOneWidget);
        expect(find.byType(Slider), findsWidgets);
      });

      testWidgets('shows stress level scale labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('1'), findsWidgets);
        expect(find.text('10'), findsWidgets);
      });

      testWidgets('allows stress level adjustment', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());
        final slider = find.byType(Slider).first;
        await tester.drag(slider, const Offset(50, 0));
        await tester.pump();

        // Assert
        expect(find.byType(Slider), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Energy level assessment', () {
      testWidgets('displays energy level slider', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Current Energy Level'), findsOneWidget);
      });

      testWidgets('shows energy level scale labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('1'), findsWidgets);
        expect(find.text('10'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Mood rating assessment', () {
      testWidgets('displays mood rating slider', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Current Mood Rating'), findsOneWidget);
      });

      testWidgets('shows mood rating scale labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('1'), findsWidgets);
        expect(find.text('10'), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Assessment instructions', () {
      testWidgets('displays assessment instructions', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('About ASVS'), findsWidgets);
        expect(
          find.textContaining('Automated Subjective Vitality Scale'),
          findsWidgets,
        );
      });
    });

    group('[ONBOARDING] Action: User interactions', () {
      testWidgets('allows slider interaction', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(
          find.byType(Slider),
          findsNWidgets(3),
        ); // 3 sliders for stress, energy, mood
      });
    });

    group('[ONBOARDING] Action: Data persistence', () {
      testWidgets('pre-fills sliders with existing profile data',
          (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('5'), findsWidgets); // Stress level
        expect(find.text('7'), findsWidgets); // Energy level
        expect(find.text('8'), findsWidgets); // Mood rating
      });
    });

    group('[ONBOARDING] Action: Layout and accessibility', () {
      testWidgets('has proper accessibility labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('adapts to different screen sizes', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingInProgress(
            currentStep: OnboardingStep.asvsRatings,
            profile: testProfile,
            stepIndex: 5,
            totalSteps: 7,
          ),
        );

        // Test with small screen
        await tester.binding.setSurfaceSize(const Size(400, 600));
        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byType(AsvsAssessmentScreen), findsOneWidget);

        // Test with large screen
        await tester.binding.setSurfaceSize(const Size(800, 1200));
        await tester.pump();

        expect(find.byType(AsvsAssessmentScreen), findsOneWidget);
      });
    });
  });
}
