import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/welcome_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockAsvsProfileRepository extends Mock implements AsvsProfileRepository {}

class MockOnboardingService extends Mock implements OnboardingService {}

void main() {
  group('[ONBOARDING] WelcomeScreen Widget Tests', () {
    late OnboardingBloc onboardingBloc;
    late MockAsvsProfileRepository mockRepository;
    late MockOnboardingService mockOnboardingService;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
    );

    setUp(() {
      mockRepository = MockAsvsProfileRepository();
      mockOnboardingService = MockOnboardingService();

      // Set up fallback values
      registerFallbackValue(testProfile);
      registerFallbackValue(const StartOnboarding());

      // Set up default mock behavior
      when(() => mockRepository.initialize()).thenAnswer((_) async {});
      when(() => mockRepository.loadProfile())
          .thenAnswer((_) async => testProfile);
      when(() => mockRepository.saveProfile(any<AsvsUserProfile>()))
          .thenAnswer((_) async {});
      when(() => mockOnboardingService.validateProfile(any<AsvsUserProfile>()))
          .thenReturn(
        const ValidationResult(
          isValid: true,
          errors: [],
          warnings: [],
        ),
      );
      when(
        () =>
            mockOnboardingService.isOnboardingComplete(any<AsvsUserProfile>()),
      ).thenReturn(false);
      when(() => mockOnboardingService.getNextStep(any<AsvsUserProfile>()))
          .thenReturn(OnboardingStep.personalInfo);
      when(
        () => mockOnboardingService
            .createProfileWithCalculations(any<AsvsUserProfile>()),
      ).thenAnswer(
        (invocation) => invocation.positionalArguments[0] as AsvsUserProfile,
      );

      onboardingBloc = OnboardingBloc(
        repository: mockRepository,
        onboardingService: mockOnboardingService,
      )..add(const StartOnboarding());
    });

    tearDown(() {
      onboardingBloc.close();
    });

    setUpAll(() async {
      // Set a larger screen size to prevent overflow in tests
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => onboardingBloc,
        child: const WelcomeScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Act & Assert - Just verify the widget can be created
        expect(createWidgetUnderTest, returnsNormally);
      });

      testWidgets('displays welcome title and subtitle', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Welcome to ASVS'), findsOneWidget);
        expect(
          find.text('Automated Subjective Vitality Scale'),
          findsOneWidget,
        );
      });

      testWidgets('displays description text', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(
          find.textContaining(
            "We'll collect some information about your health and vitality",
          ),
          findsOneWidget,
        );
      });

      testWidgets('displays progress indicator', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: State handling', () {
      testWidgets('shows correct step index for initial state', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(0));
      });

      testWidgets('shows correct step index for in progress state',
          (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(0));
      });
    });

    group('[ONBOARDING] Action: Layout and accessibility', () {
      testWidgets('has proper accessibility labels', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(Semantics), findsWidgets);
      });
    });

    group('[ONBOARDING] Action: Privacy notice', () {
      testWidgets('displays privacy notice', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Privacy Notice'), findsOneWidget);
        expect(
          find.textContaining('Your health information is confidential'),
          findsOneWidget,
        );
        expect(find.text('Read our Privacy Policy'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Features list', () {
      testWidgets('displays features list', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Health Assessment'), findsOneWidget);
        expect(find.text('Personalized Insights'), findsOneWidget);
        expect(find.text('Secure & Private'), findsOneWidget);
      });
    });
  });
}
