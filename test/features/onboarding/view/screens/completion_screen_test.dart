import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';

import 'package:bloomg_flutter/features/onboarding/view/screens/completion_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockOnboardingBloc extends MockBloc<OnboardingEvent, OnboardingState>
    implements OnboardingBloc {}

void main() {
  group('[ONBOARDING] CompletionScreen Widget Tests', () {
    late MockOnboardingBloc mockOnboardingBloc;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      bmi: 23.1, // Calculated: 75 / (1.8 * 1.8) = 23.15
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
      isOnboardingComplete: true,
    );

    setUp(() {
      mockOnboardingBloc = MockOnboardingBloc();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => mockOnboardingBloc,
        child: const CompletionScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(CompletionScreen), findsOneWidget);
      });

      testWidgets('displays completion title', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingStepHeader), findsOneWidget);
        expect(find.text('Profile Complete!'), findsOneWidget);
      });

      testWidgets('displays progress indicator at 100%', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(progressIndicator.currentStep, equals(6));
      });
    });

    group('[ONBOARDING] Action: Success message', () {
      testWidgets('displays congratulations message', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Congratulations'), findsOneWidget);
        expect(find.textContaining('successfully'), findsOneWidget);
      });

      testWidgets('displays personalized welcome message', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('John'), findsOneWidget);
        expect(find.textContaining('personalized'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Profile summary', () {
      testWidgets('displays profile summary card', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Profile Summary'), findsOneWidget);
        expect(find.textContaining('Personal Information'), findsOneWidget);
      });

      testWidgets('shows key profile information', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('John'), findsOneWidget);
        expect(find.textContaining('Doe'), findsOneWidget);
        expect(find.textContaining('180'), findsOneWidget); // Height
        expect(find.textContaining('75'), findsOneWidget); // Weight
      });

      testWidgets('displays BMI calculation', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('BMI: 23.1'), findsOneWidget);
      });

      testWidgets('shows ASVS scores', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining('Stress'), findsOneWidget);
        expect(find.textContaining('Energy'), findsOneWidget);
        expect(find.textContaining('Mood'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Next steps', () {
      testWidgets('displays next steps information', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.textContaining("What's Next?"), findsOneWidget);
        expect(find.textContaining('Personalized Insights'), findsOneWidget);
      });

      testWidgets('shows feature highlights', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Personalized Insights'), findsOneWidget);
        expect(find.text('Progress Tracking'), findsOneWidget);
        expect(find.text('Face Verification'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: User interactions', () {
      testWidgets('displays completion screen content', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Profile Complete!'), findsOneWidget);
        expect(find.text('Congratulations!'), findsOneWidget);
      });

      testWidgets('displays profile summary sections', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Profile Summary'), findsOneWidget);
        expect(find.text('Personal Information'), findsOneWidget);
        expect(find.text('Physical Measurements'), findsOneWidget);
      });

      testWidgets('displays health information section', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.text('Health Information'), findsOneWidget);
        expect(find.text('ASVS Assessment'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Celebration elements', () {
      testWidgets('displays success icon or animation', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byIcon(Icons.check_circle), findsWidgets);
      });

      testWidgets('shows progress indicator', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Layout and accessibility', () {
      testWidgets('has proper accessibility labels', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('adapts to different screen sizes', (tester) async {
        // Arrange
        when(() => mockOnboardingBloc.state).thenReturn(
          OnboardingCompleted(profile: testProfile),
        );

        // Test with small screen
        await tester.binding.setSurfaceSize(const Size(400, 600));
        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byType(CompletionScreen), findsOneWidget);

        // Test with large screen
        await tester.binding.setSurfaceSize(const Size(800, 1200));
        await tester.pump();

        expect(find.byType(CompletionScreen), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Data validation', () {
      testWidgets('handles incomplete profile gracefully', (tester) async {
        // Arrange
        const incompleteProfile = AsvsUserProfile(
          firstName: 'John',
          isOnboardingComplete: true,
        );

        when(() => mockOnboardingBloc.state).thenReturn(
          const OnboardingCompleted(profile: incompleteProfile),
        );

        // Act
        await tester.pumpApp(createWidgetUnderTest());

        // Assert
        expect(find.byType(CompletionScreen), findsOneWidget);
        // Note: Incomplete profile only has firstName, so full name won't be
        // displayed
      });
    });
  });
}
