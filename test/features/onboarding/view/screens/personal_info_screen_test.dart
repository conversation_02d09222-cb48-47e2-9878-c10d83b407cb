import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/personal_info_screen.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/helpers.dart';

// Mock classes
class MockAsvsProfileRepository extends Mock implements AsvsProfileRepository {}

class MockOnboardingService extends Mock implements OnboardingService {}

void main() {
  group('[ONBOARDING] PersonalInfoScreen Widget Tests', () {
    late OnboardingBloc onboardingBloc;
    late MockAsvsProfileRepository mockRepository;
    late MockOnboardingService mockOnboardingService;

    // Test data
    final testProfile = AsvsUserProfile(
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: DateTime(1990),
      gender: Gender.male,
      heightCm: 180,
      weightKg: 75,
      smokingStatus: SmokingStatus.never,
      exerciseFrequency: 3,
      sleepHours: 8,
      stressLevel: 5,
      energyLevel: 7,
      moodRating: 8,
    );

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(const AsvsUserProfile());
      registerFallbackValue(OnboardingStep.personalInfo);
      registerFallbackValue(<String, dynamic>{});
      registerFallbackValue(
        const ValidationResult(
          isValid: true,
          errors: [],
          warnings: [],
        ),
      );
    });

    setUp(() {
      mockRepository = MockAsvsProfileRepository();
      mockOnboardingService = MockOnboardingService();

      // Default mock behaviors
      when(() => mockRepository.initialize()).thenAnswer((_) async {});
      when(() => mockRepository.loadProfile())
          .thenAnswer((_) async => testProfile);
      when(() => mockRepository.saveProfile(any())).thenAnswer((_) async {});
      when(() => mockOnboardingService.validateProfile(any())).thenReturn(
        const ValidationResult(
          isValid: true,
          errors: [],
          warnings: [],
        ),
      );
      when(() => mockOnboardingService.isOnboardingComplete(any()))
          .thenReturn(false);
      when(() => mockOnboardingService.getNextStep(any()))
          .thenReturn(OnboardingStep.personalInfo);
      when(() => mockOnboardingService.createProfileWithCalculations(any()))
          .thenAnswer(
        (invocation) => invocation.positionalArguments[0] as AsvsUserProfile,
      );

      onboardingBloc = OnboardingBloc(
        repository: mockRepository,
        onboardingService: mockOnboardingService,
      )..add(const StartOnboarding());
    });

    tearDown(() {
      onboardingBloc.close();
    });

    Widget createWidgetUnderTest() {
      return BlocProvider<OnboardingBloc>(
        create: (context) => onboardingBloc,
        child: const PersonalInfoScreen(),
      );
    }

    group('[ONBOARDING] Action: Basic rendering', () {
      testWidgets('renders without crashing', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.byType(PersonalInfoScreen), findsOneWidget);
      });

      testWidgets('displays personal info title', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.byType(OnboardingStepHeader), findsOneWidget);
        expect(find.text('Personal Information'), findsOneWidget);
      });

      testWidgets('displays progress indicator with correct step',
          (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.byType(OnboardingProgressIndicator), findsOneWidget);
        final progressIndicator = tester.widget<OnboardingProgressIndicator>(
          find.byType(OnboardingProgressIndicator),
        );
        expect(
          progressIndicator.currentStep,
          equals(0),
        ); // personalInfo is step 0
      });
    });

    group('[ONBOARDING] Action: Form fields', () {
      testWidgets('displays first name text field', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.text('First Name'), findsOneWidget);
        expect(find.byType(TextField), findsWidgets);
      });

      testWidgets('displays last name text field', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.text('Last Name'), findsOneWidget);
      });

      testWidgets('displays date of birth field', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.text('Date of Birth'), findsOneWidget);
      });

      testWidgets('displays gender selection', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.text('Gender'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: User interactions', () {
      testWidgets('updates first name when text is entered', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state
        await tester.enterText(
          find.byType(TextField).first,
          'Jane',
        );
        await tester.pump();

        // Assert
        expect(find.text('Jane'), findsOneWidget);
      });

      testWidgets('displays gender selection field', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.text('Gender'), findsOneWidget);
        // When using testProfile, gender field shows "Male" instead of hint
        expect(find.text('Male'), findsOneWidget);
      });

      testWidgets('updates profile when first name is changed', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state
        await tester.enterText(find.byType(TextField).first, 'John');
        await tester.pump();

        // Assert
        // The bloc should have received an UpdateProfile event
        // We can't easily verify this with a real bloc, so we'll just check
        // the UI updated
        expect(find.text('John'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Form validation', () {
      testWidgets('shows validation errors for empty required fields',
          (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Enter some text and then clear it to trigger validation
        await tester.enterText(find.byType(TextField).first, 'test');
        await tester.pump();
        await tester.enterText(find.byType(TextField).first, '');
        await tester.pump();

        // Assert
        // First name field shows validation error
        expect(find.text('First name is required'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Data persistence', () {
      testWidgets('pre-fills form with existing profile data', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.text('John'), findsOneWidget);
        expect(find.text('Doe'), findsOneWidget);
      });
    });

    group('[ONBOARDING] Action: Layout and accessibility', () {
      testWidgets('has proper accessibility labels', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('renders correctly on standard screen size', (tester) async {
        // Act
        await tester.pumpApp(createWidgetUnderTest());
        await tester.pump(); // Allow bloc to emit state

        // Assert
        expect(find.byType(PersonalInfoScreen), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
      });
    });
  });
}
