import 'package:bloomg_flutter/features/onboarding/models/models.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AsvsUserProfile', () {
    test('should create empty profile', () {
      const profile = AsvsUserProfile.empty();

      expect(profile.firstName, isNull);
      expect(profile.lastName, isNull);
      expect(profile.isOnboardingComplete, isFalse);
      expect(profile.unitPreference, UnitPreference.metric);
    });

    test('should calculate age correctly', () {
      final birthDate = DateTime(1990, 5, 15);
      final profile = AsvsUserProfile(dateOfBirth: birthDate);

      expect(profile.age, isNotNull);
      expect(profile.age, greaterThan(30));
    });

    test('should calculate BMI correctly', () {
      const profile = AsvsUserProfile(
        heightCm: 175,
        weightKg: 70,
      );

      final calculatedBmi = profile.calculatedBmi;
      expect(calculatedBmi, isNotNull);
      expect(calculatedBmi, closeTo(22.86, 0.01));
    });

    test('should determine BMI category correctly', () {
      const underweight = AsvsUserProfile(heightCm: 175, weightKg: 50);
      const normal = AsvsUserProfile(heightCm: 175, weightKg: 70);
      const overweight = AsvsUserProfile(heightCm: 175, weightKg: 85);
      const obese = AsvsUserProfile(heightCm: 175, weightKg: 100);

      expect(underweight.bmiCategory, 'Underweight');
      expect(normal.bmiCategory, 'Normal weight');
      expect(overweight.bmiCategory, 'Overweight');
      expect(obese.bmiCategory, 'Obese');
    });

    test('should calculate completion percentage correctly', () {
      const emptyProfile = AsvsUserProfile.empty();
      expect(emptyProfile.completionPercentage, 0);

      final partialProfile = AsvsUserProfile(
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: DateTime(1990),
        gender: Gender.male,
      );
      expect(partialProfile.completionPercentage, 50);
    });

    test('should create copy with updated fields', () {
      const original = AsvsUserProfile(firstName: 'John');
      final updated = original.copyWith(lastName: 'Doe');

      expect(updated.firstName, 'John');
      expect(updated.lastName, 'Doe');
      expect(original.lastName, isNull);
    });

    test('should have correct full name', () {
      const profile = AsvsUserProfile(
        firstName: 'John',
        lastName: 'Doe',
      );

      expect(profile.fullName, 'John Doe');
    });

    test('should handle null names in full name', () {
      const profileFirstOnly = AsvsUserProfile(firstName: 'John');
      const profileLastOnly = AsvsUserProfile(lastName: 'Doe');
      const profileEmpty = AsvsUserProfile.empty();

      expect(profileFirstOnly.fullName, 'John');
      expect(profileLastOnly.fullName, 'Doe');
      expect(profileEmpty.fullName, '');
    });
  });

  group('Gender', () {
    test('should have correct display names', () {
      expect(Gender.male.displayName, 'Male');
      expect(Gender.female.displayName, 'Female');
      expect(Gender.nonBinary.displayName, 'Non-binary');
      expect(Gender.preferNotToSay.displayName, 'Prefer not to say');
    });

    test('should have correct codes', () {
      expect(Gender.male.code, 'M');
      expect(Gender.female.code, 'F');
      expect(Gender.nonBinary.code, 'NB');
      expect(Gender.preferNotToSay.code, 'PNS');
    });
  });

  group('UnitPreference', () {
    test('should have correct display names', () {
      expect(UnitPreference.metric.displayName, 'Metric (kg, cm)');
      expect(UnitPreference.imperial.displayName, 'Imperial (lbs, ft/in)');
    });

    test('should have correct unit labels', () {
      expect(UnitPreference.metric.weightUnit, 'kg');
      expect(UnitPreference.imperial.weightUnit, 'lbs');
      expect(UnitPreference.metric.heightUnit, 'cm');
      expect(UnitPreference.imperial.heightUnit, 'ft/in');
    });
  });

  group('SmokingStatus', () {
    test('should have correct display names', () {
      expect(SmokingStatus.never.displayName, 'Never smoked');
      expect(SmokingStatus.former.displayName, 'Former smoker');
      expect(SmokingStatus.current.displayName, 'Current smoker');
      expect(SmokingStatus.occasional.displayName, 'Occasional smoker');
    });

    test('should have correct risk levels', () {
      expect(SmokingStatus.never.riskLevel, 0);
      expect(SmokingStatus.former.riskLevel, 1);
      expect(SmokingStatus.occasional.riskLevel, 2);
      expect(SmokingStatus.current.riskLevel, 3);
    });
  });
}
