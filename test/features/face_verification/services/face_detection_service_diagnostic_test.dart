import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('[FACE_VERIFICATION] FaceDetectionService Diagnostic Tests', () {
    late FaceDetectionService service;

    setUp(() {
      service = FaceDetectionService();
    });

    tearDown(() async {
      await service.dispose();
    });

    group('[FACE_VERIFICATION] Action: Enhanced diagnostic logging', () {
      test('getComprehensiveDiagnostics returns correct structure', () {
        final diagnostics = service.getComprehensiveDiagnostics();

        expect(diagnostics, isA<Map<String, dynamic>>());
        expect(diagnostics.containsKey('frameSequenceNumber'), isTrue);
        expect(diagnostics.containsKey('conversionStats'), isTrue);
        expect(diagnostics.containsKey('mlKitStats'), isTrue);
        expect(diagnostics.containsKey('performanceStats'), isTrue);
        expect(diagnostics.containsKey('recentFailures'), isTrue);
        expect(diagnostics.containsKey('processingTimes'), isTrue);

        // Check conversion stats structure
        final conversionStats =
            diagnostics['conversionStats'] as Map<String, dynamic>;
        expect(conversionStats.containsKey('successes'), isTrue);
        expect(conversionStats.containsKey('failures'), isTrue);
        expect(conversionStats.containsKey('total'), isTrue);
        expect(conversionStats.containsKey('successRate'), isTrue);

        // Check ML Kit stats structure
        final mlKitStats = diagnostics['mlKitStats'] as Map<String, dynamic>;
        expect(mlKitStats.containsKey('successes'), isTrue);
        expect(mlKitStats.containsKey('failures'), isTrue);
        expect(mlKitStats.containsKey('total'), isTrue);
        expect(mlKitStats.containsKey('successRate'), isTrue);

        // Check performance stats structure
        final performanceStats =
            diagnostics['performanceStats'] as Map<String, dynamic>;
        expect(
          performanceStats.containsKey('avgInputImageConversionTime'),
          isTrue,
        );
        expect(performanceStats.containsKey('avgMlKitProcessingTime'), isTrue);
        expect(performanceStats.containsKey('avgResultConversionTime'), isTrue);
        expect(performanceStats.containsKey('avgRecentProcessingTime'), isTrue);
        expect(performanceStats.containsKey('consecutiveFailures'), isTrue);
        expect(performanceStats.containsKey('maxConsecutiveFailures'), isTrue);
      });

      test('logPeriodicDiagnosticSummary executes without errors', () {
        expect(() => service.logPeriodicDiagnosticSummary(), returnsNormally);
      });

      test('analyzeFailurePatterns returns correct structure', () {
        final analysis = service.analyzeFailurePatterns();

        expect(analysis, isA<Map<String, dynamic>>());
        expect(analysis.containsKey('totalFailures'), isTrue);
        expect(analysis.containsKey('recentFailureCount'), isTrue);
        expect(analysis.containsKey('failureTypeCounts'), isTrue);
        expect(analysis.containsKey('mostCommonFailure'), isTrue);
        expect(analysis.containsKey('mostCommonFailureCount'), isTrue);
        expect(analysis.containsKey('consecutiveFailures'), isTrue);
        expect(analysis.containsKey('maxConsecutiveFailures'), isTrue);
        expect(analysis.containsKey('failureRate'), isTrue);

        // Verify data types
        expect(analysis['totalFailures'], isA<int>());
        expect(analysis['recentFailureCount'], isA<int>());
        expect(analysis['failureTypeCounts'], isA<Map<String, int>>());
        expect(analysis['consecutiveFailures'], isA<int>());
        expect(analysis['maxConsecutiveFailures'], isA<int>());
        expect(analysis['failureRate'], isA<double>());
      });

      test('getMlKitPerformanceMetrics returns correct structure', () {
        final metrics = service.getMlKitPerformanceMetrics();

        expect(metrics, isA<Map<String, dynamic>>());
        expect(metrics.containsKey('totalProcessed'), isTrue);
        expect(metrics.containsKey('successes'), isTrue);
        expect(metrics.containsKey('failures'), isTrue);
        expect(metrics.containsKey('successRate'), isTrue);
        expect(metrics.containsKey('avgProcessingTime'), isTrue);
        expect(metrics.containsKey('minProcessingTime'), isTrue);
        expect(metrics.containsKey('maxProcessingTime'), isTrue);
        expect(metrics.containsKey('recentProcessingTimes'), isTrue);

        // Verify data types
        expect(metrics['totalProcessed'], isA<int>());
        expect(metrics['successes'], isA<int>());
        expect(metrics['failures'], isA<int>());
        expect(metrics['successRate'], isA<double>());
        expect(metrics['avgProcessingTime'], isA<double>());
        expect(metrics['minProcessingTime'], isA<int>());
        expect(metrics['maxProcessingTime'], isA<int>());
        expect(metrics['recentProcessingTimes'], isA<List<int>>());
      });

      test('getConversionSuccessRateAnalysis returns correct structure', () {
        final analysis = service.getConversionSuccessRateAnalysis();

        expect(analysis, isA<Map<String, dynamic>>());
        expect(analysis.containsKey('totalConversions'), isTrue);
        expect(analysis.containsKey('successes'), isTrue);
        expect(analysis.containsKey('failures'), isTrue);
        expect(analysis.containsKey('successRate'), isTrue);
        expect(analysis.containsKey('avgConversionTime'), isTrue);
        expect(analysis.containsKey('recentConversionTimes'), isTrue);

        // Verify data types
        expect(analysis['totalConversions'], isA<int>());
        expect(analysis['successes'], isA<int>());
        expect(analysis['failures'], isA<int>());
        expect(analysis['successRate'], isA<double>());
        expect(analysis['avgConversionTime'], isA<double>());
        expect(analysis['recentConversionTimes'], isA<List<int>>());
      });

      test('diagnostic methods work with initial state', () {
        // Test that all diagnostic methods work correctly with no data
        final comprehensive = service.getComprehensiveDiagnostics();
        final failureAnalysis = service.analyzeFailurePatterns();
        final mlKitMetrics = service.getMlKitPerformanceMetrics();
        final conversionAnalysis = service.getConversionSuccessRateAnalysis();

        // All should return valid structures with zero values
        expect(comprehensive['frameSequenceNumber'], equals(0));
        expect(failureAnalysis['totalFailures'], equals(0));
        expect(mlKitMetrics['totalProcessed'], equals(0));
        expect(conversionAnalysis['totalConversions'], equals(0));

        // Success rates should be 0.0 when no data
        expect(
          (comprehensive['conversionStats'] as Map)['successRate'],
          equals(0.0),
        );
        expect(
          (comprehensive['mlKitStats'] as Map)['successRate'],
          equals(0.0),
        );
        expect(failureAnalysis['failureRate'], equals(0.0));
        expect(mlKitMetrics['successRate'], equals(0.0));
        expect(conversionAnalysis['successRate'], equals(0.0));
      });

      test('trackConversionSuccess updates diagnostic counters', () {
        final initialDiagnostics = service.getComprehensiveDiagnostics();
        final initialSuccesses =
            (initialDiagnostics['conversionStats'] as Map)['successes'] as int;

        service.trackConversionSuccess();

        final updatedDiagnostics = service.getComprehensiveDiagnostics();
        final updatedSuccesses =
            (updatedDiagnostics['conversionStats'] as Map)['successes'] as int;

        expect(updatedSuccesses, equals(initialSuccesses + 1));
      });

      test('trackConversionFailure updates diagnostic counters', () {
        final initialDiagnostics = service.getComprehensiveDiagnostics();
        final initialFailures =
            (initialDiagnostics['conversionStats'] as Map)['failures'] as int;

        service.trackConversionFailure();

        final updatedDiagnostics = service.getComprehensiveDiagnostics();
        final updatedFailures =
            (updatedDiagnostics['conversionStats'] as Map)['failures'] as int;

        expect(updatedFailures, equals(initialFailures + 1));
      });
    });
  });
}
