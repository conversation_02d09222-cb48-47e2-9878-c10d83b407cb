import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

// Mock classes
class MockLoggerService extends Mock implements LoggerService {}
class MockDirectory extends Mock implements Directory {}
class MockFile extends Mock implements File {}

// Test implementation of CameraService that allows mocking initialization
class TestCameraService extends CameraService {
  bool _shouldThrowOnInitialize = false;
  bool _shouldThrowOnDirectoryCreate = false;

  void setInitializationException(bool shouldThrow) {
    _shouldThrowOnInitialize = shouldThrow;
  }

  void setDirectoryException(bool shouldThrow) {
    _shouldThrowOnDirectoryCreate = shouldThrow;
  }

  @override
  Future<void> initialize() async {
    if (_shouldThrowOnInitialize) {
      throw Exception('Permission denied');
    }
    return super.initialize();
  }

  @override
  Future<void> startRecording({
    required String filePath,
    VideoCaptureConfig? config,
  }) async {
    if (!isInitialized) {
      throw StateError('Camera service not initialized');
    }

    if (isRecording) {
      throw StateError('Recording already in progress');
    }

    if (_shouldThrowOnDirectoryCreate) {
      throw const FileSystemException('Directory creation failed');
    }

    return super.startRecording(filePath: filePath, config: config);
  }
}

void main() {
  group('[FACE_VERIFICATION] CameraService', () {
    late TestCameraService cameraService;
    late MockLoggerService mockLogger;
    late MockDirectory mockDirectory;
    late MockFile mockFile;

    setUp(() {
      mockLogger = MockLoggerService();
      mockDirectory = MockDirectory();
      mockFile = MockFile();
      cameraService = TestCameraService();

      // Register fallback values
      registerFallbackValue(Directory(''));
    });

    group('[FACE_VERIFICATION] Action: Happy Path Flow', () {
      test('complete flow: initialize → startRecording → stopRecording', () async {
        const testFilePath = '/test/path/video.mp4';
        final stopwatch = Stopwatch();

        // 1. Initialize
        await cameraService.initialize();
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // 2. Start recording
        await cameraService.startRecording(filePath: testFilePath);
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));

        // 3. Stop recording and verify 500ms delay
        stopwatch.start();
        await cameraService.stopRecording();
        stopwatch.stop();

        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
        
        // Verify the 500ms grace delay
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
      });

      test('verifies internal flags during recording lifecycle', () async {
        const testFilePath = '/test/path/video.mp4';

        // Before initialization
        expect(cameraService.isInitialized, isFalse);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // After initialization
        await cameraService.initialize();
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);

        // During recording
        await cameraService.startRecording(filePath: testFilePath);
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));

        // After stopping
        await cameraService.stopRecording();
        expect(cameraService.isInitialized, isTrue);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Failure Path - Initialize', () {
      test('throws exception when permissions denied', () async {
        cameraService.setInitializationException(true);

        expect(
          () async => await cameraService.initialize(),
          throwsA(isA<Exception>()),
        );

        expect(cameraService.isInitialized, isFalse);
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });

      test('startRecording throws when not initialized', () async {
        expect(
          () async => await cameraService.startRecording(
            filePath: '/test/path/video.mp4',
          ),
          throwsA(isA<StateError>()),
        );
      });

      test('stopRecording throws when not initialized', () async {
        expect(
          () async => await cameraService.stopRecording(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('[FACE_VERIFICATION] Action: Failure Path - Recording States', () {
      test('startRecording throws when already recording', () async {
        const testFilePath = '/test/path/video.mp4';
        await cameraService.initialize();
        await cameraService.startRecording(filePath: testFilePath);

        expect(
          () async => await cameraService.startRecording(
            filePath: '/another/path/video.mp4',
          ),
          throwsA(isA<StateError>()),
        );

        // Verify state hasn't changed
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));
      });

      test('stopRecording throws when not recording', () async {
        await cameraService.initialize();

        expect(
          () async => await cameraService.stopRecording(),
          throwsA(isA<StateError>()),
        );

        // Verify state hasn't changed
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Failure Path - Directory Creation', () {
      test('throws FileSystemException when directory creation fails', () async {
        await cameraService.initialize();
        cameraService.setDirectoryException(true);

        expect(
          () async => await cameraService.startRecording(
            filePath: '/invalid/path/video.mp4',
          ),
          throwsA(isA<FileSystemException>()),
        );

        // Verify state remains clean after failure
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Configuration and State Management', () {
      test('startRecording with custom config', () async {
        const testFilePath = '/test/path/video.mp4';
        const config = VideoCaptureConfig(
          recordingDuration: Duration(seconds: 15),
          videoQuality: VideoQuality.high,
          enableAudio: false,
        );

        await cameraService.initialize();
        await cameraService.startRecording(
          filePath: testFilePath,
          config: config,
        );

        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));

        await cameraService.stopRecording();
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });

      test('getCameraConfig returns correct information', () async {
        const testFilePath = '/test/path/video.mp4';
        await cameraService.initialize();
        
        var config = cameraService.getCameraConfig();
        expect(config['isInitialized'], isTrue);
        expect(config['isRecording'], isFalse);
        expect(config['currentRecordingPath'], isNull);
        expect(config['supportedResolutions'], isNotEmpty);
        expect(config['currentLens'], equals('front'));

        await cameraService.startRecording(filePath: testFilePath);
        config = cameraService.getCameraConfig();
        expect(config['isRecording'], isTrue);
        expect(config['currentRecordingPath'], equals(testFilePath));

        await cameraService.stopRecording();
        config = cameraService.getCameraConfig();
        expect(config['isRecording'], isFalse);
        expect(config['currentRecordingPath'], isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Grace Period Verification', () {
      test('verifies 500ms grace delay after stopRecording', () async {
        const testFilePath = '/test/path/video.mp4';
        await cameraService.initialize();
        await cameraService.startRecording(filePath: testFilePath);

        final stopwatch = Stopwatch()..start();
        await cameraService.stopRecording();
        stopwatch.stop();

        // Verify the delay is approximately 500ms (allowing for some variance)
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
        expect(stopwatch.elapsedMilliseconds, lessThan(600)); // Upper bound for reasonable test execution
      });

      test('grace period does not affect state consistency', () async {
        const testFilePath = '/test/path/video.mp4';
        await cameraService.initialize();
        await cameraService.startRecording(filePath: testFilePath);

        // State should be updated immediately when stopRecording is called
        final stopFuture = cameraService.stopRecording();
        
        // These should be updated immediately, not after the grace period
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
        
        await stopFuture; // Wait for grace period to complete
        
        // State should remain consistent
        expect(cameraService.isRecording, isFalse);
        expect(cameraService.currentRecordingPath, isNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Error Recovery', () {
      test('service can be reinitialized after initialization failure', () async {
        cameraService.setInitializationException(true);
        
        // First initialization should fail
        expect(
          () async => await cameraService.initialize(),
          throwsA(isA<Exception>()),
        );
        expect(cameraService.isInitialized, isFalse);

        // Reset the exception and try again
        cameraService.setInitializationException(false);
        await cameraService.initialize();
        expect(cameraService.isInitialized, isTrue);
      });

      test('recording can be attempted after directory creation failure', () async {
        const testFilePath = '/test/path/video.mp4';
        await cameraService.initialize();
        cameraService.setDirectoryException(true);

        // First recording attempt should fail
        expect(
          () async => await cameraService.startRecording(filePath: testFilePath),
          throwsA(isA<FileSystemException>()),
        );
        expect(cameraService.isRecording, isFalse);

        // Reset the exception and try again
        cameraService.setDirectoryException(false);
        await cameraService.startRecording(filePath: testFilePath);
        expect(cameraService.isRecording, isTrue);
        expect(cameraService.currentRecordingPath, equals(testFilePath));
      });
    });
  });
}

