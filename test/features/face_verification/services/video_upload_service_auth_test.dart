import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_upload_service.dart';
import 'package:bloomg_flutter/features/video_gallery/services/firebase_video_metadata_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFirebaseStorage extends Mock implements FirebaseStorage {}

class MockReference extends Mock implements Reference {}

class MockUploadTask extends Mock implements UploadTask {}

class MockTaskSnapshot extends Mock implements TaskSnapshot {}

class MockUser extends Mock implements User {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirebaseVideoMetadataService extends Mock
    implements FirebaseVideoMetadataService {}

void main() {
  group('VideoUploadService Authorization Tests', () {
    late VideoUploadService videoUploadService;
    late MockFirebaseStorage mockStorage;
    late MockReference mockRef;
    late MockFirebaseVideoMetadataService mockMetadataService;

    setUp(() {
      mockStorage = MockFirebaseStorage();
      mockRef = MockReference();
      mockMetadataService = MockFirebaseVideoMetadataService();

      // Register fallback values
      registerFallbackValue(File(''));
      registerFallbackValue(SettableMetadata());

      // Inject the mocked Firebase Storage and metadata service
      videoUploadService = VideoUploadService(
        storage: mockStorage,
        metadataService: mockMetadataService,
      );
    });

    group('Authentication Error Handling', () {
      test('should provide detailed error message for unauthorized access',
          () async {
        // Arrange
        final tempDir =
            Directory.systemTemp.createTempSync('video_upload_test');
        final videoPath = '${tempDir.path}/video.mp4';
        const userId = 'test_user_123';
        const coverageStats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 90,
          framesWithValidCoverage: 80,
          averageCoverage: 0.85,
          minimumCoverage: 0.70,
          maximumCoverage: 0.95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: [],
          qualityScore: 85,
        );

        // Create a test video file
        final testFile = File(videoPath);
        await testFile.writeAsBytes([1, 2, 3, 4]); // Dummy video data

        // Mock Firebase Storage to throw unauthorized error
        when(() => mockStorage.ref()).thenReturn(mockRef);
        when(() => mockRef.child(any())).thenReturn(mockRef);
        when(() => mockRef.putFile(any(), any())).thenThrow(
          Exception('User is not authorized to perform the desired action'),
        );

        // Act & Assert
        expect(
          () => videoUploadService.uploadVideo(
            videoPath: videoPath,
            coverageStats: coverageStats,
            userId: userId,
          ),
          throwsA(isA<Exception>()),
        );

        // Cleanup
        tempDir.deleteSync(recursive: true);
      });

      test('should handle permission denied errors gracefully', () async {
        // Arrange
        final tempDir =
            Directory.systemTemp.createTempSync('video_upload_test');
        final videoPath = '${tempDir.path}/video2.mp4';
        const userId = 'test_user_456';
        const coverageStats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 90,
          framesWithValidCoverage: 80,
          averageCoverage: 0.85,
          minimumCoverage: 0.70,
          maximumCoverage: 0.95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: [],
          qualityScore: 85,
        );

        // Create a test video file
        final testFile = File(videoPath);
        await testFile.writeAsBytes([1, 2, 3, 4]); // Dummy video data

        // Mock Firebase Storage to throw permission error
        when(() => mockStorage.ref()).thenReturn(mockRef);
        when(() => mockRef.child(any())).thenReturn(mockRef);
        when(() => mockRef.putFile(any(), any())).thenThrow(
          Exception('Permission denied'),
        );

        // Act & Assert
        expect(
          () => videoUploadService.uploadVideo(
            videoPath: videoPath,
            coverageStats: coverageStats,
            userId: userId,
          ),
          throwsA(isA<Exception>()),
        );

        // Cleanup
        tempDir.deleteSync(recursive: true);
      });
    });

    group('Path Construction Tests', () {
      test('should construct correct storage path for user videos', () {
        // Arrange
        const userId = 'user123';
        const expectedPathPattern = 'face_verification_videos/user123/';

        // Act - This tests the internal path construction logic
        // The actual path construction happens in
        // _generateFileName and storagePath
        const expectedPath =
            'face_verification_videos/$userId/face_verification_${userId}_123456789.mp4';

        // Assert
        expect(expectedPath, startsWith(expectedPathPattern));
        expect(expectedPath, endsWith('.mp4'));
        expect(expectedPath, contains(userId));
      });

      test('should generate unique filenames for different uploads', () {
        // Arrange
        const userId = 'user123';

        // Act - Simulate filename generation
        final timestamp1 = DateTime.now().millisecondsSinceEpoch;
        final timestamp2 = timestamp1 + 1000; // 1 second later

        final filename1 = 'face_verification_${userId}_$timestamp1.mp4';
        final filename2 = 'face_verification_${userId}_$timestamp2.mp4';

        // Assert
        expect(filename1, isNot(equals(filename2)));
        expect(filename1, contains(userId));
        expect(filename2, contains(userId));
        expect(filename1, endsWith('.mp4'));
        expect(filename2, endsWith('.mp4'));
      });
    });
  });
}
