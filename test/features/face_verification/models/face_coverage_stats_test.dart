import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceCoverageStats', () {
    late List<FaceDetectionResult> sampleDetectionResults;
    late DateTime baseTime;

    setUp(() {
      baseTime = DateTime.now();
      sampleDetectionResults = [
        FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: baseTime,
        ),
        FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 90,
          timestamp: baseTime.add(const Duration(milliseconds: 100)),
        ),
        FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: baseTime.add(const Duration(milliseconds: 200)),
        ),
      ];
    });

    test('should create with all required parameters', () {
      const stats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 60,
        averageCoverage: 75.5,
        minimumCoverage: 10,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
      );

      expect(stats.totalFrames, 100);
      expect(stats.framesWithFace, 80);
      expect(stats.framesWithValidCoverage, 60);
      expect(stats.averageCoverage, 75.5);
      expect(stats.minimumCoverage, 10.0);
      expect(stats.maximumCoverage, 95.0);
      expect(stats.recordingDuration, const Duration(seconds: 9));
      expect(stats.detectionResults, isEmpty);
      expect(stats.qualityScore, 0.0); // Default value
    });

    test('should create with custom quality score', () {
      const stats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 60,
        averageCoverage: 75.5,
        minimumCoverage: 10,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
        qualityScore: 85.5,
      );

      expect(stats.qualityScore, 85.5);
    });

    test('should create with detection results', () {
      final stats = FaceCoverageStats(
        totalFrames: 3,
        framesWithFace: 2,
        framesWithValidCoverage: 2,
        averageCoverage: 58.33,
        minimumCoverage: 0,
        maximumCoverage: 90,
        recordingDuration: const Duration(milliseconds: 300),
        detectionResults: sampleDetectionResults,
      );

      expect(stats.detectionResults.length, 3);
      expect(stats.detectionResults, sampleDetectionResults);
    });

    test('should calculate face detection rate correctly', () {
      const stats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 60,
        averageCoverage: 75.5,
        minimumCoverage: 10,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
      );

      expect(stats.faceDetectionRate, 80.0);
    });

    test('should calculate valid coverage rate correctly', () {
      const stats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 60,
        averageCoverage: 75.5,
        minimumCoverage: 10,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
      );

      expect(stats.validCoverageRate, 60.0);
    });

    test('should determine if meets quality threshold correctly', () {
      const statsAboveThreshold = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 90,
        framesWithValidCoverage: 85,
        averageCoverage: 85,
        minimumCoverage: 70,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
        qualityScore: 85,
      );

      const statsAtThreshold = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 70,
        averageCoverage: 70,
        minimumCoverage: 60,
        maximumCoverage: 80,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
        qualityScore: 70,
      );

      const statsBelowThreshold = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 60,
        framesWithValidCoverage: 50,
        averageCoverage: 60,
        minimumCoverage: 40,
        maximumCoverage: 75,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
        qualityScore: 60,
      );

      expect(statsAboveThreshold.meetsQualityThreshold, true);
      expect(statsAtThreshold.meetsQualityThreshold, true);
      expect(statsBelowThreshold.meetsQualityThreshold, false);
    });

    test('should support equality comparison', () {
      const stats1 = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 60,
        averageCoverage: 75.5,
        minimumCoverage: 10,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
      );

      const stats2 = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 80,
        framesWithValidCoverage: 60,
        averageCoverage: 75.5,
        minimumCoverage: 10,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
      );

      const stats3 = FaceCoverageStats(
        totalFrames: 50,
        framesWithFace: 40,
        framesWithValidCoverage: 30,
        averageCoverage: 70,
        minimumCoverage: 5,
        maximumCoverage: 90,
        recordingDuration: Duration(seconds: 5),
        detectionResults: [],
      );

      expect(stats1, equals(stats2));
      expect(stats1, isNot(equals(stats3)));
    });

    test('should handle edge cases with zero values', () {
      const stats = FaceCoverageStats(
        totalFrames: 0,
        framesWithFace: 0,
        framesWithValidCoverage: 0,
        averageCoverage: 0,
        minimumCoverage: 0,
        maximumCoverage: 0,
        recordingDuration: Duration.zero,
        detectionResults: [],
      );

      expect(stats.faceDetectionRate, 0.0);
      expect(stats.validCoverageRate, 0.0);
      expect(stats.meetsQualityThreshold, false);
    });

    test('should handle perfect coverage scenario', () {
      const stats = FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 100,
        framesWithValidCoverage: 100,
        averageCoverage: 100,
        minimumCoverage: 100,
        maximumCoverage: 100,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
        qualityScore: 100,
      );

      expect(stats.faceDetectionRate, 100.0);
      expect(stats.validCoverageRate, 100.0);
      expect(stats.meetsQualityThreshold, true);
    });

    test('should handle division by zero in percentage calculations', () {
      const stats = FaceCoverageStats(
        totalFrames: 0,
        framesWithFace: 0,
        framesWithValidCoverage: 0,
        averageCoverage: 0,
        minimumCoverage: 0,
        maximumCoverage: 0,
        recordingDuration: Duration.zero,
        detectionResults: [],
      );

      // Should not throw and should return 0.0
      expect(() => stats.faceDetectionRate, returnsNormally);
      expect(() => stats.validCoverageRate, returnsNormally);
      expect(stats.faceDetectionRate, 0.0);
      expect(stats.validCoverageRate, 0.0);
    });
  });
}
