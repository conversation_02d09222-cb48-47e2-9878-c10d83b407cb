import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceDetectionResult', () {
    test('should create with required parameters', () {
      final timestamp = DateTime.now();
      final result = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85.5,
        timestamp: timestamp,
      );

      expect(result.faceDetected, true);
      expect(result.faceCount, 1);
      expect(result.coveragePercentage, 85.5);
      expect(result.timestamp, timestamp);
      expect(result.boundingBox, isNull);
      expect(result.confidence, isNull);
    });

    test('should create with all parameters', () {
      final timestamp = DateTime.now();
      const boundingBox = FaceBoundingBox(
        left: 10,
        top: 20,
        width: 100,
        height: 120,
      );

      final result = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85.5,
        timestamp: timestamp,
        boundingBox: boundingBox,
        confidence: 0.95,
      );

      expect(result.faceDetected, true);
      expect(result.faceCount, 1);
      expect(result.coveragePercentage, 85.5);
      expect(result.timestamp, timestamp);
      expect(result.boundingBox, boundingBox);
      expect(result.confidence, 0.95);
    });

    test('should calculate meetsThreshold correctly', () {
      final timestamp = DateTime.now();

      final resultAboveThreshold = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85,
        timestamp: timestamp,
      );

      final resultAtThreshold = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 80,
        timestamp: timestamp,
      );

      final resultBelowThreshold = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 79.9,
        timestamp: timestamp,
      );

      expect(resultAboveThreshold.meetsThreshold, true);
      expect(resultAtThreshold.meetsThreshold, true);
      expect(resultBelowThreshold.meetsThreshold, false);
    });

    test('should support equality comparison', () {
      final timestamp = DateTime.now();
      const boundingBox = FaceBoundingBox(
        left: 10,
        top: 20,
        width: 100,
        height: 120,
      );

      final result1 = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85.5,
        timestamp: timestamp,
        boundingBox: boundingBox,
        confidence: 0.95,
      );

      final result2 = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 85.5,
        timestamp: timestamp,
        boundingBox: boundingBox,
        confidence: 0.95,
      );

      final result3 = FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );

      expect(result1, equals(result2));
      expect(result1, isNot(equals(result3)));
    });

    test('should handle no face detected scenario', () {
      final timestamp = DateTime.now();
      final result = FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );

      expect(result.faceDetected, false);
      expect(result.faceCount, 0);
      expect(result.coveragePercentage, 0.0);
      expect(result.meetsThreshold, false);
    });

    test('should handle multiple faces detected', () {
      final timestamp = DateTime.now();
      final result = FaceDetectionResult(
        faceDetected: true,
        faceCount: 3,
        coveragePercentage: 45,
        timestamp: timestamp,
      );

      expect(result.faceDetected, true);
      expect(result.faceCount, 3);
      expect(result.coveragePercentage, 45.0);
      expect(result.meetsThreshold, false);
    });

    test('should handle edge case coverage values', () {
      final timestamp = DateTime.now();

      final resultZero = FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );

      final resultMax = FaceDetectionResult(
        faceDetected: true,
        faceCount: 1,
        coveragePercentage: 100,
        timestamp: timestamp,
      );

      expect(resultZero.coveragePercentage, 0.0);
      expect(resultZero.meetsThreshold, false);
      expect(resultMax.coveragePercentage, 100.0);
      expect(resultMax.meetsThreshold, true);
    });
  });

  group('FaceBoundingBox', () {
    test('should create with all parameters', () {
      const boundingBox = FaceBoundingBox(
        left: 10.5,
        top: 20.3,
        width: 100.7,
        height: 120.9,
      );

      expect(boundingBox.left, 10.5);
      expect(boundingBox.top, 20.3);
      expect(boundingBox.width, 100.7);
      expect(boundingBox.height, 120.9);
    });

    test('should support equality comparison', () {
      const boundingBox1 = FaceBoundingBox(
        left: 10,
        top: 20,
        width: 100,
        height: 120,
      );

      const boundingBox2 = FaceBoundingBox(
        left: 10,
        top: 20,
        width: 100,
        height: 120,
      );

      const boundingBox3 = FaceBoundingBox(
        left: 15,
        top: 20,
        width: 100,
        height: 120,
      );

      expect(boundingBox1, equals(boundingBox2));
      expect(boundingBox1, isNot(equals(boundingBox3)));
    });

    test('should handle zero and negative values', () {
      const boundingBox = FaceBoundingBox(
        left: -5,
        top: 0,
        width: 0,
        height: -10,
      );

      expect(boundingBox.left, -5.0);
      expect(boundingBox.top, 0.0);
      expect(boundingBox.width, 0.0);
      expect(boundingBox.height, -10.0);
    });
  });
}
