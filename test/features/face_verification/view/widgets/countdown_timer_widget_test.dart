import 'package:bloomg_flutter/features/face_verification/view/widgets/countdown_timer_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/helpers.dart';
import '../../../../test_config.dart';

void main() {
  setupFlutterTestBindings();
  group('[FACE_VERIFICATION] CountdownTimerWidget Tests', () {
    group('[FACE_VERIFICATION] Action: Basic rendering', () {
      testWidgets('renders countdown with 3 seconds', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 3),
        );

        expect(find.text('3'), findsOneWidget);
        expect(find.text('Get ready...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('renders countdown with 2 seconds', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 2),
        );

        expect(find.text('2'), findsOneWidget);
        expect(find.text('Position your face...'), findsOneWidget);
      });

      testWidgets('renders countdown with 1 second', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 1),
        );

        expect(find.text('1'), findsOneWidget);
        expect(find.text('Recording starts now!'), findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Action: Animation behavior', () {
      testWidgets('triggers animation on countdown change', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 3),
        );

        // Initial state
        expect(find.byType(AnimatedBuilder), findsWidgets);

        // Update countdown
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 2),
        );

        // Animation should be triggered
        await tester.pump(const Duration(milliseconds: 100));
        expect(find.byType(AnimatedBuilder), findsWidgets);
      });

      testWidgets('animates scale and fade effects', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 3),
        );

        // Check for Transform.scale widget (scale animation)
        expect(find.byType(Transform), findsWidgets);

        // Check for Opacity widget (fade animation)
        expect(find.byType(Opacity), findsWidgets);
      });
    });

    group('[FACE_VERIFICATION] Action: Progress indicator', () {
      testWidgets('shows correct progress for 3 seconds remaining',
          (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 3),
        );

        final progressIndicator = tester.widget<CircularProgressIndicator>(
          find.byType(CircularProgressIndicator),
        );

        // Progress should be 0 for 3 seconds remaining (0/3)
        expect(progressIndicator.value, equals(0.0));
      });

      testWidgets('shows correct progress for 2 seconds remaining',
          (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 2),
        );

        final progressIndicator = tester.widget<CircularProgressIndicator>(
          find.byType(CircularProgressIndicator),
        );

        // Progress should be 1/3 for 2 seconds remaining
        expect(progressIndicator.value, closeTo(0.33, 0.01));
      });

      testWidgets('shows correct progress for 1 second remaining',
          (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 1),
        );

        final progressIndicator = tester.widget<CircularProgressIndicator>(
          find.byType(CircularProgressIndicator),
        );

        // Progress should be 2/3 for 1 second remaining
        expect(progressIndicator.value, closeTo(0.67, 0.01));
      });
    });

    group('[FACE_VERIFICATION] Action: Layout and positioning', () {
      testWidgets('centers content properly', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 3),
        );

        // Should have multiple Center widgets (main container and countdown)
        expect(find.byType(Center), findsWidgets);
        expect(find.byType(Column), findsOneWidget);

        final column = tester.widget<Column>(find.byType(Column));
        expect(column.mainAxisAlignment, equals(MainAxisAlignment.center));
      });

      testWidgets('maintains proper spacing between elements', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 3),
        );

        // Check for SizedBox spacing
        expect(find.byType(SizedBox), findsWidgets);
      });
    });

    group('[FACE_VERIFICATION] Action: Edge cases', () {
      testWidgets('handles zero seconds remaining', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 0),
        );

        expect(find.text('0'), findsOneWidget);
        expect(find.text('Starting...'), findsOneWidget);
      });

      testWidgets('handles negative seconds gracefully', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: -1),
        );

        // Should handle negative values gracefully
        expect(find.byType(CountdownTimerWidget), findsOneWidget);
        expect(find.text('Starting...'), findsOneWidget);
      });

      testWidgets('handles large countdown values', (tester) async {
        await tester.pumpApp(
          const CountdownTimerWidget(remainingSeconds: 10),
        );

        expect(find.text('10'), findsOneWidget);
        expect(find.text('Starting...'), findsOneWidget);
      });
    });
  });
}
