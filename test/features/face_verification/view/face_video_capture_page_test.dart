import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_upload_service.dart';
import 'package:bloomg_flutter/features/face_verification/view/face_video_capture_page.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_persistence_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/helpers.dart';

// Mock classes
class MockFaceVideoCaptureBloc
    extends MockBloc<FaceVideoCaptureEvent, FaceVideoCaptureState>
    implements FaceVideoCaptureBloc {}

class MockGoRouter extends Mock implements GoRouter {}

class MockVideoPersistenceService extends Mock
    implements VideoPersistenceService {}

class MockVideoUploadService extends Mock implements VideoUploadService {}

class MockVideoGalleryService extends Mock implements VideoGalleryService {}

class MockAuthRepository extends Mock implements AuthRepository {}

class FakeFaceCoverageStats extends Fake implements FaceCoverageStats {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeFaceCoverageStats());
  });
  group('[FACE_VERIFICATION] FaceVideoCapturePage Widget Tests', () {
    late MockFaceVideoCaptureBloc mockBloc;
    late MockGoRouter mockGoRouter;
    late MockVideoPersistenceService mockVideoPersistenceService;
    late MockVideoUploadService mockVideoUploadService;
    late MockVideoGalleryService mockVideoGalleryService;
    late MockAuthRepository mockAuthRepository;

    const testConfig = VideoCaptureConfig(
      minimumFaceCoverage: 70,
    );

    setUp(() {
      mockBloc = MockFaceVideoCaptureBloc();
      mockGoRouter = MockGoRouter();
      mockVideoPersistenceService = MockVideoPersistenceService();
      mockVideoUploadService = MockVideoUploadService();
      mockVideoGalleryService = MockVideoGalleryService();
      mockAuthRepository = MockAuthRepository();

      // Set up default mock behavior
      when(() => mockBloc.isClosed).thenReturn(false);
      when(() => mockBloc.state).thenReturn(const Initial());
      when(() => mockBloc.stream).thenAnswer((_) => const Stream.empty());

      // Set up mock auth repository with a test user
      when(() => mockAuthRepository.currentUser).thenReturn(
        const UserModel(
          uid: 'test_user_123',
          email: '<EMAIL>',
          displayName: 'Test User',
          emailVerified: true,
        ),
      );

      // Set up mock video upload service with progress stream
      when(
        () => mockVideoUploadService.uploadVideoWithProgress(
          videoPath: any(named: 'videoPath'),
          coverageStats: any(named: 'coverageStats'),
          userId: any(named: 'userId'),
        ),
      ).thenAnswer((_) async* {
        yield const UploadProgress(
          status: UploadStatus.starting,
          progress: 0,
          message: 'Starting upload...',
        );
        yield const UploadProgress(
          status: UploadStatus.uploading,
          progress: 0.5,
          message: 'Uploading...',
        );
        yield const UploadProgress(
          status: UploadStatus.completed,
          progress: 1,
          message: 'Upload completed!',
        );
      });

      // Set up mock video persistence service
      when(() => mockVideoPersistenceService.cleanupTemporaryVideo(any()))
          .thenAnswer((_) async {});

      // Register mocks in GetIt for widget tests
      if (GetIt.instance.isRegistered<FaceVideoCaptureBloc>()) {
        GetIt.instance.unregister<FaceVideoCaptureBloc>();
      }
      if (GetIt.instance.isRegistered<AuthRepository>()) {
        GetIt.instance.unregister<AuthRepository>();
      }
      GetIt.instance.registerFactory<FaceVideoCaptureBloc>(() => mockBloc);
      GetIt.instance.registerFactory<AuthRepository>(() => mockAuthRepository);
    });

    tearDown(() {
      mockBloc.close();

      // Clean up GetIt registrations
      if (GetIt.instance.isRegistered<FaceVideoCaptureBloc>()) {
        GetIt.instance.unregister<FaceVideoCaptureBloc>();
      }
      if (GetIt.instance.isRegistered<AuthRepository>()) {
        GetIt.instance.unregister<AuthRepository>();
      }
    });

    Widget createWidgetUnderTest() {
      return InheritedGoRouter(
        goRouter: mockGoRouter,
        child: FaceVideoCapturePage(
          videoPersistenceService: mockVideoPersistenceService,
          videoUploadService: mockVideoUploadService,
          videoGalleryService: mockVideoGalleryService,
        ),
      );
    }

    group('[FACE_VERIFICATION] Action: Initial state rendering', () {
      testWidgets('renders loading screen for initial state', (tester) async {
        when(() => mockBloc.state).thenReturn(const Initial());

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Initializing camera...'), findsOneWidget);
      });

      testWidgets('renders loading screen for camera initializing state',
          (tester) async {
        when(() => mockBloc.state).thenReturn(
          const CameraInitializing(config: testConfig),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Initializing camera...'), findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Action: Camera ready state', () {
      testWidgets('renders camera ready screen with positioning instructions',
          (tester) async {
        when(() => mockBloc.state).thenReturn(
          const CameraReady(
            config: testConfig,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(
          find.text('Position your face closer in the center guide for '
              'optimal quality. Ensure good lighting and face the camera '
              'directly.'),
          findsOneWidget,
        );

        // Should not have FloatingActionButton anymore
        expect(find.byType(FloatingActionButton), findsNothing);

        // Should have camera preview widget
        expect(find.byType(CameraPreviewWidget), findsOneWidget);
      });

      testWidgets('renders camera ready screen with tap-to-start instructions',
          (tester) async {
        when(() => mockBloc.state).thenReturn(
          const CameraReady(
            config: testConfig,
            canStartRecording: true,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(
          find.text('Perfect! Your face is detected. '
              'Tap anywhere on the screen to start recording.'),
          findsOneWidget,
        );

        // Should not have FloatingActionButton anymore
        expect(find.byType(FloatingActionButton), findsNothing);

        // Should have camera preview widget
        expect(find.byType(CameraPreviewWidget), findsOneWidget);
      });

      testWidgets('tapping overlay triggers start countdown event',
          (tester) async {
        when(() => mockBloc.state).thenReturn(
          const CameraReady(
            config: testConfig,
            canStartRecording: true,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        // Tap on the FaceGuideOverlay widget
        await tester.tap(find.byType(FaceGuideOverlay));

        verify(() => mockBloc.add(const StartCountdown())).called(1);
      });
    });

    group('[FACE_VERIFICATION] Action: Countdown state', () {
      testWidgets('renders countdown screen with timer', (tester) async {
        when(() => mockBloc.state).thenReturn(
          const CountdownInProgress(
            remainingSeconds: 3,
            config: testConfig,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.text('3'), findsOneWidget);
        expect(find.text('Get ready...'), findsOneWidget);
      });

      testWidgets('renders countdown with face validation status',
          (tester) async {
        when(() => mockBloc.state).thenReturn(
          const CountdownInProgress(
            remainingSeconds: 2,
            faceValidationStatus: 'Face detected - countdown continuing...',
            config: testConfig,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.text('2'), findsOneWidget);
        expect(
          find.text('Face detected - countdown continuing...'),
          findsOneWidget,
        );
      });
    });

    group('[FACE_VERIFICATION] Action: Recording state', () {
      testWidgets('renders recording screen with progress', (tester) async {
        when(() => mockBloc.state).thenReturn(
          const Recording(
            elapsedTime: Duration(seconds: 3),
            remainingTime: Duration(seconds: 6),
            config: testConfig,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byIcon(Icons.fiber_manual_record), findsOneWidget);
        expect(find.byType(LinearProgressIndicator), findsOneWidget);
        expect(find.text('6s remaining'), findsOneWidget);
      });

      testWidgets('renders recording screen with face detection feedback',
          (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        when(() => mockBloc.state).thenReturn(
          Recording(
            elapsedTime: const Duration(seconds: 3),
            remainingTime: const Duration(seconds: 6),
            config: testConfig,
            currentDetection: detectionResult,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byIcon(Icons.fiber_manual_record), findsOneWidget);
        expect(find.text('85%'), findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Action: Processing state', () {
      testWidgets('renders processing screen', (tester) async {
        when(() => mockBloc.state).thenReturn(
          const Processing(config: testConfig),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Processing video...'), findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Action: Success state', () {
      testWidgets('renders success screen with statistics', (tester) async {
        const coverageStats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 85,
          framesWithValidCoverage: 80,
          averageCoverage: 82.5,
          minimumCoverage: 70,
          maximumCoverage: 95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: [],
          qualityScore: 85,
        );

        when(() => mockBloc.state).thenReturn(
          const Success(
            videoPath: '/test/path/video.mp4',
            config: testConfig,
            coverageStats: coverageStats,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byIcon(Icons.check_circle), findsOneWidget);
        expect(find.text('Verification Complete!'), findsOneWidget);
        // Quality score appears in multiple places, so check for at least one
        expect(find.textContaining('85.0%'), findsAtLeastNWidgets(1));
      });

      testWidgets('success screen continue button navigates correctly',
          (tester) async {
        const coverageStats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 85,
          framesWithValidCoverage: 80,
          averageCoverage: 82.5,
          minimumCoverage: 70,
          maximumCoverage: 95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: [],
          qualityScore: 85,
        );

        when(() => mockBloc.state).thenReturn(
          const Success(
            videoPath: '/test/path/video.mp4',
            config: testConfig,
            coverageStats: coverageStats,
          ),
        );

        // Mock GoRouter navigation
        when(() => mockGoRouter.go(any())).thenReturn(null);

        await tester.pumpApp(createWidgetUnderTest());

        // Find and tap continue button
        final continueButton = find.text('Continue');
        expect(continueButton, findsOneWidget);

        await tester.tap(continueButton);

        // Wait for upload dialog to appear and complete
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 100));

        // Wait for all async operations to complete
        await tester.pumpAndSettle(const Duration(seconds: 5));

        // For now, just verify the button exists and can be tapped
        // Navigation testing will be added when VideoPersistenceService
        // is properly mocked
        expect(continueButton, findsOneWidget);
      });
    });

    group('[FACE_VERIFICATION] Action: Error state', () {
      testWidgets('renders error screen with message', (tester) async {
        const coverageStats = FaceCoverageStats(
          totalFrames: 50,
          framesWithFace: 20,
          framesWithValidCoverage: 15,
          averageCoverage: 45,
          minimumCoverage: 70,
          maximumCoverage: 60,
          recordingDuration: Duration(seconds: 5),
          detectionResults: [],
          qualityScore: 30,
        );

        when(() => mockBloc.state).thenReturn(
          const Failure(
            reason: 'Camera initialization failed',
            config: testConfig,
            coverageStats: coverageStats,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        expect(find.byIcon(Icons.error), findsOneWidget);
        expect(find.text('Camera initialization failed'), findsOneWidget);
        expect(find.text('Try Again'), findsOneWidget);
      });

      testWidgets('error screen retry button triggers reset event',
          (tester) async {
        const coverageStats = FaceCoverageStats(
          totalFrames: 50,
          framesWithFace: 20,
          framesWithValidCoverage: 15,
          averageCoverage: 45,
          minimumCoverage: 70,
          maximumCoverage: 60,
          recordingDuration: Duration(seconds: 5),
          detectionResults: [],
          qualityScore: 30,
        );

        when(() => mockBloc.state).thenReturn(
          const Failure(
            reason: 'Something went wrong',
            config: testConfig,
            coverageStats: coverageStats,
          ),
        );

        await tester.pumpApp(createWidgetUnderTest());

        await tester.tap(find.text('Try Again'));

        verify(() => mockBloc.add(const ResetCapture())).called(1);
      });
    });

    group('[FACE_VERIFICATION] Action: Widget lifecycle', () {
      testWidgets('initializes camera on widget creation', (tester) async {
        when(() => mockBloc.state).thenReturn(const Initial());

        await tester.pumpApp(createWidgetUnderTest());

        verify(() => mockBloc.add(const InitializeCamera())).called(1);
      });

      testWidgets('disposes resources on widget disposal', (tester) async {
        await tester.pumpApp(createWidgetUnderTest());

        // Remove widget from tree to trigger dispose
        await tester.pumpWidget(const SizedBox());

        // Allow dispose to complete
        await tester.pumpAndSettle();

        verify(() => mockBloc.add(const DisposeResources())).called(1);
      });
    });
  });
}
