import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFaceDetectionRepository extends Mock
    implements FaceDetectionRepository {}

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

class MockVideoValidationService extends Mock
    implements VideoValidationService {}

class MockFile extends Mock implements File {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(const Duration(seconds: 1));
    registerFallbackValue(<FaceDetectionResult>[]);
  });

  group('[FACE_VERIFICATION] Video Recording Integration', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockVideoValidationService mockVideoValidationService;

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockVideoValidationService = MockVideoValidationService();

      // Setup default mocks
      when(() => mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockFaceDetectionRepository.dispose())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.dispose()).thenAnswer((_) async {});

      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: mockVideoStorageRepository,
        videoValidationService: mockVideoValidationService,
        config:
            const VideoCaptureConfig(recordingDuration: Duration(seconds: 2)),
      );
    });

    tearDown(() {
      bloc.close();
    });

    test(
        'should handle complete video recording flow with CamerAwesome integration',
        () async {
      // Arrange - Create a temporary file for testing
      final tempDir = Directory.systemTemp.createTempSync('video_test_');
      final testVideoPath = '${tempDir.path}/video.mp4';
      final testFile = File(testVideoPath);
      await testFile.writeAsBytes([1, 2, 3, 4]); // Create a non-empty file

      when(() => mockVideoStorageRepository.startRecording())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.stopRecording())
          .thenAnswer((_) async {});

      // Mock video validation service for successful validation
      when(
        () => mockVideoValidationService.calculateCoverageStatsWithQuality(
          any(),
          any(),
        ),
      ).thenReturn(
        const FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 85,
          framesWithValidCoverage: 80,
          averageCoverage: 82.5,
          minimumCoverage: 70,
          maximumCoverage: 95,
          recordingDuration: Duration(seconds: 2),
          detectionResults: [],
          qualityScore: 85, // Above 70% threshold
        ),
      );

      // Track states for verification
      final states = <FaceVideoCaptureState>[];
      final subscription = bloc.stream.listen(states.add);

      // Initialize camera
      bloc.add(const InitializeCamera());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Start recording
      bloc.add(const StartRecording());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Add some detection results to simulate recording
      for (var i = 0; i < 5; i++) {
        bloc.add(
          ProcessFrame(
            FaceDetectionResult(
              faceDetected: true,
              faceCount: 1,
              coveragePercentage: 85,
              timestamp: DateTime.now(),
            ),
          ),
        );
      }

      // Stop recording to transition to Processing state
      bloc.add(const StopRecording());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Simulate CamerAwesome completing video recording
      bloc.add(VideoRecordingCompleted(videoPath: testVideoPath));
      await Future<void>.delayed(const Duration(milliseconds: 2000));

      // Cancel subscription and verify final state
      await subscription.cancel();

      // Verify the final state is Success
      expect(bloc.state, isA<Success>());
      expect((bloc.state as Success).videoPath, equals(testVideoPath));

      // Verify that we went through the expected states
      expect(states.any((s) => s is CameraInitializing), isTrue);
      expect(states.any((s) => s is CameraReady), isTrue);
      expect(states.any((s) => s is Recording), isTrue);
      expect(states.any((s) => s is Processing), isTrue);
      expect(states.any((s) => s is Success), isTrue);

      // Verify interactions
      verify(() => mockVideoStorageRepository.initialize()).called(1);
      verify(() => mockVideoStorageRepository.startRecording()).called(1);
      verify(() => mockVideoStorageRepository.stopRecording()).called(1);

      // Cleanup
      tempDir.deleteSync(recursive: true);
    });

    test('should handle video recording failure gracefully', () async {
      // Arrange
      when(() => mockVideoStorageRepository.startRecording())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.stopRecording())
          .thenAnswer((_) async {});

      // Mock video validation service for failed validation
      when(
        () => mockVideoValidationService.calculateCoverageStatsWithQuality(
          any(),
          any(),
        ),
      ).thenReturn(
        const FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 30,
          framesWithValidCoverage: 20,
          averageCoverage: 45,
          minimumCoverage: 20,
          maximumCoverage: 60,
          recordingDuration: Duration(seconds: 2),
          detectionResults: [],
          qualityScore: 45, // Below 70% threshold
        ),
      );

      // Track states for verification
      final states = <FaceVideoCaptureState>[];
      final subscription = bloc.stream.listen(states.add);

      // Initialize camera
      bloc.add(const InitializeCamera());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Start recording
      bloc.add(const StartRecording());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Add some detection results to simulate poor recording
      for (var i = 0; i < 5; i++) {
        bloc.add(
          ProcessFrame(
            FaceDetectionResult(
              faceDetected: true,
              faceCount: 1,
              coveragePercentage: 45, // Poor coverage
              timestamp: DateTime.now(),
            ),
          ),
        );
      }

      // Create a temporary file for testing
      final tempDir = Directory.systemTemp.createTempSync('video_test_');
      final testVideoPath = '${tempDir.path}/video.mp4';
      final testFile = File(testVideoPath);
      await testFile.writeAsBytes([1, 2, 3, 4]); // Create a non-empty file

      // Stop recording to transition to Processing state
      bloc.add(const StopRecording());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Simulate CamerAwesome video recording completion
      bloc.add(VideoRecordingCompleted(videoPath: testVideoPath));
      await Future<void>.delayed(const Duration(milliseconds: 2000));

      // Cancel subscription and verify final state
      await subscription.cancel();

      // Verify the final state is Failure
      expect(bloc.state, isA<Failure>());

      // Verify that we went through the expected states
      expect(states.any((s) => s is CameraInitializing), isTrue);
      expect(states.any((s) => s is CameraReady), isTrue);
      expect(states.any((s) => s is Recording), isTrue);
      expect(states.any((s) => s is Processing), isTrue);
      expect(states.any((s) => s is Failure), isTrue);

      // Cleanup
      tempDir.deleteSync(recursive: true);
    });

    test('should validate video file exists before processing', () async {
      // Arrange
      const testVideoPath = '/test/path/video.mp4';

      when(() => mockVideoStorageRepository.startRecording())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.stopRecording())
          .thenAnswer((_) async {});

      // Act
      bloc.add(const InitializeCamera());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      bloc.add(const StartRecording());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Stop recording to transition to Processing state
      bloc.add(const StopRecording());
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Simulate video recording completion with non-existent file
      bloc.add(const VideoRecordingCompleted(videoPath: testVideoPath));
      await Future<void>.delayed(const Duration(milliseconds: 1000));

      // Assert - Should transition to Error state since file doesn't exist
      expect(bloc.state, isA<Error>());
    });

    test(
        'should not process VideoRecordingCompleted if not in Processing state',
        () async {
      // Arrange
      const testVideoPath = '/test/path/video.mp4';

      // Act - Send VideoRecordingCompleted without being in Processing state
      bloc.add(const VideoRecordingCompleted(videoPath: testVideoPath));
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Assert - Should remain in initial state
      expect(bloc.state, isA<Initial>());
    });
  });
}
