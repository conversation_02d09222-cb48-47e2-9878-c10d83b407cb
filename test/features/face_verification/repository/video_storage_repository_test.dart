import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockCameraService extends Mock implements CameraService {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('[FACE_VERIFICATION] VideoStorageRepository', () {
    late VideoStorageRepository repository;
    late MockCameraService mockCameraService;
    late Directory tempDir;

    setUp(() async {
      mockCameraService = MockCameraService();

      // Create a real temporary directory for testing
      tempDir = await Directory.systemTemp.createTemp('video_storage_test_');

      // Create repository with mocked camera service
      repository = VideoStorageRepository(cameraService: mockCameraService);

      // Mock path_provider plugin to return our temp directory
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('plugins.flutter.io/path_provider'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'getApplicationDocumentsDirectory':
              return tempDir.path;
            case 'getTemporaryDirectory':
              return tempDir.path;
            default:
              return null;
          }
        },
      );

      // Mock camera service methods
      when(() => mockCameraService.initialize()).thenAnswer((_) async {
        return;
      });
      when(() => mockCameraService.dispose()).thenAnswer((_) async {
        return;
      });
      when(
        () => mockCameraService.startRecording(
          filePath: any<String>(named: 'filePath'),
          config: any(named: 'config'),
        ),
      ).thenAnswer((invocation) async {
        // Create a dummy file to simulate recording
        final filePath = invocation.namedArguments[#filePath] as String;
        final file = File(filePath);
        await file.parent.create(recursive: true);
        await file.writeAsString('dummy video content');
        return;
      });
      when(() => mockCameraService.stopRecording()).thenAnswer((_) async {
        return;
      });
    });

    tearDown(() async {
      await repository.dispose();

      // Clean up the temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }

      // Clean up mock method call handler
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('plugins.flutter.io/path_provider'),
        null,
      );
    });

    group('[FACE_VERIFICATION] Action: Initialization', () {
      test('initializes successfully', () async {
        // Now with proper temp directory, initialization should work
        await repository.initialize();

        // Verify the storage directory was created
        final storageDir =
            Directory('${tempDir.path}/face_verification_videos');
        expect(await storageDir.exists(), isTrue);
      });

      test('handles multiple initialization calls', () async {
        // Should handle multiple calls gracefully
        await repository.initialize();
        await repository.initialize();

        // Should not throw and storage should still exist
        final storageDir =
            Directory('${tempDir.path}/face_verification_videos');
        expect(await storageDir.exists(), isTrue);
      });
    });

    group('[FACE_VERIFICATION] Action: Recording management', () {
      test('starts recording and sets current video path', () async {
        // Initialize first
        await repository.initialize();

        // Start recording should set current video path
        await repository.startRecording();

        expect(repository.currentVideoPath, isA<String>());
        expect(repository.currentVideoPath!.isNotEmpty, isTrue);
        expect(repository.currentVideoPath!.endsWith('.mp4'), isTrue);
        expect(repository.isRecording, isTrue);
      });

      test('stops recording and returns file path', () async {
        await repository.initialize();

        // Start recording first
        await repository.startRecording();

        // Stop recording should complete successfully
        await repository.stopRecording();

        expect(repository.isRecording, isFalse);
        expect(repository.currentVideoPath, isNull);
      });

      test('handles stop recording without start', () async {
        await repository.initialize();

        // Stopping without starting should handle gracefully
        expect(
          () => repository.stopRecording(),
          throwsA(isA<StateError>()),
        );
      });

      test('handles multiple start recording calls', () async {
        await repository.initialize();

        await repository.startRecording();

        // Second start should handle gracefully or throw appropriate error
        expect(
          () => repository.startRecording(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('[FACE_VERIFICATION] Action: File operations', () {
      test('generates unique file paths', () async {
        await repository.initialize();

        await repository.startRecording();
        final path1 = repository.currentVideoPath!;
        await repository.stopRecording();

        // Small delay to ensure different timestamp
        await Future<void>.delayed(const Duration(milliseconds: 10));

        await repository.startRecording();
        final path2 = repository.currentVideoPath!;
        await repository.stopRecording();

        expect(path1, isNot(equals(path2)));
      });

      test('creates files in correct directory structure', () async {
        await repository.initialize();

        await repository.startRecording();
        final filePath = repository.currentVideoPath!;

        expect(filePath.contains('face_verification_videos'), isTrue);
        expect(filePath.contains('face_verification_'), isTrue);
        expect(filePath.endsWith('.mp4'), isTrue);
      });

      test('handles file path generation errors gracefully', () async {
        // Test without initialization
        expect(
          () => repository.startRecording(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('[FACE_VERIFICATION] Action: Storage validation', () {
      test('validates storage directory exists after initialization', () async {
        await repository.initialize();

        // After initialization, storage should be ready
        await repository.startRecording();
        final filePath = repository.currentVideoPath!;

        expect(filePath, isNotNull);
        expect(filePath.isNotEmpty, isTrue);
      });

      test('handles storage permission issues', () async {
        // This test would require mocking file system operations
        // For now, we test that the repository handles errors gracefully
        await repository.initialize();

        expect(() => repository.startRecording(), returnsNormally);
      });
    });

    group('[FACE_VERIFICATION] Action: Resource disposal', () {
      test('disposes resources correctly', () async {
        await repository.initialize();

        // Disposal should not throw
        await repository.dispose();

        // After disposal, operations should fail appropriately
        expect(
          () => repository.startRecording(),
          throwsA(isA<StateError>()),
        );
      });

      test('handles disposal without initialization', () async {
        // Should handle disposal gracefully even without initialization
        await repository.dispose();

        expect(repository, isNotNull);
      });

      test('handles multiple disposal calls', () async {
        await repository.initialize();

        await repository.dispose();
        await repository.dispose();

        // Should handle multiple disposal calls gracefully
        expect(repository, isNotNull);
      });
    });

    group('[FACE_VERIFICATION] Action: Error scenarios', () {
      test('handles recording state errors', () async {
        await repository.initialize();

        // Try to stop without starting
        expect(
          () => repository.stopRecording(),
          throwsA(isA<StateError>()),
        );
      });

      test('handles concurrent recording attempts', () async {
        await repository.initialize();

        await repository.startRecording();

        // Second start should throw StateError
        expect(
          () => repository.startRecording(),
          throwsA(isA<StateError>()),
        );

        // Should still be recording the first video
        expect(repository.isRecording, isTrue);
        expect(repository.currentVideoPath, isNotNull);
      });

      test('maintains state consistency', () async {
        await repository.initialize();

        await repository.startRecording();
        final path = repository.currentVideoPath;
        await repository.stopRecording();

        // Should be able to start again after stopping
        await repository.startRecording();
        final newPath = repository.currentVideoPath;
        expect(newPath, isNot(equals(path)));
      });
    });

    group('[FACE_VERIFICATION] Action: File naming and structure', () {
      test('generates files with timestamp-based names', () async {
        await repository.initialize();

        await repository.startRecording();
        final path = repository.currentVideoPath!;

        expect(path.contains('face_verification_'), isTrue);
        expect(path.endsWith('.mp4'), isTrue);

        // Extract timestamp part and verify it's numeric
        final fileName = path.split('/').last;
        final timestampPart = fileName
            .replaceAll('face_verification_', '')
            .replaceAll('.mp4', '');

        expect(int.tryParse(timestampPart), isNotNull);
      });

      test('creates files in documents subdirectory', () async {
        await repository.initialize();

        await repository.startRecording();
        final path = repository.currentVideoPath!;

        expect(path.contains('face_verification_videos'), isTrue);
      });
    });
  });
}
