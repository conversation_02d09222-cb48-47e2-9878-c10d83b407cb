import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFaceDetectionService extends Mock implements FaceDetectionService {}

class MockInputImage extends Mock implements InputImage {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(MockInputImage());
  });

  group('[FACE_VERIFICATION] FaceDetectionRepository', () {
    late FaceDetectionRepository repository;
    late MockFaceDetectionService mockFaceDetectionService;

    setUp(() {
      mockFaceDetectionService = MockFaceDetectionService();

      // Setup default mock behavior
      when(() => mockFaceDetectionService.dispose()).thenAnswer((_) async {});

      repository = FaceDetectionRepository(
        faceDetectionService: mockFaceDetectionService,
      );
    });

    tearDown(() {
      repository.dispose();
    });

    group('[FACE_VERIFICATION] Action: Initialization', () {
      test('initializes successfully', () async {
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});

        await repository.initialize();

        verify(() => mockFaceDetectionService.initialize()).called(1);
      });

      test('handles initialization failure', () async {
        when(() => mockFaceDetectionService.initialize())
            .thenThrow(Exception('Initialization failed'));

        expect(
          () => repository.initialize(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('[FACE_VERIFICATION] Action: Face detection processing', () {
      test('processes image successfully', () async {
        // Initialize repository first
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        final mockInputImage = MockInputImage();
        final expectedResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        when(() => mockFaceDetectionService.processImage(mockInputImage))
            .thenAnswer((_) async => expectedResult);

        final result = await repository.processImage(mockInputImage);

        expect(result, equals(expectedResult));
        verify(() => mockFaceDetectionService.processImage(mockInputImage))
            .called(1);
      });

      test('handles image processing failure', () async {
        // Initialize repository first
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        final mockInputImage = MockInputImage();

        when(() => mockFaceDetectionService.processImage(mockInputImage))
            .thenThrow(Exception('Processing failed'));

        // Repository catches exceptions and returns null, not rethrows
        final result = await repository.processImage(mockInputImage);
        expect(result, isNull);
      });

      test('returns null for invalid image', () async {
        // Initialize repository first
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        final mockInputImage = MockInputImage();

        when(() => mockFaceDetectionService.processImage(mockInputImage))
            .thenAnswer((_) async => null);

        final result = await repository.processImage(mockInputImage);

        expect(result, isNull);
      });

      test('returns null when not initialized', () async {
        final mockInputImage = MockInputImage();

        final result = await repository.processImage(mockInputImage);

        expect(result, isNull);
        // Service should not be called when not initialized
        verifyNever(() => mockFaceDetectionService.processImage(any()));
      });
    });

    group('[FACE_VERIFICATION] Action: Feedback generation', () {
      test('generates feedback message correctly', () {
        const feedbackMessage = 'Face detected - good coverage';
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        when(
          () => mockFaceDetectionService.generateFeedbackMessage(
            detectionResult,
          ),
        ).thenReturn(feedbackMessage);

        final result = repository.generateFeedbackMessage(detectionResult);

        expect(result, equals(feedbackMessage));
        verify(
          () => mockFaceDetectionService.generateFeedbackMessage(
            detectionResult,
          ),
        ).called(1);
      });

      test('handles different detection scenarios', () {
        final noFaceResult = FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: DateTime.now(),
        );

        when(
          () => mockFaceDetectionService.generateFeedbackMessage(
            noFaceResult,
          ),
        ).thenReturn('No face detected');

        final result = repository.generateFeedbackMessage(noFaceResult);

        expect(result, equals('No face detected'));
      });
    });

    group('[FACE_VERIFICATION] Action: Statistics management', () {
      test('gets processing statistics', () {
        final expectedStats = {
          'total_frames': 100,
          'successful_detections': 85,
          'failed_detections': 15,
          'average_processing_time': 50.5,
        };

        when(() => mockFaceDetectionService.getProcessingStats())
            .thenReturn(expectedStats);

        final result = repository.getProcessingStats();

        expect(result, equals(expectedStats));
        verify(() => mockFaceDetectionService.getProcessingStats()).called(1);
      });

      test('resets statistics correctly', () {
        repository.resetStats();

        verify(() => mockFaceDetectionService.resetStats()).called(1);
      });

      test('validates recording quality', () {
        when(
          () => mockFaceDetectionService.validateRecordingQuality(),
        ).thenReturn(true);

        final result = repository.validateRecordingQuality();

        expect(result, isTrue);
        verify(
          () => mockFaceDetectionService.validateRecordingQuality(),
        ).called(1);
      });

      test('validates recording quality with custom threshold', () {
        when(
          () => mockFaceDetectionService.validateRecordingQuality(
            minimumValidFramePercentage: 70,
          ),
        ).thenReturn(false);

        final result = repository.validateRecordingQuality(
          minimumValidFramePercentage: 70,
        );

        expect(result, isFalse);
      });
    });

    group('[FACE_VERIFICATION] Action: Resource disposal', () {
      test('disposes resources correctly', () async {
        // Initialize repository first so it can be disposed
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        when(() => mockFaceDetectionService.dispose()).thenAnswer((_) async {});

        await repository.dispose();

        verify(() => mockFaceDetectionService.dispose()).called(1);
      });

      test('handles disposal failure gracefully', () async {
        // Initialize repository first so it can be disposed
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        when(() => mockFaceDetectionService.dispose())
            .thenThrow(Exception('Disposal failed'));

        // Should not throw, disposal should be graceful
        await repository.dispose();

        verify(() => mockFaceDetectionService.dispose()).called(1);
      });

      test('skips disposal when not initialized', () async {
        await repository.dispose();

        // Service dispose should not be called when not initialized
        verifyNever(() => mockFaceDetectionService.dispose());
      });
    });

    group('[FACE_VERIFICATION] Action: Edge cases', () {
      test('handles multiple initialization calls', () async {
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});

        await repository.initialize();
        await repository.initialize();

        // Repository has early return logic, so service is only called once
        verify(() => mockFaceDetectionService.initialize()).called(1);
      });

      test('handles processing after disposal', () async {
        // Initialize first, then dispose
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        final mockInputImage = MockInputImage();

        when(() => mockFaceDetectionService.dispose()).thenAnswer((_) async {});
        when(() => mockFaceDetectionService.processImage(mockInputImage))
            .thenThrow(Exception('Service disposed'));

        await repository.dispose();

        // Repository returns null when not initialized, doesn't throw
        final result = await repository.processImage(mockInputImage);
        expect(result, isNull);
      });

      test('handles null input image gracefully', () async {
        // Initialize repository first
        when(() => mockFaceDetectionService.initialize())
            .thenAnswer((_) async {});
        await repository.initialize();

        when(() => mockFaceDetectionService.processImage(any()))
            .thenAnswer((_) async => null);

        final result = await repository.processImage(MockInputImage());

        expect(result, isNull);
      });
    });
  });
}
