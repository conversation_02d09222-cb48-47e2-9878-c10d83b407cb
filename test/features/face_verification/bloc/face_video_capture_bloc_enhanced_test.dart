import 'dart:async';
import 'dart:io';

import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fake_async/fake_async.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFaceDetectionRepository extends Mock implements FaceDetectionRepository {}
class MockVideoStorageRepository extends Mock implements VideoStorageRepository {}
class MockVideoValidationService extends Mock implements VideoValidationService {}

/// Helper function to create a temporary video file with content
Future<String> createTempVideoFile({int sizeInBytes = 1024}) async {
  final tempDir = Directory.systemTemp;
  final tempFile = File(
    '${tempDir.path}/test_video_${DateTime.now().millisecondsSinceEpoch}.mp4',
  );

  // Create MP4 file with basic header
  final mp4Header = List.generate(sizeInBytes, (index) => index % 256);
  await tempFile.writeAsBytes(mp4Header);
  return tempFile.path;
}

/// Helper function to create an empty video file (0 bytes)
Future<String> createEmptyVideoFile() async {
  final tempDir = Directory.systemTemp;
  final tempFile = File(
    '${tempDir.path}/empty_video_${DateTime.now().millisecondsSinceEpoch}.mp4',
  );
  await tempFile.writeAsBytes([]);
  return tempFile.path;
}

/// Helper function to create synthetic face detection results
List<FaceDetectionResult> createSyntheticDetectionResults({
  required int count,
  required bool faceDetected,
  required int faceCount,
  required double coveragePercentage,
}) {
  return List.generate(
    count,
    (index) => FaceDetectionResult(
      faceDetected: faceDetected,
      faceCount: faceCount,
      coveragePercentage: coveragePercentage,
      timestamp: DateTime.now().subtract(Duration(milliseconds: index * 100)),
    ),
  );
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(const Duration(seconds: 1));
    registerFallbackValue(const VideoCaptureConfig());
    registerFallbackValue(<FaceDetectionResult>[]);
  });

  group('Enhanced FaceVideoCaptureBloc Tests', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockVideoValidationService mockVideoValidationService;
    late VideoCaptureConfig testConfig;

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockVideoValidationService = MockVideoValidationService();
      testConfig = const VideoCaptureConfig();

      // Setup default mock behaviors
      when(() => mockFaceDetectionRepository.initialize()).thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.initialize()).thenAnswer((_) async {});
      when(() => mockFaceDetectionRepository.dispose()).thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.dispose()).thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.startRecording()).thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.stopRecording()).thenAnswer((_) async {});

      // Default video validation service behavior
      when(() => mockVideoValidationService.calculateCoverageStatsWithQuality(any(), any()))
          .thenReturn(const FaceCoverageStats(
        totalFrames: 100,
        framesWithFace: 85,
        framesWithValidCoverage: 80,
        averageCoverage: 82.5,
        minimumCoverage: 70,
        maximumCoverage: 95,
        recordingDuration: Duration(seconds: 9),
        detectionResults: [],
        qualityScore: 85,
      ));

      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: mockVideoStorageRepository,
        videoValidationService: mockVideoValidationService,
        config: testConfig,
      );
    });

    tearDown(() {
      bloc.close();
    });

    group('1. Complex State Chains', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Complete flow: Initialize ➜ Ready ➜ Countdown ➜ Recording ➜ Processing ➜ Success',
        build: () => bloc,
        act: (bloc) async {
          // Initialize
          bloc.add(const InitializeCamera());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          
          // Start countdown
          bloc.add(const StartCountdown());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          
          // Start recording
          bloc.add(const StartRecording());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          
          // Stop recording
          bloc.add(const StopRecording());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          
          // Complete recording with valid video
          final videoPath = await createTempVideoFile();
          bloc.add(VideoRecordingCompleted(videoPath: videoPath));
        },
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<CountdownInProgress>(),
          isA<Recording>(),
          isA<Processing>(),
          isA<Success>(),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Complete flow: Initialize ➜ Ready ➜ Countdown ➜ Recording ➜ Processing ➜ Failure',
        build: () {
          // Mock poor quality video
          when(() => mockVideoValidationService.calculateCoverageStatsWithQuality(any(), any()))
              .thenReturn(const FaceCoverageStats(
            totalFrames: 100,
            framesWithFace: 30,
            framesWithValidCoverage: 25,
            averageCoverage: 45.0,
            minimumCoverage: 30,
            maximumCoverage: 60,
            recordingDuration: Duration(seconds: 9),
            detectionResults: [],
            qualityScore: 45,
          ));
          return bloc;
        },
        act: (bloc) async {
          bloc.add(const InitializeCamera());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          bloc.add(const StartCountdown());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          bloc.add(const StartRecording());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          bloc.add(const StopRecording());
          await Future<void>.delayed(const Duration(milliseconds: 100));
          final videoPath = await createTempVideoFile();
          bloc.add(VideoRecordingCompleted(videoPath: videoPath));
        },
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<CountdownInProgress>(),
          isA<Recording>(),
          isA<Processing>(),
          isA<Failure>().having(
            (state) => state.reason,
            'failure reason',
            contains('Insufficient face coverage'),
          ),
        ],
      );
    });

    group('2. Multiple Face Scenarios', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'No face detected scenario with synthetic ProcessFrame events',
        build: () => bloc,
        seed: () => Recording(
          elapsedTime: const Duration(seconds: 2),
          remainingTime: const Duration(seconds: 7),
          config: testConfig,
        ),
        act: (bloc) {
          // Generate synthetic events with no face
          for (var i = 0; i < 10; i++) {
            bloc.add(ProcessFrame(FaceDetectionResult(
              faceDetected: false,
              faceCount: 0,
              coveragePercentage: 0.0,
              timestamp: DateTime.now().subtract(Duration(milliseconds: i * 100)),
            )));
          }
        },
        expect: () => [
          isA<Recording>(),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Two faces detected scenario with synthetic ProcessFrame events',
        build: () => bloc,
        seed: () => Recording(
          elapsedTime: const Duration(seconds: 2),
          remainingTime: const Duration(seconds: 7),
          config: testConfig,
        ),
        act: (bloc) {
          // Generate synthetic events with two faces
          for (var i = 0; i < 10; i++) {
            bloc.add(ProcessFrame(FaceDetectionResult(
              faceDetected: true,
              faceCount: 2,
              coveragePercentage: 85.0,
              timestamp: DateTime.now().subtract(Duration(milliseconds: i * 100)),
            )));
          }
        },
        expect: () => [
          isA<Recording>(),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Fluctuating coverage scenario with synthetic ProcessFrame events',
        build: () => bloc,
        seed: () => Recording(
          elapsedTime: const Duration(seconds: 2),
          remainingTime: const Duration(seconds: 7),
          config: testConfig,
        ),
        act: (bloc) {
          // Generate synthetic events with fluctuating coverage
          for (var i = 0; i < 20; i++) {
            final coverage = 60.0 + (i % 5) * 10.0; // Coverage fluctuates between 60-100%
            bloc.add(ProcessFrame(FaceDetectionResult(
              faceDetected: true,
              faceCount: 1,
              coveragePercentage: coverage,
              timestamp: DateTime.now().subtract(Duration(milliseconds: i * 100)),
            )));
          }
        },
        expect: () => [
          isA<Recording>(),
        ],
      );
    });

    group('3. Timer Race Conditions', () {
      test('Jump clock forward to exactly 0 ms left during countdown', () {
FakeAsync().run((fake) {
          // Initialize and start countdown
          bloc.add(const InitializeCamera());
          bloc.add(const StartCountdown());
          
          // Jump to exactly when countdown should finish
fake.elapse(const Duration(seconds: 3));
fake.flushMicrotasks();
          
          // Verify the bloc handled the race condition properly
          expect(bloc.state, isA<FaceVideoCaptureState>());
        });
      });

      test('Jump clock forward to exactly 0 ms left during recording', () {
FakeAsync().run((fake) {
          // Initialize, start countdown, and start recording
          bloc.add(const InitializeCamera());
          bloc.add(const StartCountdown());
          bloc.add(const StartRecording());
          
          // Jump to exactly when recording should finish
fake.elapse(const Duration(seconds: 9));
fake.flushMicrotasks();
          
          // Verify the bloc handled the race condition properly
          expect(bloc.state, isA<FaceVideoCaptureState>());
        });
      });
    });

    group('4. Async Timeout Paths', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Timeout during video validation causes Error state',
        build: () {
          // Make video validation service hang
          when(() => mockVideoValidationService.calculateCoverageStatsWithQuality(any(), any()))
              .thenThrow(Exception('Operation timed out: Coverage statistics calculation after 10s'));
          return bloc;
        },
        act: (bloc) async {
          bloc.add(const InitializeCamera());
          bloc.add(const StartRecording());
          bloc.add(const StopRecording());
          final videoPath = await createTempVideoFile();
          bloc.add(VideoRecordingCompleted(videoPath: videoPath));
        },
        wait: const Duration(seconds: 12),
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<Recording>(),
          isA<Processing>(),
          isA<Error>().having(
            (state) => state.error,
            'error message',
            contains('Operation timed out'),
          ),
        ],
      );

      test('Late Future with TimeoutException using FakeAsync', () {
FakeAsync().run((fake) {
          // Create a completer that won't complete immediately
          final completer = Completer<void>();
          
          // Create a future that should timeout
          final timeoutFuture = Future.any([
            completer.future,
            Future<void>.delayed(const Duration(seconds: 1)).then((_) {
              throw Exception('Operation timed out: Test timeout operation after 1s');
            }),
          ]);
          
          // Advance time to trigger timeout
fake.elapse(const Duration(seconds: 2));
          
          // Verify timeout behavior
          expect(() => timeoutFuture, throwsException);
        });
      });
    });

    group('5. Video Corruption', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'VideoRecordingCompleted with 0-byte file causes Error state',
        build: () => bloc,
        act: (bloc) async {
          bloc.add(const InitializeCamera());
          bloc.add(const StartRecording());
          bloc.add(const StopRecording());
          
          // Create empty video file
          final emptyVideoPath = await createEmptyVideoFile();
          bloc.add(VideoRecordingCompleted(videoPath: emptyVideoPath));
        },
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<Recording>(),
          isA<Processing>(),
          isA<Error>().having(
            (state) => state.error,
            'error message',
            contains('Video file is empty'),
          ),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'VideoRecordingCompleted with non-existent file causes Error state',
        build: () => bloc,
        act: (bloc) async {
          bloc.add(const InitializeCamera());
          bloc.add(const StartRecording());
          bloc.add(const StopRecording());
          
          // Use non-existent file path
          const nonExistentPath = '/non/existent/path/video.mp4';
          bloc.add(const VideoRecordingCompleted(videoPath: nonExistentPath));
        },
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<Recording>(),
          isA<Processing>(),
          isA<Error>().having(
            (state) => state.error,
            'error message',
            contains('Video file not found'),
          ),
        ],
      );
    });

    group('6. Memory High-Load', () {
      test('10k ProcessFrame events - bloc remains responsive', () async {
        // Set up recording state
        bloc.add(const InitializeCamera());
        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Generate 10k ProcessFrame events
        for (var i = 0; i < 10000; i++) {
          bloc.add(ProcessFrame(FaceDetectionResult(
            faceDetected: true,
            faceCount: 1,
            coveragePercentage: 85.0,
            timestamp: DateTime.now().subtract(Duration(microseconds: i)),
          )));
        }

        // Allow all events to be processed
        await Future<void>.delayed(const Duration(seconds: 2));

        // Verify bloc is still responsive
        expect(bloc.state, isA<Recording>());
        expect(bloc.isClosed, isFalse);
        
        // Verify stats are computed
        final recordingState = bloc.state as Recording;
        expect(recordingState.coverageStats.totalFrames, greaterThan(0));
      });

      test('Memory stress test - rapid events with garbage collection', () async {
        bloc.add(const InitializeCamera());
        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Generate many events in batches to stress test memory
        for (var batch = 0; batch < 100; batch++) {
          for (var i = 0; i < 100; i++) {
            bloc.add(ProcessFrame(FaceDetectionResult(
              faceDetected: batch % 2 == 0,
              faceCount: (batch % 3) + 1,
              coveragePercentage: (batch * 10.0) % 100.0,
              timestamp: DateTime.now().subtract(Duration(microseconds: batch * 100 + i)),
            )));
          }
          // Allow garbage collection between batches
          await Future<void>.delayed(const Duration(milliseconds: 10));
        }

        // Verify bloc is still functional
        expect(bloc.state, isA<Recording>());
        expect(bloc.isClosed, isFalse);
      });
    });

    group('7. Network Interruption Simulation', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'stopRecording throws mid-await causes Error state',
        build: () {
          // Mock network interruption during stop recording
          when(() => mockVideoStorageRepository.stopRecording())
              .thenThrow(Exception('Network connection lost'));
          return bloc;
        },
        act: (bloc) async {
          bloc.add(const InitializeCamera());
          bloc.add(const StartRecording());
          bloc.add(const StopRecording());
        },
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<Recording>(),
          isA<Processing>(),
          isA<Error>().having(
            (state) => state.error,
            'error message',
            contains('Failed to stop recording'),
          ),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'startRecording throws due to network interruption',
        build: () {
          // Mock network interruption during start recording
          when(() => mockVideoStorageRepository.startRecording())
              .thenThrow(Exception('Network timeout'));
          return bloc;
        },
        act: (bloc) async {
          bloc.add(const InitializeCamera());
          bloc.add(const StartRecording());
        },
        expect: () => [
          isA<CameraInitializing>(),
          isA<CameraReady>(),
          isA<Error>().having(
            (state) => state.error,
            'error message',
            contains('Failed to start recording'),
          ),
        ],
      );
    });

    group('8. Additional Edge Cases', () {
      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Rapid event sequence - face detection status changes',
        build: () => bloc,
        seed: () => CameraReady(config: testConfig),
        act: (bloc) {
          // Rapid fire face detection status changes
          for (var i = 0; i < 50; i++) {
            bloc.add(FaceDetectionStatusChanged(
              canStartRecording: i % 2 == 0,
              detectionResult: FaceDetectionResult(
                faceDetected: i % 2 == 0,
                faceCount: i % 2 == 0 ? 1 : 0,
                coveragePercentage: i % 2 == 0 ? 85.0 : 0.0,
                timestamp: DateTime.now().subtract(Duration(milliseconds: i * 10)),
              ),
            ));
          }
        },
        expect: () => [
          isA<CameraReady>(),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Countdown abort during face detection changes',
        build: () => bloc,
        seed: () => CountdownInProgress(
          remainingSeconds: 2,
          config: testConfig,
        ),
        act: (bloc) {
          // Simulate face detection loss during countdown
          bloc.add(FaceDetectionStatusChanged(
            canStartRecording: false,
            detectionResult: FaceDetectionResult(
              faceDetected: false,
              faceCount: 0,
              coveragePercentage: 0.0,
              timestamp: DateTime.now(),
            ),
          ));
        },
        expect: () => [
          isA<CameraReady>(),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Resource disposal during active recording',
        build: () => bloc,
        seed: () => Recording(
          elapsedTime: const Duration(seconds: 3),
          remainingTime: const Duration(seconds: 6),
          config: testConfig,
        ),
        act: (bloc) => bloc.add(const DisposeResources()),
        expect: () => [
          isA<Initial>(),
        ],
      );

      blocTest<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        'Reset capture during processing',
        build: () => bloc,
        seed: () => Processing(config: testConfig),
        act: (bloc) => bloc.add(const ResetCapture()),
        expect: () => [
          isA<CameraReady>(),
        ],
      );
    });
  });
}
