import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockVideoGalleryRepository extends Mock
    implements VideoGalleryRepository {}

class FakeGalleryFilter extends Fake implements GalleryFilter {}

class FakeVideoItem extends Fake implements VideoItem {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeGalleryFilter());
    registerFallbackValue(FakeVideoItem());
  });
  group('VideoGalleryBloc', () {
    late VideoGalleryBloc bloc;
    late MockVideoGalleryRepository mockRepository;
    late List<VideoItem> sampleVideos;

    setUp(() {
      mockRepository = MockVideoGalleryRepository();
      bloc = VideoGalleryBloc(repository: mockRepository);

      sampleVideos = [
        VideoItem(
          id: 'video-1',
          fileName: 'face_verification_2024-01-15_10-30-45.mp4',
          filePath: '/path/to/video1.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15, 10, 30, 45),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
        VideoItem(
          id: 'video-2',
          fileName: 'face_verification_2024-01-14_09-15-30.mp4',
          filePath: '/path/to/video2.mp4',
          fileSize: 2048000,
          createdAt: DateTime(2024, 1, 14, 9, 15, 30),
          duration: const Duration(seconds: 9),
          qualityScore: 92,
        ),
        VideoItem(
          id: 'video-3',
          fileName: 'face_verification_2024-01-16_14-45-20.mp4',
          filePath: '/path/to/video3.mp4',
          fileSize: 1536000,
          createdAt: DateTime(2024, 1, 16, 14, 45, 20),
          duration: const Duration(seconds: 9),
          qualityScore: 78.3,
        ),
      ];
    });

    tearDown(() {
      bloc.close();
    });

    test('initial state is VideoGalleryInitial', () {
      expect(bloc.state, const VideoGalleryInitial());
    });

    group('LoadVideos', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [loading, loaded] when videos are loaded successfully',
        build: () {
          when(() => mockRepository.loadVideos())
              .thenAnswer((_) async => sampleVideos);
          when(() => mockRepository.filterVideos(sampleVideos, any()))
              .thenReturn(sampleVideos);
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadVideos()),
        expect: () => [
          isA<VideoGalleryLoading>(),
          isA<VideoGalleryLoaded>()
              .having((state) => state.allVideos, 'allVideos', sampleVideos)
              .having(
                (state) => state.filteredVideos,
                'filteredVideos',
                sampleVideos,
              ),
        ],
        verify: (_) {
          verify(() => mockRepository.loadVideos()).called(1);
          verify(() => mockRepository.filterVideos(sampleVideos, any()))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [loading, empty] when no videos are found',
        build: () {
          when(() => mockRepository.loadVideos())
              .thenAnswer((_) async => <VideoItem>[]);
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadVideos()),
        expect: () => [
          isA<VideoGalleryLoading>(),
          isA<VideoGalleryEmpty>(),
        ],
        verify: (_) {
          verify(() => mockRepository.loadVideos()).called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [loading, error] when loading fails',
        build: () {
          when(() => mockRepository.loadVideos())
              .thenThrow(Exception('Failed to load videos'));
          return bloc;
        },
        act: (bloc) => bloc.add(const LoadVideos()),
        expect: () => [
          isA<VideoGalleryLoading>(),
          isA<VideoGalleryError>().having(
            (state) => state.message,
            'message',
            contains('Failed to load videos'),
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.loadVideos()).called(1);
        },
      );
    });

    group('RefreshVideos', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'triggers LoadVideos event',
        build: () {
          when(() => mockRepository.loadVideos())
              .thenAnswer((_) async => sampleVideos);
          when(() => mockRepository.filterVideos(sampleVideos, any()))
              .thenReturn(sampleVideos);
          return bloc;
        },
        act: (bloc) => bloc.add(const RefreshVideos()),
        expect: () => [
          isA<VideoGalleryLoading>(),
          isA<VideoGalleryLoaded>(),
        ],
        verify: (_) {
          verify(() => mockRepository.loadVideos()).called(1);
        },
      );
    });

    group('DeleteVideo', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [deleting, deleted] when video is deleted successfully',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenAnswer((_) async => true);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(DeleteVideo(sampleVideos.first)),
        expect: () => [
          isA<VideoDeleting>().having(
            (state) => state.deletingVideo,
            'deletingVideo',
            sampleVideos.first,
          ),
          isA<VideoDeleted>()
              .having(
                (state) => state.deletedVideo,
                'deletedVideo',
                sampleVideos.first,
              )
              .having((state) => state.allVideos.length, 'allVideos length', 2)
              .having(
                (state) => state.filteredVideos.length,
                'filteredVideos length',
                2,
              ),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteVideo(sampleVideos.first))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [deleting, empty] when last video is deleted',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenAnswer((_) async => true);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: [sampleVideos.first],
          filteredVideos: [sampleVideos.first],
        ),
        act: (bloc) => bloc.add(DeleteVideo(sampleVideos.first)),
        expect: () => [
          isA<VideoDeleting>(),
          isA<VideoGalleryEmpty>(),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteVideo(sampleVideos.first))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [deleting, deleteError] when deletion fails',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenAnswer((_) async => false);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(DeleteVideo(sampleVideos.first)),
        expect: () => [
          isA<VideoDeleting>(),
          isA<VideoDeleteError>()
              .having((state) => state.video, 'video', sampleVideos.first)
              .having(
                (state) => state.message,
                'message',
                'Failed to delete video',
              ),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteVideo(sampleVideos.first))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [deleting, deleteError] when deletion throws error',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenThrow(Exception('Delete error'));
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(DeleteVideo(sampleVideos.first)),
        expect: () => [
          isA<VideoDeleting>(),
          isA<VideoDeleteError>()
              .having((state) => state.video, 'video', sampleVideos.first)
              .having(
                (state) => state.message,
                'message',
                contains('Error deleting video'),
              ),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteVideo(sampleVideos.first))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'clears selected video if it was the deleted video',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenAnswer((_) async => true);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
          selectedVideo: sampleVideos.first,
        ),
        act: (bloc) => bloc.add(DeleteVideo(sampleVideos.first)),
        expect: () => [
          isA<VideoDeleting>(),
          isA<VideoDeleted>()
              .having((state) => state.selectedVideo, 'selectedVideo', isNull),
        ],
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'keeps selected video if different video was deleted',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenAnswer((_) async => true);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
          selectedVideo: sampleVideos.last,
        ),
        act: (bloc) => bloc.add(DeleteVideo(sampleVideos.first)),
        expect: () => [
          isA<VideoDeleting>(),
          isA<VideoDeleted>().having(
            (state) => state.selectedVideo,
            'selectedVideo',
            sampleVideos.last,
          ),
        ],
      );
    });

    group('ApplyFilter', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'applies filter to existing videos',
        build: () {
          when(() => mockRepository.filterVideos(sampleVideos, any()))
              .thenReturn([sampleVideos.first]);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(
          const ApplyFilter(GalleryFilter(minQualityScore: 80)),
        ),
        expect: () => [
          isA<VideoGalleryLoaded>()
              .having(
                (state) => state.currentFilter.minQualityScore,
                'filter',
                80,
              )
              .having(
                (state) => state.filteredVideos.length,
                'filtered count',
                1,
              ),
        ],
        verify: (_) {
          verify(
            () => mockRepository.filterVideos(
              sampleVideos,
              const GalleryFilter(minQualityScore: 80),
            ),
          ).called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits empty state when no videos match filter',
        build: () {
          when(() => mockRepository.filterVideos(any(), any()))
              .thenReturn(<VideoItem>[]);
          return bloc;
        },
        seed: () => const VideoGalleryLoaded(
          allVideos: <VideoItem>[],
          filteredVideos: <VideoItem>[],
        ),
        act: (bloc) => bloc.add(
          const ApplyFilter(GalleryFilter(minQualityScore: 95)),
        ),
        expect: () => [
          isA<VideoGalleryEmpty>().having(
            (state) => state.currentFilter.minQualityScore,
            'filter',
            95,
          ),
        ],
      );
    });

    group('ClearFilter', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'applies default filter',
        build: () {
          when(() => mockRepository.filterVideos(sampleVideos, any()))
              .thenReturn(sampleVideos);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: [sampleVideos.first],
          currentFilter: const GalleryFilter(minQualityScore: 80),
        ),
        act: (bloc) => bloc.add(const ClearFilter()),
        expect: () => [
          isA<VideoGalleryLoaded>()
              .having(
                (state) => state.currentFilter,
                'filter',
                const GalleryFilter(),
              )
              .having(
                (state) => state.filteredVideos.length,
                'filtered count',
                3,
              ),
        ],
        verify: (_) {
          verify(
            () => mockRepository.filterVideos(
              sampleVideos,
              const GalleryFilter(),
            ),
          ).called(1);
        },
      );
    });

    group('GenerateThumbnail', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits [generating, loaded] when thumbnail is generated successfully',
        build: () {
          final updatedVideo = sampleVideos.first.copyWith(
            thumbnailPath: '/path/to/thumbnail.jpg',
          );
          when(() => mockRepository.generateThumbnail(any()))
              .thenAnswer((_) async => updatedVideo);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(GenerateThumbnail(sampleVideos.first)),
        expect: () => [
          isA<ThumbnailGenerating>()
              .having((state) => state.video, 'video', sampleVideos.first),
          isA<VideoGalleryLoaded>().having(
            (state) => state.allVideos.first.thumbnailPath,
            'thumbnail',
            '/path/to/thumbnail.jpg',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.generateThumbnail(sampleVideos.first))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'returns to loaded state when thumbnail generation fails',
        build: () {
          when(() => mockRepository.generateThumbnail(any()))
              .thenAnswer((_) async => null);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(GenerateThumbnail(sampleVideos.first)),
        expect: () => [
          isA<ThumbnailGenerating>(),
          isA<VideoGalleryLoaded>(),
        ],
        verify: (_) {
          verify(() => mockRepository.generateThumbnail(sampleVideos.first))
              .called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'returns to loaded state when thumbnail generation throws error',
        build: () {
          when(() => mockRepository.generateThumbnail(any()))
              .thenThrow(Exception('Thumbnail error'));
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(GenerateThumbnail(sampleVideos.first)),
        expect: () => [
          isA<ThumbnailGenerating>(),
          isA<VideoGalleryLoaded>(),
        ],
        verify: (_) {
          verify(() => mockRepository.generateThumbnail(sampleVideos.first))
              .called(1);
        },
      );
    });

    group('LoadStorageStats', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'updates storage stats in current state',
        build: () {
          final stats = {
            'totalVideos': 3,
            'totalSize': 4608000,
            'averageQuality': 85.27,
          };
          when(() => mockRepository.getStorageStats())
              .thenAnswer((_) async => stats);
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(const LoadStorageStats()),
        expect: () => [
          isA<VideoGalleryLoaded>().having(
            (state) => state.storageStats,
            'storageStats',
            isNotEmpty,
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.getStorageStats()).called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'handles storage stats error gracefully',
        build: () {
          when(() => mockRepository.getStorageStats())
              .thenThrow(Exception('Stats error'));
          return bloc;
        },
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(const LoadStorageStats()),
        expect: () => <VideoGalleryState>[],
        verify: (_) {
          verify(() => mockRepository.getStorageStats()).called(1);
        },
      );
    });

    group('VideoSelected', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'sets selected video in state',
        build: () => bloc,
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
        act: (bloc) => bloc.add(VideoSelected(sampleVideos.first)),
        expect: () => [
          isA<VideoGalleryLoaded>().having(
            (state) => state.selectedVideo,
            'selectedVideo',
            sampleVideos.first,
          ),
        ],
      );
    });

    group('VideoDeselected', () {
      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'clears selected video from state',
        build: () => bloc,
        seed: () => VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
          selectedVideo: sampleVideos.first,
        ),
        act: (bloc) => bloc.add(const VideoDeselected()),
        expect: () => [
          isA<VideoGalleryLoaded>()
              .having((state) => state.selectedVideo, 'selectedVideo', isNull),
        ],
      );
    });

    group('State preservation', () {
      test('loading state preserves previous state properties', () {
        const initialState = VideoGalleryLoaded(
          allVideos: [],
          filteredVideos: [],
          currentFilter: GalleryFilter(minQualityScore: 80),
          storageStats: {'total': 5},
        );

        const loadingState = VideoGalleryLoading(
          currentFilter: GalleryFilter(minQualityScore: 80),
          storageStats: {'total': 5},
        );

        expect(loadingState.currentFilter, initialState.currentFilter);
        expect(loadingState.storageStats, initialState.storageStats);
      });

      test('error state preserves previous state properties', () {
        const previousState = VideoGalleryLoaded(
          allVideos: [],
          filteredVideos: [],
          currentFilter: GalleryFilter(minQualityScore: 80),
          storageStats: {'total': 5},
        );

        const errorState = VideoGalleryError(
          message: 'Test error',
          currentFilter: GalleryFilter(minQualityScore: 80),
          storageStats: {'total': 5},
        );

        expect(errorState.currentFilter, previousState.currentFilter);
        expect(errorState.storageStats, previousState.storageStats);
      });
    });

    group('Event equality', () {
      test('LoadVideos events are equal', () {
        const event1 = LoadVideos();
        const event2 = LoadVideos();
        expect(event1, equals(event2));
      });

      test('DeleteVideo events are equal with same video', () {
        final video = sampleVideos.first;
        final event1 = DeleteVideo(video);
        final event2 = DeleteVideo(video);
        expect(event1, equals(event2));
      });

      test('ApplyFilter events are equal with same filter', () {
        const filter = GalleryFilter(minQualityScore: 80);
        const event1 = ApplyFilter(filter);
        const event2 = ApplyFilter(filter);
        expect(event1, equals(event2));
      });
    });

    group('State equality', () {
      test('VideoGalleryLoaded states are equal with same properties', () {
        const state1 = VideoGalleryLoaded(
          allVideos: [],
          filteredVideos: [],
        );
        const state2 = VideoGalleryLoaded(
          allVideos: [],
          filteredVideos: [],
        );
        expect(state1, equals(state2));
      });

      test('VideoGalleryError states are equal with same message', () {
        const state1 = VideoGalleryError(
          message: 'Test error',
        );
        const state2 = VideoGalleryError(
          message: 'Test error',
        );
        expect(state1, equals(state2));
      });
    });
  });
}
