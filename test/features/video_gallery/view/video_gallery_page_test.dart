import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/view/video_gallery_page.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_gallery_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

class MockVideoGalleryBloc
    extends MockBloc<VideoGalleryEvent, VideoGalleryState>
    implements VideoGalleryBloc {}

class MockGoRouter extends Mock implements GoRouter {}

void main() {
  group('VideoGalleryView', () {
    late MockVideoGalleryBloc mockBloc;
    late MockGoRouter mockGoRouter;

    setUp(() {
      mockBloc = MockVideoGalleryBloc();
      mockGoRouter = MockGoRouter();
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: InheritedGoRouter(
          goRouter: mockGoRouter,
          child: BlocProvider<VideoGalleryBloc>.value(
            value: mockBloc,
            child: const VideoGalleryView(),
          ),
        ),
      );
    }

    testWidgets('displays app bar with correct title',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('Video Gallery'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('displays refresh button in app bar',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    testWidgets('displays home button in app bar', (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byIcon(Icons.home), findsOneWidget);
    });

    testWidgets('displays filter button in app bar',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byIcon(Icons.filter_list), findsOneWidget);
    });

    testWidgets('home button has correct tooltip', (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      final homeButton = find.byIcon(Icons.home);
      expect(homeButton, findsOneWidget);

      // Verify tooltip
      final iconButton = tester.widget<IconButton>(
        find.ancestor(
          of: homeButton,
          matching: find.byType(IconButton),
        ),
      );
      expect(iconButton.tooltip, 'Home');
    });

    testWidgets('triggers RefreshVideos event when refresh button is tapped',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      await tester.tap(find.byIcon(Icons.refresh));

      verify(() => mockBloc.add(const RefreshVideos())).called(1);
    });

    testWidgets('shows filter snackbar when filter button is tapped',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pump();

      expect(find.text('Filter dialog coming soon!'), findsOneWidget);
    });
  });

  group('VideoGalleryContent', () {
    late MockVideoGalleryBloc mockBloc;
    late List<VideoItem> sampleVideos;

    setUp(() {
      mockBloc = MockVideoGalleryBloc();

      sampleVideos = [
        VideoItem(
          id: 'video-1',
          fileName: 'face_verification_2024-01-15_10-30-45.mp4',
          filePath: '/path/to/video1.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15, 10, 30, 45),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
        VideoItem(
          id: 'video-2',
          fileName: 'face_verification_2024-01-14_09-15-30.mp4',
          filePath: '/path/to/video2.mp4',
          fileSize: 2048000,
          createdAt: DateTime(2024, 1, 14, 9, 15, 30),
          duration: const Duration(seconds: 9),
          qualityScore: 92,
        ),
      ];
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: BlocProvider<VideoGalleryBloc>.value(
          value: mockBloc,
          child: const Scaffold(
            body: VideoGalleryContent(),
          ),
        ),
      );
    }

    testWidgets('displays loading widget for initial state',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryInitial());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(GalleryLoadingWidget), findsOneWidget);
    });

    testWidgets('displays loading widget for loading state',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryLoading());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(GalleryLoadingWidget), findsOneWidget);
    });

    testWidgets('displays error widget for error state',
        (WidgetTester tester) async {
      const errorMessage = 'Failed to load videos';
      when(() => mockBloc.state).thenReturn(
        const VideoGalleryError(
          message: errorMessage,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(GalleryErrorWidget), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets(
        'triggers LoadVideos event when retry button is tapped in error state',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        const VideoGalleryError(
          message: 'Failed to load videos',
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      await tester.tap(find.text('Retry'));

      verify(() => mockBloc.add(const LoadVideos())).called(1);
    });

    testWidgets('displays empty widget for empty state',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(const VideoGalleryEmpty());

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(EmptyGalleryWidget), findsOneWidget);
    });

    testWidgets('displays video grid for loaded state with videos',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(VideoGalleryGrid), findsOneWidget);
    });
  });

  group('VideoGalleryGrid', () {
    late MockVideoGalleryBloc mockBloc;
    late MockGoRouter mockGoRouter;
    late List<VideoItem> sampleVideos;

    setUp(() {
      mockBloc = MockVideoGalleryBloc();
      mockGoRouter = MockGoRouter();

      sampleVideos = [
        VideoItem(
          id: 'video-1',
          fileName: 'face_verification_2024-01-15_10-30-45.mp4',
          filePath: '/path/to/video1.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15, 10, 30, 45),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
        VideoItem(
          id: 'video-2',
          fileName: 'face_verification_2024-01-14_09-15-30.mp4',
          filePath: '/path/to/video2.mp4',
          fileSize: 2048000,
          createdAt: DateTime(2024, 1, 14, 9, 15, 30),
          duration: const Duration(seconds: 9),
          qualityScore: 92,
        ),
      ];
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: InheritedGoRouter(
          goRouter: mockGoRouter,
          child: BlocProvider<VideoGalleryBloc>.value(
            value: mockBloc,
            child: const Scaffold(
              body: VideoGalleryGrid(),
            ),
          ),
        ),
      );
    }

    testWidgets('displays empty gallery widget when no videos',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        const VideoGalleryLoaded(
          allVideos: [],
          filteredVideos: [],
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(EmptyGalleryWidget), findsOneWidget);
    });

    testWidgets('displays grid of video thumbnails when videos exist',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(GridView), findsOneWidget);
      expect(find.byType(VideoThumbnailWidget), findsNWidgets(2));
    });

    testWidgets('navigates to video player when video thumbnail is tapped',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
      );

      // Mock GoRouter navigation
      when(() => mockGoRouter.go(any(), extra: any(named: 'extra')))
          .thenReturn(null);

      await tester.pumpWidget(createWidgetUnderTest());

      // Tap on the first video thumbnail
      await tester.tap(find.byType(VideoThumbnailWidget).first);
      await tester.pumpAndSettle();

      // Verify navigation was called
      verify(() => mockGoRouter.go(any(), extra: any(named: 'extra')))
          .called(1);
    });

    testWidgets('triggers DeleteVideo event when delete button is tapped',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      // Find and tap the delete button on the first video
      await tester.tap(find.byIcon(Icons.delete).first);
      await tester.pumpAndSettle();

      // Confirm deletion in the dialog
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      verify(() => mockBloc.add(DeleteVideo(sampleVideos.first))).called(1);
    });

    testWidgets('grid has correct configuration', (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: sampleVideos,
          filteredVideos: sampleVideos,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      final gridView = tester.widget<GridView>(find.byType(GridView));
      final delegate =
          gridView.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount;

      expect(delegate.crossAxisCount, 2);
      expect(delegate.crossAxisSpacing, 16.0);
      expect(delegate.mainAxisSpacing, 16.0);
      expect(delegate.childAspectRatio, 0.8);
    });

    testWidgets('handles large number of videos', (WidgetTester tester) async {
      // Create a large list of videos
      final largeVideoList = List.generate(
        20,
        (index) => VideoItem(
          id: 'video-$index',
          fileName: 'video_$index.mp4',
          filePath: '/path/to/video$index.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
      );

      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: largeVideoList,
          filteredVideos: largeVideoList,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(GridView), findsOneWidget);

      // Should display all videos (though not all may be visible without
      // scrolling)
      final gridView = tester.widget<GridView>(find.byType(GridView));
      expect(gridView.semanticChildCount, 20);
    });

    testWidgets('maintains scroll position when state updates',
        (WidgetTester tester) async {
      final largeVideoList = List.generate(
        20,
        (index) => VideoItem(
          id: 'video-$index',
          fileName: 'video_$index.mp4',
          filePath: '/path/to/video$index.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
      );

      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: largeVideoList,
          filteredVideos: largeVideoList,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      // Scroll down
      await tester.drag(find.byType(GridView), const Offset(0, -300));
      await tester.pumpAndSettle();

      // Verify we scrolled (this is a basic check)
      expect(find.byType(GridView), findsOneWidget);
    });
  });

  group('State transitions', () {
    late MockVideoGalleryBloc mockBloc;

    setUp(() {
      mockBloc = MockVideoGalleryBloc();
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: BlocProvider<VideoGalleryBloc>.value(
          value: mockBloc,
          child: const VideoGalleryView(),
        ),
      );
    }

    testWidgets('handles state transition from loading to loaded',
        (WidgetTester tester) async {
      whenListen(
        mockBloc,
        Stream.fromIterable([
          const VideoGalleryLoading(),
          VideoGalleryLoaded(
            allVideos: [
              VideoItem(
                id: 'video-1',
                fileName: 'video.mp4',
                filePath: '/path/video.mp4',
                fileSize: 1024000,
                createdAt: DateTime(2024, 1, 15),
                duration: const Duration(seconds: 9),
                qualityScore: 85.5,
              ),
            ],
            filteredVideos: [
              VideoItem(
                id: 'video-1',
                fileName: 'video.mp4',
                filePath: '/path/video.mp4',
                fileSize: 1024000,
                createdAt: DateTime(2024, 1, 15),
                duration: const Duration(seconds: 9),
                qualityScore: 85.5,
              ),
            ],
          ),
        ]),
        initialState: const VideoGalleryLoading(),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      // Initially shows loading
      expect(find.byType(GalleryLoadingWidget), findsOneWidget);

      // After state change, shows grid
      await tester.pump();
      expect(find.byType(VideoGalleryGrid), findsOneWidget);
    });

    testWidgets('handles state transition from loading to error',
        (WidgetTester tester) async {
      whenListen(
        mockBloc,
        Stream.fromIterable([
          const VideoGalleryLoading(),
          const VideoGalleryError(
            message: 'Network error',
          ),
        ]),
        initialState: const VideoGalleryLoading(),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      // Initially shows loading
      expect(find.byType(GalleryLoadingWidget), findsOneWidget);

      // After state change, shows error
      await tester.pump();
      expect(find.byType(GalleryErrorWidget), findsOneWidget);
      expect(find.text('Network error'), findsOneWidget);
    });

    testWidgets('handles state transition from error to loaded',
        (WidgetTester tester) async {
      whenListen(
        mockBloc,
        Stream.fromIterable([
          const VideoGalleryError(
            message: 'Network error',
          ),
          VideoGalleryLoaded(
            allVideos: [
              VideoItem(
                id: 'video-1',
                fileName: 'video.mp4',
                filePath: '/path/video.mp4',
                fileSize: 1024000,
                createdAt: DateTime(2024, 1, 15),
                duration: const Duration(seconds: 9),
                qualityScore: 85.5,
              ),
            ],
            filteredVideos: [
              VideoItem(
                id: 'video-1',
                fileName: 'video.mp4',
                filePath: '/path/video.mp4',
                fileSize: 1024000,
                createdAt: DateTime(2024, 1, 15),
                duration: const Duration(seconds: 9),
                qualityScore: 85.5,
              ),
            ],
          ),
        ]),
        initialState: const VideoGalleryError(
          message: 'Network error',
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      // Initially shows error
      expect(find.byType(GalleryErrorWidget), findsOneWidget);

      // After state change, shows grid
      await tester.pump();
      expect(find.byType(VideoGalleryGrid), findsOneWidget);
    });
  });

  group('Edge cases', () {
    late MockVideoGalleryBloc mockBloc;

    setUp(() {
      mockBloc = MockVideoGalleryBloc();
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: BlocProvider<VideoGalleryBloc>.value(
          value: mockBloc,
          child: const VideoGalleryView(),
        ),
      );
    }

    testWidgets('handles null video list gracefully',
        (WidgetTester tester) async {
      when(() => mockBloc.state).thenReturn(
        const VideoGalleryLoaded(
          allVideos: [],
          filteredVideos: [],
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(EmptyGalleryWidget), findsOneWidget);
    });

    testWidgets('handles very long error messages',
        (WidgetTester tester) async {
      const longErrorMessage =
          'This is a very long error message that should be displayed '
          'properly in the error widget even if it spans multiple lines and '
          'contains a lot of text that might cause layout issues';

      when(() => mockBloc.state).thenReturn(
        const VideoGalleryError(
          message: longErrorMessage,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(GalleryErrorWidget), findsOneWidget);
      expect(find.textContaining('very long error message'), findsOneWidget);
    });

    testWidgets('handles videos with extreme file sizes',
        (WidgetTester tester) async {
      final extremeVideos = [
        VideoItem(
          id: 'tiny-video',
          fileName: 'tiny.mp4',
          filePath: '/path/tiny.mp4',
          fileSize: 1, // 1 byte
          createdAt: DateTime(2024, 1, 15),
          duration: const Duration(seconds: 1),
          qualityScore: 50,
        ),
        VideoItem(
          id: 'huge-video',
          fileName: 'huge.mp4',
          filePath: '/path/huge.mp4',
          fileSize: 999999999999, // Very large
          createdAt: DateTime(2024, 1, 15),
          duration: const Duration(hours: 2),
          qualityScore: 95,
        ),
      ];

      when(() => mockBloc.state).thenReturn(
        VideoGalleryLoaded(
          allVideos: extremeVideos,
          filteredVideos: extremeVideos,
        ),
      );

      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(VideoGalleryGrid), findsOneWidget);
      expect(find.byType(VideoThumbnailWidget), findsNWidgets(2));
    });
  });
}
