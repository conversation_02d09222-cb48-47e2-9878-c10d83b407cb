import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockVideoGalleryService extends Mo<PERSON> implements VideoGalleryService {}

void main() {
  group('VideoGalleryRepository', () {
    late VideoGalleryRepository repository;
    late MockVideoGalleryService mockVideoGalleryService;
    late List<VideoItem> sampleVideos;

    setUp(() {
      mockVideoGalleryService = MockVideoGalleryService();
      repository = VideoGalleryRepository(
        videoGalleryService: mockVideoGalleryService,
      );

      sampleVideos = [
        VideoItem(
          id: 'video-1',
          fileName: 'face_verification_2024-01-15_10-30-45.mp4',
          filePath: '/path/to/video1.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15, 10, 30, 45),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
        VideoItem(
          id: 'video-2',
          fileName: 'face_verification_2024-01-14_09-15-30.mp4',
          filePath: '/path/to/video2.mp4',
          fileSize: 2048000,
          createdAt: DateTime(2024, 1, 14, 9, 15, 30),
          duration: const Duration(seconds: 9),
          qualityScore: 92,
        ),
        VideoItem(
          id: 'video-3',
          fileName: 'face_verification_2024-01-16_14-45-20.mp4',
          filePath: '/path/to/video3.mp4',
          fileSize: 1536000,
          createdAt: DateTime(2024, 1, 16, 14, 45, 20),
          duration: const Duration(seconds: 9),
          qualityScore: 78.3,
        ),
      ];
    });

    group('loadVideos', () {
      test('should load videos successfully', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenAnswer((_) async => sampleVideos);

        final result = await repository.loadVideos();

        expect(result, sampleVideos);
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });

      test('should return empty list when no videos exist', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenAnswer((_) async => <VideoItem>[]);

        final result = await repository.loadVideos();

        expect(result, isEmpty);
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });

      test('should handle service errors', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenThrow(Exception('Service error'));

        expect(
          () => repository.loadVideos(),
          throwsException,
        );
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });
    });

    group('filterVideos', () {
      test('should return all videos with default filter', () {
        const filter = GalleryFilter();

        // Mock the service filterVideos method
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[2], // video-3 (2024-01-16)
          sampleVideos[0], // video-1 (2024-01-15)
          sampleVideos[1], // video-2 (2024-01-14)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        // Should be sorted by date descending (default)
        expect(result[0].id, 'video-3'); // 2024-01-16
        expect(result[1].id, 'video-1'); // 2024-01-15
        expect(result[2].id, 'video-2'); // 2024-01-14
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should sort by date ascending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.dateAscending);

        // Mock the service filterVideos method
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[1], // video-2 (2024-01-14)
          sampleVideos[0], // video-1 (2024-01-15)
          sampleVideos[2], // video-3 (2024-01-16)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result[0].id, 'video-2'); // 2024-01-14
        expect(result[1].id, 'video-1'); // 2024-01-15
        expect(result[2].id, 'video-3'); // 2024-01-16
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should sort by quality descending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.qualityDescending);

        // Mock the service filterVideos method
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[1], // video-2 (92.0)
          sampleVideos[0], // video-1 (85.5)
          sampleVideos[2], // video-3 (78.3)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result[0].qualityScore, 92.0); // video-2
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result[2].qualityScore, 78.3); // video-3
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should sort by quality ascending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.qualityAscending);

        // Mock the service filterVideos method
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[2], // video-3 (78.3)
          sampleVideos[0], // video-1 (85.5)
          sampleVideos[1], // video-2 (92.0)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result[0].qualityScore, 78.3); // video-3
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result[2].qualityScore, 92.0); // video-2
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should filter by high quality', () {
        const filter = GalleryFilter(minQualityScore: 80);

        // Mock the service filterVideos method - return videos >= 80 quality
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[1], // video-2 (92.0)
          sampleVideos[0], // video-1 (85.5)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result.every((video) => video.qualityScore >= 80), true);
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should filter by medium quality', () {
        const filter = GalleryFilter(minQualityScore: 60);

        // Mock the service filterVideos method - return all videos (all >= 60)
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[2], // video-3 (2024-01-16)
          sampleVideos[0], // video-1 (2024-01-15)
          sampleVideos[1], // video-2 (2024-01-14)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        expect(result.every((video) => video.qualityScore >= 60), true);
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should filter by low quality', () {
        final lowQualityVideos = [
          ...sampleVideos,
          VideoItem(
            id: 'video-4',
            fileName: 'low_quality.mp4',
            filePath: '/path/to/video4.mp4',
            fileSize: 512000,
            createdAt: DateTime(2024, 1, 17),
            duration: const Duration(seconds: 9),
            qualityScore: 45,
          ),
        ];

        const filter = GalleryFilter(minQualityScore: 0);

        // Mock the service filterVideos method - return all videos (all >= 0)
        when(
          () => mockVideoGalleryService.filterVideos(lowQualityVideos, filter),
        ).thenReturn([
          lowQualityVideos[3], // video-4 (2024-01-17)
          lowQualityVideos[2], // video-3 (2024-01-16)
          lowQualityVideos[0], // video-1 (2024-01-15)
          lowQualityVideos[1], // video-2 (2024-01-14)
        ]);

        final result = repository.filterVideos(lowQualityVideos, filter);

        expect(result.length, 4);
        expect(result.every((video) => video.qualityScore >= 0), true);
        verify(
          () => mockVideoGalleryService.filterVideos(lowQualityVideos, filter),
        ).called(1);
      });

      test('should filter by date range', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 14),
          end: DateTime(2024, 1, 15),
        );
        final filter = GalleryFilter(dateRange: dateRange);

        // Mock the service filterVideos method - return videos within range
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[0], // video-1 (2024-01-15)
          sampleVideos[1], // video-2 (2024-01-14)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result.any((video) => video.id == 'video-1'), true);
        expect(result.any((video) => video.id == 'video-2'), true);
        expect(result.any((video) => video.id == 'video-3'), false);
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should apply multiple filters', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 14),
          end: DateTime(2024, 1, 16),
        );
        final filter = GalleryFilter(
          sortBy: GallerySortBy.qualityDescending,
          minQualityScore: 80,
          dateRange: dateRange,
        );

        // Mock the service filterVideos method - return filtered and sorted
        when(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .thenReturn([
          sampleVideos[1], // video-2 (92.0 quality, 2024-01-14)
          sampleVideos[0], // video-1 (85.5 quality, 2024-01-15)
        ]);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result[0].qualityScore, 92.0); // video-2 (highest quality)
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result.every((video) => video.qualityScore >= 80), true);
        verify(() => mockVideoGalleryService.filterVideos(sampleVideos, filter))
            .called(1);
      });

      test('should handle empty video list', () {
        const filter = GalleryFilter();

        // Mock the service filterVideos method - return empty list
        when(() => mockVideoGalleryService.filterVideos(<VideoItem>[], filter))
            .thenReturn(<VideoItem>[]);

        final result = repository.filterVideos([], filter);

        expect(result, isEmpty);
        verify(
          () => mockVideoGalleryService.filterVideos(<VideoItem>[], filter),
        ).called(1);
      });
    });

    group('deleteVideo', () {
      test('should delete video successfully', () async {
        final video = sampleVideos.first;
        when(() => mockVideoGalleryService.deleteVideo(video))
            .thenAnswer((_) async => true);

        final result = await repository.deleteVideo(video);

        expect(result, true);
        verify(() => mockVideoGalleryService.deleteVideo(video)).called(1);
      });

      test('should return false when deletion fails', () async {
        final video = sampleVideos.first;
        when(() => mockVideoGalleryService.deleteVideo(video))
            .thenAnswer((_) async => false);

        final result = await repository.deleteVideo(video);

        expect(result, false);
        verify(() => mockVideoGalleryService.deleteVideo(video)).called(1);
      });

      test('should handle service errors during deletion', () async {
        final video = sampleVideos.first;
        when(() => mockVideoGalleryService.deleteVideo(video))
            .thenThrow(Exception('Delete failed'));

        final result = await repository.deleteVideo(video);

        expect(result, false);
        verify(() => mockVideoGalleryService.deleteVideo(video)).called(1);
      });
    });

    group('generateThumbnail', () {
      test('should generate thumbnail successfully', () async {
        final video = sampleVideos[0];

        when(() => mockVideoGalleryService.generateThumbnail(video))
            .thenAnswer((_) async => '/path/to/thumbnail.jpg');

        final result = await repository.generateThumbnail(video);

        expect(result, isNotNull);
        expect(result!.thumbnailPath, '/path/to/thumbnail.jpg');
        verify(() => mockVideoGalleryService.generateThumbnail(video))
            .called(1);
      });

      test('should return null when thumbnail generation fails', () async {
        final video = sampleVideos[0];

        when(() => mockVideoGalleryService.generateThumbnail(video))
            .thenAnswer((_) async => null);

        final result = await repository.generateThumbnail(video);

        expect(result, isNull);
        verify(() => mockVideoGalleryService.generateThumbnail(video))
            .called(1);
      });

      test('should handle service errors during thumbnail generation',
          () async {
        final video = sampleVideos[0];

        when(() => mockVideoGalleryService.generateThumbnail(video))
            .thenThrow(Exception('Thumbnail generation failed'));

        final result = await repository.generateThumbnail(video);

        expect(result, isNull);
        verify(() => mockVideoGalleryService.generateThumbnail(video))
            .called(1);
      });
    });

    group('getStorageStats', () {
      test('should get storage stats successfully', () async {
        final expectedStats = {
          'totalVideos': 3,
          'totalSize': 4608000,
          'averageQuality': 85.27,
          'oldestVideo': DateTime(2024, 1, 14),
          'newestVideo': DateTime(2024, 1, 16),
        };

        when(() => mockVideoGalleryService.getStorageStats())
            .thenAnswer((_) async => expectedStats);

        final result = await repository.getStorageStats();

        expect(result, expectedStats);
        verify(() => mockVideoGalleryService.getStorageStats()).called(1);
      });

      test('should handle empty storage stats', () async {
        final expectedStats = {
          'totalVideos': 0,
          'totalSize': 0,
          'averageQuality': 0.0,
          'oldestVideo': null,
          'newestVideo': null,
        };

        when(() => mockVideoGalleryService.getStorageStats())
            .thenAnswer((_) async => expectedStats);

        final result = await repository.getStorageStats();

        expect(result, expectedStats);
        verify(() => mockVideoGalleryService.getStorageStats()).called(1);
      });

      test('should handle service errors during stats retrieval', () async {
        when(() => mockVideoGalleryService.getStorageStats())
            .thenThrow(Exception('Stats retrieval failed'));

        final result = await repository.getStorageStats();

        expect(result, {
          'totalVideos': 0,
          'totalSize': 0,
          'averageQuality': 0.0,
          'oldestVideo': null,
          'newestVideo': null,
        });
        verify(() => mockVideoGalleryService.getStorageStats()).called(1);
      });
    });

    group('refreshVideos', () {
      test('should refresh videos successfully', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenAnswer((_) async => sampleVideos);

        final result = await repository.refreshVideos();

        expect(result, sampleVideos);
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });
    });
  });
}
