import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoItem', () {
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 15, 10, 30, 45);
    });

    test('should create with all required parameters', () {
      final videoItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 9),
        qualityScore: 85.5,
      );

      expect(videoItem.id, 'test-id-123');
      expect(videoItem.fileName, 'face_verification_2024-01-15_10-30-45.mp4');
      expect(videoItem.filePath, '/path/to/video.mp4');
      expect(videoItem.fileSize, 1024000);
      expect(videoItem.createdAt, testDate);
      expect(videoItem.duration, const Duration(seconds: 9));
      expect(videoItem.qualityScore, 85.5);
      expect(videoItem.thumbnailPath, isNull);
    });

    test('should create with optional parameters', () {
      final videoItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 9),
        qualityScore: 85.5,
        thumbnailPath: '/path/to/thumbnail.jpg',
      );

      expect(videoItem.thumbnailPath, '/path/to/thumbnail.jpg');
      expect(videoItem.duration, const Duration(seconds: 9));
    });

    test('should support copyWith method', () {
      final originalItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 5),
        qualityScore: 85.5,
      );

      final updatedItem = originalItem.copyWith(
        thumbnailPath: '/path/to/thumbnail.jpg',
        duration: const Duration(seconds: 9),
        qualityScore: 90,
      );

      expect(updatedItem.id, originalItem.id);
      expect(updatedItem.fileName, originalItem.fileName);
      expect(updatedItem.filePath, originalItem.filePath);
      expect(updatedItem.fileSize, originalItem.fileSize);
      expect(updatedItem.createdAt, originalItem.createdAt);
      expect(updatedItem.thumbnailPath, '/path/to/thumbnail.jpg');
      expect(updatedItem.duration, const Duration(seconds: 9));
      expect(updatedItem.qualityScore, 90.0);
    });

    test('should support equality comparison', () {
      final item1 = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 9),
        qualityScore: 85.5,
      );

      final item2 = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 9),
        qualityScore: 85.5,
      );

      final item3 = VideoItem(
        id: 'different-id',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 9),
        qualityScore: 85.5,
      );

      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
    });

    test('should have proper props for equality', () {
      final videoItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        duration: const Duration(seconds: 9),
        qualityScore: 85.5,
        thumbnailPath: '/path/to/thumbnail.jpg',
      );

      expect(videoItem.props, [
        'test-id-123',
        '/path/to/video.mp4',
        'face_verification_2024-01-15_10-30-45.mp4',
        testDate,
        const Duration(seconds: 9),
        1024000,
        85.5,
        '/path/to/thumbnail.jpg',
      ]);
    });

    test('should handle edge case values', () {
      final futureDate = DateTime(2030, 12, 31, 23, 59, 59);

      final videoItem = VideoItem(
        id: '',
        fileName: '',
        filePath: '',
        fileSize: 0,
        createdAt: futureDate,
        duration: Duration.zero,
        qualityScore: 0,
      );

      expect(videoItem.id, '');
      expect(videoItem.fileName, '');
      expect(videoItem.filePath, '');
      expect(videoItem.fileSize, 0);
      expect(videoItem.createdAt, futureDate);
      expect(videoItem.duration, Duration.zero);
      expect(videoItem.qualityScore, 0.0);
    });

    test('should handle maximum values', () {
      final maxDate = DateTime(9999, 12, 31, 23, 59, 59);

      final videoItem = VideoItem(
        id: 'very-long-id-with-many-characters-to-test-edge-cases',
        fileName:
            'very_long_file_name_with_many_characters_to_test_edge_cases.mp4',
        filePath: '/very/long/path/to/test/edge/cases/video.mp4',
        fileSize: 9223372036854775807, // Max int64
        createdAt: maxDate,
        duration: const Duration(hours: 24),
        qualityScore: 100,
      );

      expect(videoItem.fileSize, 9223372036854775807);
      expect(videoItem.qualityScore, 100.0);
      expect(videoItem.duration, const Duration(hours: 24));
    });

    test('should handle negative quality score', () {
      final videoItem = VideoItem(
        id: 'test-id',
        fileName: 'test.mp4',
        filePath: '/path/test.mp4',
        fileSize: 1000,
        createdAt: testDate,
        duration: const Duration(seconds: 5),
        qualityScore: -10,
      );

      expect(videoItem.qualityScore, -10.0);
    });

    test('should handle very small file size', () {
      final videoItem = VideoItem(
        id: 'test-id',
        fileName: 'tiny.mp4',
        filePath: '/path/tiny.mp4',
        fileSize: 1,
        createdAt: testDate,
        duration: const Duration(seconds: 1),
        qualityScore: 50,
      );

      expect(videoItem.fileSize, 1);
    });

    test('should handle zero duration', () {
      final videoItem = VideoItem(
        id: 'test-id',
        fileName: 'instant.mp4',
        filePath: '/path/instant.mp4',
        fileSize: 1000,
        createdAt: testDate,
        duration: Duration.zero,
        qualityScore: 75,
      );

      expect(videoItem.duration, Duration.zero);
    });
  });

  group('Formatted methods', () {
    late VideoItem videoItem;

    setUp(() {
      videoItem = VideoItem(
        id: 'test-id',
        fileName: 'test_video.mp4',
        filePath: '/path/to/test_video.mp4',
        fileSize: 1048576, // 1 MB
        createdAt: DateTime(2024, 3, 15, 14, 30, 45),
        duration: const Duration(minutes: 2, seconds: 30),
        qualityScore: 87.65,
      );
    });

    test('should format file size correctly', () {
      // Test bytes
      final bytesVideo = videoItem.copyWith(fileSize: 512);
      expect(bytesVideo.formattedFileSize, '512B');

      // Test KB
      final kbVideo = videoItem.copyWith(fileSize: 2048);
      expect(kbVideo.formattedFileSize, '2.0KB');

      // Test MB
      final mbVideo = videoItem.copyWith(fileSize: 1048576);
      expect(mbVideo.formattedFileSize, '1.0MB');

      // Test large MB
      final largeMbVideo = videoItem.copyWith(fileSize: 2621440);
      expect(largeMbVideo.formattedFileSize, '2.5MB');

      // Test very large file
      final veryLargeVideo = videoItem.copyWith(fileSize: 1073741824);
      expect(veryLargeVideo.formattedFileSize, '1024.0MB');
    });

    test('should format duration correctly', () {
      // Test seconds only
      final secondsVideo =
          videoItem.copyWith(duration: const Duration(seconds: 45));
      expect(secondsVideo.formattedDuration, '00:45');

      // Test minutes and seconds
      final minutesVideo =
          videoItem.copyWith(duration: const Duration(minutes: 5, seconds: 30));
      expect(minutesVideo.formattedDuration, '05:30');

      // Test hours (should show in minutes)
      final hoursVideo = videoItem.copyWith(
        duration: const Duration(hours: 1, minutes: 30, seconds: 15),
      );
      expect(hoursVideo.formattedDuration, '90:15');

      // Test zero duration
      final zeroVideo = videoItem.copyWith(duration: Duration.zero);
      expect(zeroVideo.formattedDuration, '00:00');

      // Test exact minute
      final exactMinuteVideo =
          videoItem.copyWith(duration: const Duration(minutes: 3));
      expect(exactMinuteVideo.formattedDuration, '03:00');
    });

    test('should format date correctly', () {
      // Test different dates
      final janVideo = videoItem.copyWith(createdAt: DateTime(2024, 1, 5));
      expect(janVideo.formattedDate, '5/1/2024');

      final decVideo = videoItem.copyWith(createdAt: DateTime(2023, 12, 25));
      expect(decVideo.formattedDate, '25/12/2023');

      final leapVideo = videoItem.copyWith(createdAt: DateTime(2024, 2, 29));
      expect(leapVideo.formattedDate, '29/2/2024');
    });

    test('should format time correctly', () {
      // Test morning time
      final morningVideo =
          videoItem.copyWith(createdAt: DateTime(2024, 1, 15, 9, 5));
      expect(morningVideo.formattedTime, '09:05');

      // Test afternoon time
      final afternoonVideo =
          videoItem.copyWith(createdAt: DateTime(2024, 1, 15, 14, 30));
      expect(afternoonVideo.formattedTime, '14:30');

      // Test evening time
      final eveningVideo =
          videoItem.copyWith(createdAt: DateTime(2024, 1, 15, 23, 59));
      expect(eveningVideo.formattedTime, '23:59');

      // Test midnight
      final midnightVideo =
          videoItem.copyWith(createdAt: DateTime(2024, 1, 15));
      expect(midnightVideo.formattedTime, '00:00');
    });

    test('should format quality score correctly', () {
      // Test whole number
      final wholeVideo = videoItem.copyWith(qualityScore: 85);
      expect(wholeVideo.formattedQualityScore, '85.0%');

      // Test decimal
      final decimalVideo = videoItem.copyWith(qualityScore: 87.65);
      expect(decimalVideo.formattedQualityScore, '87.7%');

      // Test very low score
      final lowVideo = videoItem.copyWith(qualityScore: 0.1);
      expect(lowVideo.formattedQualityScore, '0.1%');

      // Test very high score
      final highVideo = videoItem.copyWith(qualityScore: 99.9);
      expect(highVideo.formattedQualityScore, '99.9%');

      // Test zero score
      final zeroVideo = videoItem.copyWith(qualityScore: 0);
      expect(zeroVideo.formattedQualityScore, '0.0%');

      // Test 100% score
      final perfectVideo = videoItem.copyWith(qualityScore: 100);
      expect(perfectVideo.formattedQualityScore, '100.0%');
    });

    test('should handle edge cases in formatting', () {
      // Test very small file size
      final tinyVideo = videoItem.copyWith(fileSize: 1);
      expect(tinyVideo.formattedFileSize, '1B');

      // Test exactly 1KB
      final exactKbVideo = videoItem.copyWith(fileSize: 1024);
      expect(exactKbVideo.formattedFileSize, '1.0KB');

      // Test exactly 1MB
      final exactMbVideo = videoItem.copyWith(fileSize: 1024 * 1024);
      expect(exactMbVideo.formattedFileSize, '1.0MB');

      // Test very long duration
      final longVideo = videoItem.copyWith(
        duration: const Duration(hours: 10, minutes: 30, seconds: 45),
      );
      expect(longVideo.formattedDuration, '630:45');

      // Test single digit seconds
      final singleSecondVideo =
          videoItem.copyWith(duration: const Duration(minutes: 1, seconds: 5));
      expect(singleSecondVideo.formattedDuration, '01:05');
    });
  });

  group('fromFile factory method', () {
    test('should create VideoItem from file path', () {
      // Note: This test verifies the method signature and parameters
      // In practice, this would work with actual files in integration tests

      // Test parameters for factory constructor
      // const filePath = '/path/to/test_video.mp4';
      // final createdAt = DateTime(2024, 1, 15, 10, 30);
      // const duration = Duration(seconds: 30);
      // const qualityScore = 85.5;
      // const thumbnailPath = '/path/to/thumbnail.jpg';

      // Verify that the factory constructor exists and accepts parameters
      expect(VideoItem.fromFile, isA<Function>());

      // In a real test with file system access, we would:
      // 1. Create a temporary test file
      // 2. Call VideoItem.fromFile
      // 3. Verify the resulting VideoItem properties
      // 4. Clean up the test file
    });
  });

  group('toString method', () {
    test('should provide meaningful string representation', () {
      final videoItem = VideoItem(
        id: 'test-123',
        fileName: 'sample_video.mp4',
        filePath: '/path/to/sample_video.mp4',
        fileSize: 2048000,
        createdAt: DateTime(2024, 1, 15, 14, 30),
        duration: const Duration(minutes: 1, seconds: 30),
        qualityScore: 87.5,
      );

      final string = videoItem.toString();

      expect(string, contains('VideoItem'));
      expect(string, contains('test-123'));
      expect(string, contains('sample_video.mp4'));
      expect(string, contains('01:30'));
      expect(string, contains('2.0MB'));
      expect(string, contains('87.5%'));
    });
  });
}
