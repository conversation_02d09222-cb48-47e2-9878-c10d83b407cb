import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('GalleryFilter', () {
    test('should create with default values', () {
      const filter = GalleryFilter();

      expect(filter.sortBy, GallerySortBy.dateDescending);
      expect(filter.minQualityScore, isNull);
      expect(filter.dateRange, isNull);
    });

    test('should create with custom values', () {
      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final filter = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange,
      );

      expect(filter.sortBy, GallerySortBy.qualityDescending);
      expect(filter.minQualityScore, 85.0);
      expect(filter.dateRange, dateRange);
    });

    test('should support copyWith method', () {
      const originalFilter = GalleryFilter(
        sortBy: GallerySortBy.dateAscending,
        minQualityScore: 70,
      );

      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );

      final updatedFilter = originalFilter.copyWith(
        sortBy: GallerySortBy.qualityDescending,
        dateRange: () => dateRange,
      );

      expect(updatedFilter.sortBy, GallerySortBy.qualityDescending);
      expect(updatedFilter.minQualityScore, 70.0); // Unchanged
      expect(updatedFilter.dateRange, dateRange);
    });

    test('should support copyWith with null values', () {
      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final originalFilter = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange,
      );

      final updatedFilter = originalFilter.copyWith(
        minQualityScore: () => null,
        dateRange: () => null,
      );

      expect(updatedFilter.sortBy, GallerySortBy.qualityDescending);
      expect(updatedFilter.minQualityScore, isNull);
      expect(updatedFilter.dateRange, isNull);
    });

    test('should clear all filters', () {
      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final filter = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange,
      );

      final clearedFilter = filter.clear();

      expect(clearedFilter.sortBy, GallerySortBy.dateDescending);
      expect(clearedFilter.minQualityScore, isNull);
      expect(clearedFilter.dateRange, isNull);
    });

    test('should detect active filters correctly', () {
      const noFilters = GalleryFilter();
      expect(noFilters.hasActiveFilters, false);

      const qualityFilter = GalleryFilter(minQualityScore: 80);
      expect(qualityFilter.hasActiveFilters, true);

      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final dateFilter = GalleryFilter(dateRange: dateRange);
      expect(dateFilter.hasActiveFilters, true);

      final bothFilters = GalleryFilter(
        minQualityScore: 80,
        dateRange: dateRange,
      );
      expect(bothFilters.hasActiveFilters, true);
    });

    test('should support equality comparison', () {
      final dateRange1 = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final dateRange2 = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );

      final filter1 = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange1,
      );
      final filter2 = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange2,
      );
      final filter3 = GalleryFilter(
        sortBy: GallerySortBy.dateAscending,
        minQualityScore: 85,
        dateRange: dateRange1,
      );

      expect(filter1, equals(filter2));
      expect(filter1, isNot(equals(filter3)));
    });

    test('should have proper props for equality', () {
      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final filter = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange,
      );

      expect(filter.props, [
        GallerySortBy.qualityDescending,
        85.0,
        dateRange,
      ]);
    });

    test('should have meaningful toString', () {
      final dateRange = DateRange(
        start: DateTime(2024),
        end: DateTime(2024, 1, 31),
      );
      final filter = GalleryFilter(
        sortBy: GallerySortBy.qualityDescending,
        minQualityScore: 85,
        dateRange: dateRange,
      );

      final string = filter.toString();
      expect(string, contains('GalleryFilter'));
      expect(string, contains('qualityDescending'));
      expect(string, contains('85.0'));
      expect(string, contains('DateRange'));
    });
  });

  group('GallerySortBy', () {
    test('should have correct display names', () {
      expect(GallerySortBy.dateDescending.displayName, 'Newest First');
      expect(GallerySortBy.dateAscending.displayName, 'Oldest First');
      expect(GallerySortBy.qualityDescending.displayName, 'Best Quality');
      expect(GallerySortBy.qualityAscending.displayName, 'Lowest Quality');
      expect(GallerySortBy.sizeDescending.displayName, 'Largest Files');
      expect(GallerySortBy.sizeAscending.displayName, 'Smallest Files');
    });

    test('should have correct icons', () {
      expect(GallerySortBy.dateDescending.icon, '📅');
      expect(GallerySortBy.dateAscending.icon, '📅');
      expect(GallerySortBy.qualityDescending.icon, '⭐');
      expect(GallerySortBy.qualityAscending.icon, '⭐');
      expect(GallerySortBy.sizeDescending.icon, '📁');
      expect(GallerySortBy.sizeAscending.icon, '📁');
    });

    test('should cover all enum values', () {
      // This test ensures we don't miss any enum values in switch statements
      for (final sortBy in GallerySortBy.values) {
        expect(sortBy.displayName, isNotEmpty);
        expect(sortBy.icon, isNotEmpty);
      }
    });
  });

  group('DateRange', () {
    late DateTime start;
    late DateTime end;

    setUp(() {
      start = DateTime(2024, 1, 15);
      end = DateTime(2024, 1, 20);
    });

    test('should create with start and end dates', () {
      final dateRange = DateRange(start: start, end: end);

      expect(dateRange.start, start);
      expect(dateRange.end, end);
    });

    test('should calculate duration correctly', () {
      final dateRange = DateRange(start: start, end: end);

      expect(dateRange.duration, const Duration(days: 5));
    });

    test('should check if date is contained in range', () {
      final dateRange = DateRange(start: start, end: end);

      // Dates within range
      expect(dateRange.contains(DateTime(2024, 1, 15)), true); // Start date
      expect(dateRange.contains(DateTime(2024, 1, 20)), true); // End date
      expect(dateRange.contains(DateTime(2024, 1, 17)), true); // Middle date

      // Dates outside range
      expect(dateRange.contains(DateTime(2024, 1, 14)), false); // Before start
      expect(dateRange.contains(DateTime(2024, 1, 21)), false); // After end
      expect(dateRange.contains(DateTime(2023, 12, 31)), false); // Much earlier
      expect(dateRange.contains(DateTime(2024, 2)), false); // Much later
    });

    test('should handle time components when checking containment', () {
      final dateRange = DateRange(
        start: DateTime(2024, 1, 15, 10, 30),
        end: DateTime(2024, 1, 20, 14, 45),
      );

      // Should ignore time components and only consider date
      expect(dateRange.contains(DateTime(2024, 1, 15, 23, 59)), true);
      expect(dateRange.contains(DateTime(2024, 1, 20)), true);
      expect(dateRange.contains(DateTime(2024, 1, 17, 12)), true);
    });

    test('should handle single day range', () {
      final sameDay = DateTime(2024, 1, 15);
      final dateRange = DateRange(start: sameDay, end: sameDay);

      expect(dateRange.contains(DateTime(2024, 1, 15)), true);
      expect(dateRange.contains(DateTime(2024, 1, 14)), false);
      expect(dateRange.contains(DateTime(2024, 1, 16)), false);
      expect(dateRange.duration, Duration.zero);
    });

    test('should generate correct description for single day', () {
      final sameDay = DateTime(2024, 1, 15);
      final dateRange = DateRange(start: sameDay, end: sameDay);

      expect(dateRange.description, '15/1/2024');
    });

    test('should generate correct description for date range', () {
      final dateRange = DateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      expect(dateRange.description, '15/1/2024 - 20/1/2024');
    });

    test('should generate correct description for different months', () {
      final dateRange = DateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 2, 5),
      );

      expect(dateRange.description, '15/1/2024 - 5/2/2024');
    });

    test('should generate correct description for different years', () {
      final dateRange = DateRange(
        start: DateTime(2023, 12, 25),
        end: DateTime(2024, 1, 5),
      );

      expect(dateRange.description, '25/12/2023 - 5/1/2024');
    });

    test('should support equality comparison', () {
      final dateRange1 = DateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );
      final dateRange2 = DateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );
      final dateRange3 = DateRange(
        start: DateTime(2024, 1, 16),
        end: DateTime(2024, 1, 20),
      );

      expect(dateRange1, equals(dateRange2));
      expect(dateRange1, isNot(equals(dateRange3)));
    });

    test('should have proper props for equality', () {
      final dateRange = DateRange(start: start, end: end);

      expect(dateRange.props, [start, end]);
    });

    test('should have meaningful toString', () {
      final dateRange = DateRange(start: start, end: end);

      final string = dateRange.toString();
      expect(string, contains('DateRange'));
      expect(string, contains('15/1/2024 - 20/1/2024'));
    });

    test('should handle edge cases with very close dates', () {
      final dateRange = DateRange(
        start: DateTime(2024, 1, 15, 23, 59, 59),
        end: DateTime(2024, 1, 16, 0, 0, 1),
      );

      expect(dateRange.contains(DateTime(2024, 1, 15)), true);
      expect(dateRange.contains(DateTime(2024, 1, 16)), true);
      expect(dateRange.contains(DateTime(2024, 1, 14)), false);
      expect(dateRange.contains(DateTime(2024, 1, 17)), false);
    });

    test('should handle leap year dates', () {
      final dateRange = DateRange(
        start: DateTime(2024, 2, 28), // 2024 is a leap year
        end: DateTime(2024, 3),
      );

      expect(dateRange.contains(DateTime(2024, 2, 28)), true);
      expect(dateRange.contains(DateTime(2024, 2, 29)), true); // Leap day
      expect(dateRange.contains(DateTime(2024, 3)), true);
      expect(dateRange.contains(DateTime(2024, 2, 27)), false);
      expect(dateRange.contains(DateTime(2024, 3, 2)), false);
    });

    test('should handle month boundaries correctly', () {
      final dateRange = DateRange(
        start: DateTime(2024, 1, 31),
        end: DateTime(2024, 2, 2),
      );

      expect(dateRange.contains(DateTime(2024, 1, 31)), true);
      expect(dateRange.contains(DateTime(2024, 2)), true);
      expect(dateRange.contains(DateTime(2024, 2, 2)), true);
      expect(dateRange.contains(DateTime(2024, 1, 30)), false);
      expect(dateRange.contains(DateTime(2024, 2, 3)), false);
    });

    test('should handle year boundaries correctly', () {
      final dateRange = DateRange(
        start: DateTime(2023, 12, 30),
        end: DateTime(2024, 1, 2),
      );

      expect(dateRange.contains(DateTime(2023, 12, 30)), true);
      expect(dateRange.contains(DateTime(2023, 12, 31)), true);
      expect(dateRange.contains(DateTime(2024)), true);
      expect(dateRange.contains(DateTime(2024, 1, 2)), true);
      expect(dateRange.contains(DateTime(2023, 12, 29)), false);
      expect(dateRange.contains(DateTime(2024, 1, 3)), false);
    });
  });
}
