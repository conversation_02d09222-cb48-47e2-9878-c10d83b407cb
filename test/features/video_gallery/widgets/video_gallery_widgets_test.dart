import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_gallery_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/helpers.dart';
import '../../../test_config.dart';

void main() {
  setupFlutterTestBindings();
  group('VideoThumbnailWidget', () {
    late VideoItem videoItem;

    setUp(() {
      videoItem = VideoItem(
        id: 'video-id-123',
        fileName: 'video.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: DateTime(2024, 1, 15, 10, 30, 45),
        duration: const Duration(seconds: 10),
        qualityScore: 85.5,
        thumbnailPath: '/path/to/thumbnail.jpg',
      );
    });

    testWidgets('displays video information', (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        VideoThumbnailWidget(video: videoItem),
      );

      // Check for duration badge
      expect(find.textContaining('00:10'), findsOneWidget);
      // Check for quality score
      expect(find.textContaining('85.5%'), findsOneWidget);
      // Check for time
      expect(find.textContaining('10:30'), findsOneWidget);
      // Check for date
      expect(find.textContaining('15/1/2024'), findsOneWidget);
    });

    testWidgets('displays play icon', (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        VideoThumbnailWidget(video: videoItem),
      );

      expect(find.byIcon(Icons.play_circle_outline), findsOneWidget);
    });

    testWidgets('displays delete button', (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        VideoThumbnailWidget(
          video: videoItem,
          onDelete: () {},
        ),
      );

      expect(find.byIcon(Icons.delete), findsOneWidget);
    });

    testWidgets('calls onDelete callback when delete is tapped',
        (WidgetTester tester) async {
      var deleteCalled = false;

      await tester.pumpWidgetWithMaterial(
        VideoThumbnailWidget(
          video: videoItem,
          onDelete: () {
            deleteCalled = true;
          },
        ),
      );

      await tester.tap(find.byIcon(Icons.delete));
      await tester.pumpAndSettle();

      // Confirm deletion in the dialog
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      expect(deleteCalled, true);
    });

    testWidgets('shows delete confirmation dialog',
        (WidgetTester tester) async {
      var deleteCalled = false;

      await tester.pumpWidgetWithMaterial(
        VideoThumbnailWidget(
          video: videoItem,
          onDelete: () {
            deleteCalled = true;
          },
        ),
      );

      await tester.tap(find.byIcon(Icons.delete));
      await tester.pumpAndSettle();

      expect(find.text('Delete Video'), findsOneWidget);
      expect(
        find.text(
          'Are you sure you want to delete this video? '
          'This action cannot be undone.',
        ),
        findsOneWidget,
      );
      expect(deleteCalled, false);

      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      expect(deleteCalled, true);
    });
  });

  group('EmptyGalleryWidget', () {
    testWidgets('displays no videos message', (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        const EmptyGalleryWidget(),
      );

      expect(find.text('No videos yet'), findsOneWidget);
      expect(
        find.text('Complete a face verification to see your videos here.'),
        findsOneWidget,
      );
    });

    testWidgets('displays create video button if onCreateVideo is provided',
        (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        EmptyGalleryWidget(onCreateVideo: () {}),
      );

      expect(find.text('Start Face Verification'), findsOneWidget);
    });

    testWidgets('calls onCreateVideo callback when button is tapped',
        (WidgetTester tester) async {
      var createVideoCalled = false;

      await tester.pumpWidgetWithMaterial(
        EmptyGalleryWidget(
          onCreateVideo: () {
            createVideoCalled = true;
          },
        ),
      );

      await tester.tap(find.text('Start Face Verification'));
      await tester.pumpAndSettle();

      expect(createVideoCalled, true);
    });
  });

  group('GalleryErrorWidget', () {
    testWidgets('displays error message', (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        const GalleryErrorWidget(
          message: 'An error occurred',
        ),
      );

      expect(find.text('Error loading videos'), findsOneWidget);
      expect(find.text('An error occurred'), findsOneWidget);
    });

    testWidgets('displays retry button if onRetry is provided',
        (WidgetTester tester) async {
      await tester.pumpWidgetWithMaterial(
        GalleryErrorWidget(
          message: 'An error occurred',
          onRetry: () {},
        ),
      );

      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('calls onRetry callback when button is tapped',
        (WidgetTester tester) async {
      var retryCalled = false;

      await tester.pumpWidgetWithMaterial(
        GalleryErrorWidget(
          message: 'An error occurred',
          onRetry: () {
            retryCalled = true;
          },
        ),
      );

      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      expect(retryCalled, true);
    });
  });

  group('GalleryLoadingWidget', () {
    testWidgets('displays loading indicator and message',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: GalleryLoadingWidget(),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading videos...'), findsOneWidget);
    });
  });
}
