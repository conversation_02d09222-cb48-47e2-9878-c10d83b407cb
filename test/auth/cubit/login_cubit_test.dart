import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/login_cubit.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('LoginCubit', () {
    late MockAuthRepository mockAuthRepository;
    late LoginCubit loginCubit;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      loginCubit = LoginCubit(mockAuthRepository);
    });

    tearDown(() {
      loginCubit.close();
    });

    group('constructor', () {
      test('initial state is correct', () {
        expect(
          loginCubit.state,
          const LoginState(),
        );
      });
    });

    group('emailChanged', () {
      blocTest<LoginCubit, LoginState>(
        'emits state with updated email when email is valid',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged('<EMAIL>'),
        expect: () => [
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<LoginCubit, LoginState>(
        'emits state with valid status when both email and password are valid',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..passwordChanged('Password123!')
            ..emailChanged('<EMAIL>');
        },
        expect: () => [
          const LoginState(
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.valid,
          ),
        ],
      );

      blocTest<LoginCubit, LoginState>(
        'emits state with invalid status when email is invalid',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged('invalid-email'),
        expect: () => [
          const LoginState(
            email: Email.dirty('invalid-email'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<LoginCubit, LoginState>(
        'handles multiple email changes',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..emailChanged('invalid')
            ..emailChanged('<EMAIL>')
            ..emailChanged('<EMAIL>');
        },
        expect: () => [
          const LoginState(
            email: Email.dirty('invalid'),
            status: FormStatus.invalid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        loginCubit.close();
        expect(
          () => loginCubit.emailChanged('<EMAIL>'),
          returnsNormally,
        );
      });
    });

    group('passwordChanged', () {
      blocTest<LoginCubit, LoginState>(
        'emits state with updated password when password is valid',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.passwordChanged('Password123!'),
        expect: () => [
          const LoginState(
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<LoginCubit, LoginState>(
        'emits state with invalid status when password is weak',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.passwordChanged('weak'),
        expect: () => [
          const LoginState(
            password: Password.dirty('weak'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<LoginCubit, LoginState>(
        'emits state with invalid status when password is too short',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.passwordChanged('short'),
        expect: () => [
          const LoginState(
            password: Password.dirty('short'),
            status: FormStatus.invalid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        loginCubit.close();
        expect(
          () => loginCubit.passwordChanged('Password123!'),
          returnsNormally,
        );
      });
    });

    group('emailTouched', () {
      blocTest<LoginCubit, LoginState>(
        'emits state with emailTouched set to true',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.emailTouched(),
        expect: () => [
          const LoginState(emailTouched: true),
        ],
      );

      test('does not emit when cubit is closed', () {
        loginCubit.close();
        expect(() => loginCubit.emailTouched(), returnsNormally);
      });
    });

    group('passwordTouched', () {
      blocTest<LoginCubit, LoginState>(
        'emits state with passwordTouched set to true',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) => cubit.passwordTouched(),
        expect: () => [
          const LoginState(passwordTouched: true),
        ],
      );

      test('does not emit when cubit is closed', () {
        loginCubit.close();
        expect(() => loginCubit.passwordTouched(), returnsNormally);
      });
    });

    group('logInWithCredentials', () {
      blocTest<LoginCubit, LoginState>(
        'does not proceed when form is invalid',
        build: () => LoginCubit(mockAuthRepository),
        act: (cubit) async {
          cubit.emailChanged('invalid-email');
          await cubit.logInWithCredentials();
        },
        expect: () => [
          const LoginState(
            email: Email.dirty('invalid-email'),
            status: FormStatus.invalid,
          ),
        ],
        verify: (cubit) {
          verifyNever(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: any(named: 'email'),
              password: any(named: 'password'),
            ),
          );
        },
      );

      blocTest<LoginCubit, LoginState>(
        'emits success state when login is successful',
        build: () {
          when(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: any(named: 'email'),
              password: any(named: 'password'),
            ),
          ).thenAnswer((_) async {});
          return LoginCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!');
          await cubit.logInWithCredentials();
        },
        expect: () => [
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.valid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.submissionInProgress,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.submissionSuccess,
          ),
        ],
        verify: (cubit) {
          verify(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: '<EMAIL>',
              password: 'Password123!',
            ),
          ).called(1);
        },
      );

      blocTest<LoginCubit, LoginState>(
        'emits failure state when login fails with '
        'LogInWithEmailAndPasswordFailure',
        build: () {
          when(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: any(named: 'email'),
              password: any(named: 'password'),
            ),
          ).thenThrow(
            const LogInWithEmailAndPasswordFailure('Invalid credentials'),
          );
          return LoginCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!');
          await cubit.logInWithCredentials();
        },
        expect: () => [
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.valid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.submissionInProgress,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.submissionFailure,
            errorMessage: 'Invalid credentials',
          ),
        ],
      );

      blocTest<LoginCubit, LoginState>(
        'handles generic exceptions',
        build: () {
          when(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: any(named: 'email'),
              password: any(named: 'password'),
            ),
          ).thenThrow(Exception('Network error'));
          return LoginCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!');
          await cubit.logInWithCredentials();
        },
        expect: () => [
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.valid,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.submissionInProgress,
          ),
          const LoginState(
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.submissionFailure,
            errorMessage: 'An unexpected error occurred. Please try again.',
          ),
        ],
      );

      test('does not proceed when cubit is closed', () async {
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123!');
        await loginCubit.close();

        expect(
          () => loginCubit.logInWithCredentials(),
          returnsNormally,
        );

        verifyNever(
          () => mockAuthRepository.logInWithEmailAndPassword(
            email: any(named: 'email'),
            password: any(named: 'password'),
          ),
        );
      });
    });

    group('form validation', () {
      test('form is valid when both email and password are valid', () {
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123!');

        expect(loginCubit.state.status, FormStatus.valid);
      });

      test('form is invalid when email is invalid', () {
        loginCubit
          ..emailChanged('invalid-email')
          ..passwordChanged('Password123!');

        expect(loginCubit.state.status, FormStatus.invalid);
      });

      test('form is invalid when password is invalid', () {
        loginCubit
          ..emailChanged('<EMAIL>')
          ..passwordChanged('weak');

        expect(loginCubit.state.status, FormStatus.invalid);
      });

      test('form is invalid when both email and password are invalid', () {
        loginCubit
          ..emailChanged('invalid-email')
          ..passwordChanged('weak');

        expect(loginCubit.state.status, FormStatus.invalid);
      });
    });

    group('edge cases', () {
      test('handles empty email and password', () {
        loginCubit
          ..emailChanged('')
          ..passwordChanged('');

        expect(loginCubit.state.status, FormStatus.invalid);
        expect(
          loginCubit.state.email.error,
          EmailValidationError.empty,
        );
        expect(
          loginCubit.state.password.error,
          PasswordValidationError.empty,
        );
      });

      test('handles special email formats', () {
        const specialEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in specialEmails) {
          loginCubit.emailChanged(email);
          expect(loginCubit.state.email.isValid, isTrue);
        }
      });

      blocTest<LoginCubit, LoginState>(
        'handles rapid form submissions',
        build: () {
          when(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: any(named: 'email'),
              password: any(named: 'password'),
            ),
          ).thenAnswer((_) async {});
          return LoginCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!');
          await Future.wait([
            cubit.logInWithCredentials(),
            cubit.logInWithCredentials(),
            cubit.logInWithCredentials(),
          ]);
        },
        verify: (cubit) {
          // Should only call repository once due to form status checks
          verify(
            () => mockAuthRepository.logInWithEmailAndPassword(
              email: '<EMAIL>',
              password: 'Password123!',
            ),
          ).called(1);
        },
      );
    });
  });
}
