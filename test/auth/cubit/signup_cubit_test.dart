import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/signup_cubit.dart';
import 'package:bloomg_flutter/auth/models/confirmed_password.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/name.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('SignupCubit', () {
    late MockAuthRepository mockAuthRepository;
    late SignupCubit signupCubit;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      signupCubit = SignupCubit(mockAuthRepository);
    });

    tearDown(() {
      signupCubit.close();
    });

    group('constructor', () {
      test('initial state is correct', () {
        expect(
          signupCubit.state,
          const SignupState(),
        );
      });
    });

    group('nameChanged', () {
      blocTest<SignupCubit, SignupState>(
        'emits state with updated name when name is valid',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.nameChanged('John Doe'),
        expect: () => [
          const SignupState(
            name: Name.dirty('John Doe'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'emits state with invalid status when name is too short',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.nameChanged('J'),
        expect: () => [
          const SignupState(
            name: Name.dirty('J'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'emits state with invalid status when name is empty',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.nameChanged(''),
        expect: () => [
          const SignupState(
            name: Name.dirty(),
            status: FormStatus.invalid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        signupCubit.close();
        expect(() => signupCubit.nameChanged('John Doe'), returnsNormally);
      });
    });

    group('emailChanged', () {
      blocTest<SignupCubit, SignupState>(
        'emits state with updated email when email is valid',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged('<EMAIL>'),
        expect: () => [
          const SignupState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'emits state with invalid status when email is invalid',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged('invalid-email'),
        expect: () => [
          const SignupState(
            email: Email.dirty('invalid-email'),
            status: FormStatus.invalid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        signupCubit.close();
        expect(
          () => signupCubit.emailChanged('<EMAIL>'),
          returnsNormally,
        );
      });
    });

    group('passwordChanged', () {
      blocTest<SignupCubit, SignupState>(
        'emits state with updated password when password is valid',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.passwordChanged('Password123!'),
        expect: () => [
          const SignupState(
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'updates confirmed password validation when password changes',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..confirmedPasswordChanged('Password123!')
            ..passwordChanged('NewPassword123!');
        },
        expect: () => [
          const SignupState(
            confirmedPassword: ConfirmedPassword.dirty(
              password: '',
              value: 'Password123!',
            ),
            status: FormStatus.invalid,
          ),
          const SignupState(
            password: Password.dirty('NewPassword123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'NewPassword123!',
              value: 'Password123!',
            ),
            status: FormStatus.invalid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        signupCubit.close();
        expect(
          () => signupCubit.passwordChanged('Password123!'),
          returnsNormally,
        );
      });
    });

    group('confirmedPasswordChanged', () {
      blocTest<SignupCubit, SignupState>(
        'emits state with matching confirmed password',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..passwordChanged('Password123!')
            ..confirmedPasswordChanged('Password123!');
        },
        expect: () => [
          const SignupState(
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'emits state with mismatched confirmed password',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..passwordChanged('Password123!')
            ..confirmedPasswordChanged('DifferentPassword!');
        },
        expect: () => [
          const SignupState(
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'DifferentPassword!',
            ),
            status: FormStatus.invalid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        signupCubit.close();
        expect(
          () => signupCubit.confirmedPasswordChanged('Password123!'),
          returnsNormally,
        );
      });
    });

    group('touched methods', () {
      blocTest<SignupCubit, SignupState>(
        'nameTouched emits state with nameTouched set to true',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.nameTouched(),
        expect: () => [
          const SignupState(nameTouched: true),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'emailTouched emits state with emailTouched set to true',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.emailTouched(),
        expect: () => [
          const SignupState(emailTouched: true),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'passwordTouched emits state with passwordTouched set to true',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.passwordTouched(),
        expect: () => [
          const SignupState(passwordTouched: true),
        ],
      );

      blocTest<SignupCubit, SignupState>(
        'confirmedPasswordTouched emits state with '
        'confirmedPasswordTouched set to true',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) => cubit.confirmedPasswordTouched(),
        expect: () => [
          const SignupState(confirmedPasswordTouched: true),
        ],
      );

      test('touched methods do not emit when cubit is closed', () {
        signupCubit.close();
        expect(() => signupCubit.nameTouched(), returnsNormally);
        expect(() => signupCubit.emailTouched(), returnsNormally);
        expect(() => signupCubit.passwordTouched(), returnsNormally);
        expect(() => signupCubit.confirmedPasswordTouched(), returnsNormally);
      });
    });

    group('signUpFormSubmitted', () {
      blocTest<SignupCubit, SignupState>(
        'does not proceed when form is invalid',
        build: () => SignupCubit(mockAuthRepository),
        act: (cubit) async {
          cubit.nameChanged('J'); // Too short
          await cubit.signUpFormSubmitted();
        },
        expect: () => [
          const SignupState(
            name: Name.dirty('J'),
            status: FormStatus.invalid,
          ),
        ],
        verify: (cubit) {
          verifyNever(
            () => mockAuthRepository.signUp(
              email: any(named: 'email'),
              password: any(named: 'password'),
              name: any(named: 'name'),
            ),
          );
        },
      );

      blocTest<SignupCubit, SignupState>(
        'emits success state when signup is successful',
        build: () {
          when(
            () => mockAuthRepository.signUp(
              email: any(named: 'email'),
              password: any(named: 'password'),
              name: any(named: 'name'),
            ),
          ).thenAnswer((_) async {});
          return SignupCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..nameChanged('John Doe')
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!')
            ..confirmedPasswordChanged('Password123!');
          await cubit.signUpFormSubmitted();
        },
        expect: () => [
          const SignupState(
            name: Name.dirty('John Doe'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.valid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.submissionInProgress,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.submissionSuccess,
          ),
        ],
        verify: (cubit) {
          verify(
            () => mockAuthRepository.signUp(
              email: '<EMAIL>',
              password: 'Password123!',
              name: 'John Doe',
            ),
          ).called(1);
        },
      );

      blocTest<SignupCubit, SignupState>(
        'emits failure state when signup fails with '
        'SignUpWithEmailAndPasswordFailure',
        build: () {
          when(
            () => mockAuthRepository.signUp(
              email: any(named: 'email'),
              password: any(named: 'password'),
              name: any(named: 'name'),
            ),
          ).thenThrow(
            const SignUpWithEmailAndPasswordFailure('Email already in use'),
          );
          return SignupCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..nameChanged('John Doe')
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123!')
            ..confirmedPasswordChanged('Password123!');
          await cubit.signUpFormSubmitted();
        },
        expect: () => [
          const SignupState(
            name: Name.dirty('John Doe'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            status: FormStatus.invalid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.valid,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.submissionInProgress,
          ),
          const SignupState(
            name: Name.dirty('John Doe'),
            email: Email.dirty('<EMAIL>'),
            password: Password.dirty('Password123!'),
            confirmedPassword: ConfirmedPassword.dirty(
              password: 'Password123!',
              value: 'Password123!',
            ),
            status: FormStatus.submissionFailure,
            errorMessage: 'Email already in use',
          ),
        ],
      );

      test('does not proceed when cubit is closed', () async {
        signupCubit
          ..nameChanged('John Doe')
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123!')
          ..confirmedPasswordChanged('Password123!');
        await signupCubit.close();

        expect(
          () => signupCubit.signUpFormSubmitted(),
          returnsNormally,
        );

        verifyNever(
          () => mockAuthRepository.signUp(
            email: any(named: 'email'),
            password: any(named: 'password'),
            name: any(named: 'name'),
          ),
        );
      });
    });
  });
}
