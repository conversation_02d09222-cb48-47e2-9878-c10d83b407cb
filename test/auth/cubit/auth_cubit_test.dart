import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('AuthCubit', () {
    late MockAuthRepository mockAuthRepository;
    late StreamController<UserModel> userStreamController;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      userStreamController = StreamController<UserModel>();

      // Setup default mock behavior
      when(() => mockAuthRepository.user)
          .thenAnswer((_) => userStreamController.stream);
      when(() => mockAuthRepository.currentUser)
          .thenReturn(const UserModel.empty());
    });

    tearDown(() {
      userStreamController.close();
    });

    group('constructor', () {
      test('initial state is AuthState.unknown', () {
        final authCubit = AuthCubit(mockAuthRepository);
        expect(authCubit.state, const AuthState.unknown());
        authCubit.close();
      });

      test('subscribes to user stream on initialization', () {
        final authCubit = AuthCubit(mockAuthRepository);
        verify(() => mockAuthRepository.user).called(1);
        authCubit.close();
      });
    });

    group('user stream handling', () {
      blocTest<AuthCubit, AuthState>(
        'emits authenticated state when user stream emits valid user',
        build: () => AuthCubit(mockAuthRepository),
        act: (cubit) {
          const user = UserModel(
            uid: 'test-uid',
            email: '<EMAIL>',
            displayName: 'Test User',
          );
          userStreamController.add(user);
        },
        expect: () => [
          const AuthState.authenticated(
            UserModel(
              uid: 'test-uid',
              email: '<EMAIL>',
              displayName: 'Test User',
            ),
          ),
        ],
      );

      blocTest<AuthCubit, AuthState>(
        'emits unauthenticated state when user stream emits empty user',
        build: () => AuthCubit(mockAuthRepository),
        act: (cubit) {
          userStreamController.add(const UserModel.empty());
        },
        expect: () => [
          const AuthState.unauthenticated(),
        ],
      );

      blocTest<AuthCubit, AuthState>(
        'handles multiple user state changes',
        build: () => AuthCubit(mockAuthRepository),
        act: (cubit) {
          // Start with empty user
          userStreamController.add(const UserModel.empty());

          // Then authenticate
          const user = UserModel(
            uid: 'test-uid',
            email: '<EMAIL>',
          );
          userStreamController
            ..add(user)

            // Then logout
            ..add(const UserModel.empty());
        },
        expect: () => [
          const AuthState.unauthenticated(),
          const AuthState.authenticated(
            UserModel(
              uid: 'test-uid',
              email: '<EMAIL>',
            ),
          ),
          const AuthState.unauthenticated(),
        ],
      );
    });

    group('user stream error handling', () {
      blocTest<AuthCubit, AuthState>(
        'handles user stream errors gracefully',
        build: () => AuthCubit(mockAuthRepository),
        act: (cubit) {
          userStreamController.addError(Exception('Stream error'));
        },
        expect: () => [
          const AuthState.unknown(),
        ],
        verify: (cubit) {
          // Should not crash and maintain unknown state after error
          expect(cubit.state, const AuthState.unknown());
        },
      );
    });

    group('getters', () {
      blocTest<AuthCubit, AuthState>(
        'isAuthenticated returns true when status is authenticated',
        build: () => AuthCubit(mockAuthRepository),
        act: (cubit) {
          const user = UserModel(uid: 'test-uid', email: '<EMAIL>');
          userStreamController.add(user);
        },
        expect: () => [
          const AuthState.authenticated(
            UserModel(uid: 'test-uid', email: '<EMAIL>'),
          ),
        ],
        verify: (cubit) {
          expect(cubit.isAuthenticated, isTrue);
          expect(cubit.isUnauthenticated, isFalse);
          expect(cubit.isUnknown, isFalse);
        },
      );

      blocTest<AuthCubit, AuthState>(
        'isUnauthenticated returns true when status is unauthenticated',
        build: () => AuthCubit(mockAuthRepository),
        act: (cubit) {
          userStreamController.add(const UserModel.empty());
        },
        expect: () => [
          const AuthState.unauthenticated(),
        ],
        verify: (cubit) {
          expect(cubit.isAuthenticated, isFalse);
          expect(cubit.isUnauthenticated, isTrue);
          expect(cubit.isUnknown, isFalse);
        },
      );

      test('isUnknown returns true when status is unknown', () {
        final authCubit = AuthCubit(mockAuthRepository);
        expect(authCubit.isAuthenticated, isFalse);
        expect(authCubit.isUnauthenticated, isFalse);
        expect(authCubit.isUnknown, isTrue);
        authCubit.close();
      });

      test('currentUser returns repository current user', () {
        const user = UserModel(uid: 'test-uid', email: '<EMAIL>');
        when(() => mockAuthRepository.currentUser).thenReturn(user);

        final authCubit = AuthCubit(mockAuthRepository);
        expect(authCubit.currentUser, user);
        verify(() => mockAuthRepository.currentUser).called(1);
        authCubit.close();
      });
    });

    group('lifecycle', () {
      test('cancels user subscription on close', () async {
        final authCubit = AuthCubit(mockAuthRepository);

        // Verify subscription is active
        expect(userStreamController.hasListener, isTrue);

        await authCubit.close();

        // Verify subscription is cancelled
        expect(userStreamController.hasListener, isFalse);
      });

      test('handles close during active stream', () async {
        final authCubit = AuthCubit(mockAuthRepository);

        // Add user while cubit is active
        const user = UserModel(uid: 'test-uid', email: '<EMAIL>');
        userStreamController.add(user);

        // Close immediately
        await authCubit.close();

        // Should not throw or cause issues
        expect(authCubit.isClosed, isTrue);
      });
    });

    group('edge cases', () {
      test('handles null user gracefully', () {
        final authCubit = AuthCubit(mockAuthRepository);

        // This shouldn't happen in practice, but test defensive programming
        expect(() => authCubit.state, returnsNormally);
        authCubit.close();
      });

      test('handles rapid user changes', () {
        final authCubit = AuthCubit(mockAuthRepository);

        // Rapidly change user states
        for (var i = 0; i < 10; i++) {
          if (i.isEven) {
            userStreamController.add(const UserModel.empty());
          } else {
            userStreamController.add(
              UserModel(uid: 'uid-$i', email: 'user$<EMAIL>'),
            );
          }
        }

        // Should handle all changes without issues
        expect(() => authCubit.state, returnsNormally);
        authCubit.close();
      });
    });
  });
}
