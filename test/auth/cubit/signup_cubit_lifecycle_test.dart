import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/signup_cubit.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('SignupCubit Lifecycle Management', () {
    late MockAuthRepository mockAuthRepository;
    late SignupCubit signupCubit;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      signupCubit = SignupCubit(mockAuthRepository);
    });

    tearDown(() {
      signupCubit.close();
    });

    group('State Emission After Close', () {
      test('should not emit states after close() is called', () async {
        // Close the cubit
        await signupCubit.close();

        // Verify cubit is closed
        expect(signupCubit.isClosed, isTrue);

        // Try to change form fields - should not emit any states
        signupCubit
          ..nameChanged('<PERSON>')
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123')
          ..confirmedPasswordChanged('Password123')
          ..nameTouched()
          ..emailTouched()
          ..passwordTouched()
          ..confirmedPasswordTouched();

        // No states should be emitted after close
        // The test passes if no exceptions are thrown
      });

      test('should handle async signup operations gracefully after close',
          () async {
        // Setup mock to simulate successful signup
        when(
          () => mockAuthRepository.signUp(
            email: any(named: 'email'),
            password: any(named: 'password'),
            name: any(named: 'name'),
          ),
        ).thenAnswer((_) async {
          // Simulate some delay
          await Future<void>.delayed(const Duration(milliseconds: 100));
        });

        // Set valid form data
        signupCubit
          ..nameChanged('John Doe')
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123')
          ..confirmedPasswordChanged('Password123');

        // Start signup process
        final signupFuture = signupCubit.signUpFormSubmitted();

        // Close cubit while signup is in progress
        await signupCubit.close();

        // Wait for signup to complete
        await signupFuture;

        // Verify no exceptions were thrown
        expect(signupCubit.isClosed, isTrue);
      });

      test('should handle signup errors gracefully after close', () async {
        // Setup mock to simulate signup failure
        when(
          () => mockAuthRepository.signUp(
            email: any(named: 'email'),
            password: any(named: 'password'),
            name: any(named: 'name'),
          ),
        ).thenThrow(Exception('Network error'));

        // Set valid form data
        signupCubit
          ..nameChanged('John Doe')
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123')
          ..confirmedPasswordChanged('Password123');

        // Start signup process
        final signupFuture = signupCubit.signUpFormSubmitted();

        // Close cubit while signup is in progress
        await signupCubit.close();

        // Wait for signup to complete (should handle error gracefully)
        await signupFuture;

        // Verify no exceptions were thrown
        expect(signupCubit.isClosed, isTrue);
      });
    });

    group('Normal Operation Before Close', () {
      test('should emit states normally when not closed', () {
        expect(signupCubit.isClosed, isFalse);

        // Test form field changes
        signupCubit.nameChanged('John Doe');
        expect(signupCubit.state.name.value, equals('John Doe'));

        signupCubit.emailChanged('<EMAIL>');
        expect(signupCubit.state.email.value, equals('<EMAIL>'));

        signupCubit.passwordChanged('Password123');
        expect(signupCubit.state.password.value, equals('Password123'));

        signupCubit.confirmedPasswordChanged('Password123');
        expect(
          signupCubit.state.confirmedPassword.value,
          equals('Password123'),
        );

        // Test touched states
        signupCubit.nameTouched();
        expect(signupCubit.state.nameTouched, isTrue);

        signupCubit.emailTouched();
        expect(signupCubit.state.emailTouched, isTrue);

        signupCubit.passwordTouched();
        expect(signupCubit.state.passwordTouched, isTrue);

        signupCubit.confirmedPasswordTouched();
        expect(signupCubit.state.confirmedPasswordTouched, isTrue);
      });

      blocTest<SignupCubit, SignupState>(
        'should handle successful signup flow normally',
        build: () {
          when(
            () => mockAuthRepository.signUp(
              email: any(named: 'email'),
              password: any(named: 'password'),
              name: any(named: 'name'),
            ),
          ).thenAnswer((_) async {});
          return SignupCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit
            ..nameChanged('John Doe')
            ..emailChanged('<EMAIL>')
            ..passwordChanged('Password123')
            ..confirmedPasswordChanged('Password123');
          await cubit.signUpFormSubmitted();
        },
        expect: () => [
          // Name change
          isA<SignupState>().having((s) => s.name.value, 'name', 'John Doe'),
          // Email change
          isA<SignupState>()
              .having((s) => s.email.value, 'email', '<EMAIL>'),
          // Password change
          isA<SignupState>()
              .having((s) => s.password.value, 'password', 'Password123'),
          // Confirmed password change
          isA<SignupState>()
              .having(
                (s) => s.confirmedPassword.value,
                'confirmedPassword',
                'Password123',
              )
              .having((s) => s.status, 'status', FormStatus.valid),
          // Submission in progress
          isA<SignupState>().having(
            (s) => s.status,
            'status',
            FormStatus.submissionInProgress,
          ),
          // Submission success
          isA<SignupState>()
              .having((s) => s.status, 'status', FormStatus.submissionSuccess),
        ],
      );
    });

    group('Rapid Navigation Simulation', () {
      test('should handle rapid close during form interaction', () async {
        // Simulate rapid user interaction followed by navigation
        signupCubit
          ..nameChanged('John Doe')
          ..emailChanged('<EMAIL>')
          ..passwordChanged('Password123')
          ..confirmedPasswordChanged('Password123')
          ..nameTouched()
          ..emailTouched();

        // Simulate immediate navigation (close)
        await signupCubit.close();

        // Try more interactions after close
        signupCubit
          ..passwordTouched()
          ..confirmedPasswordTouched()
          ..nameChanged('Jane Doe');

        // Should not throw any exceptions
        expect(signupCubit.isClosed, isTrue);
      });
    });
  });
}
