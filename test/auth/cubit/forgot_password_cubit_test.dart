import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/auth/cubit/forgot_password_cubit.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('ForgotPasswordCubit', () {
    late MockAuthRepository mockAuthRepository;
    late ForgotPasswordCubit forgotPasswordCubit;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      forgotPasswordCubit = ForgotPasswordCubit(mockAuthRepository);
    });

    tearDown(() {
      forgotPasswordCubit.close();
    });

    group('constructor', () {
      test('initial state is correct', () {
        expect(
          forgotPasswordCubit.state,
          const ForgotPasswordState(),
        );
      });
    });

    group('emailChanged', () {
      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'emits state with updated email when email is valid',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged('<EMAIL>'),
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
        ],
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'emits state with invalid status when email is invalid',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged('invalid-email'),
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('invalid-email'),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'emits state with invalid status when email is empty',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) => cubit.emailChanged(''),
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty(),
            status: FormStatus.invalid,
          ),
        ],
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'handles multiple email changes',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..emailChanged('invalid')
            ..emailChanged('<EMAIL>')
            ..emailChanged('<EMAIL>');
        },
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('invalid'),
            status: FormStatus.invalid,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
        ],
      );

      test('does not emit when cubit is closed', () {
        forgotPasswordCubit.close();
        expect(
          () => forgotPasswordCubit.emailChanged('<EMAIL>'),
          returnsNormally,
        );
      });
    });

    group('emailTouched', () {
      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'emits state with emailTouched set to true',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) => cubit.emailTouched(),
        expect: () => [
          const ForgotPasswordState(emailTouched: true),
        ],
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'does not emit duplicate states when called multiple times',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) {
          cubit
            ..emailTouched()
            ..emailTouched();
        },
        expect: () => [
          const ForgotPasswordState(emailTouched: true),
        ],
      );

      test('does not emit when cubit is closed', () {
        forgotPasswordCubit.close();
        expect(() => forgotPasswordCubit.emailTouched(), returnsNormally);
      });
    });

    group('sendPasswordResetEmail', () {
      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'does not proceed when form is invalid',
        build: () => ForgotPasswordCubit(mockAuthRepository),
        act: (cubit) async {
          cubit.emailChanged('invalid-email');
          await cubit.sendPasswordResetEmail();
        },
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('invalid-email'),
            status: FormStatus.invalid,
          ),
        ],
        verify: (cubit) {
          verifyNever(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: any(named: 'email'),
            ),
          );
        },
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'emits success state when password reset email is sent successfully',
        build: () {
          when(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: any(named: 'email'),
            ),
          ).thenAnswer((_) async {});
          return ForgotPasswordCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit.emailChanged('<EMAIL>');
          await cubit.sendPasswordResetEmail();
        },
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionInProgress,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionSuccess,
          ),
        ],
        verify: (cubit) {
          verify(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: '<EMAIL>',
            ),
          ).called(1);
        },
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'emits failure state when password reset email fails',
        build: () {
          when(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: any(named: 'email'),
            ),
          ).thenThrow(
            const SendPasswordResetEmailFailure('Reset email failed'),
          );
          return ForgotPasswordCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit.emailChanged('<EMAIL>');
          await cubit.sendPasswordResetEmail();
        },
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionInProgress,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionFailure,
            errorMessage: 'Reset email failed',
          ),
        ],
      );

      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'handles generic exceptions',
        build: () {
          when(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: any(named: 'email'),
            ),
          ).thenThrow(Exception('Generic error'));
          return ForgotPasswordCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit.emailChanged('<EMAIL>');
          await cubit.sendPasswordResetEmail();
        },
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionInProgress,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionFailure,
            errorMessage: 'An unexpected error occurred. Please try again.',
          ),
        ],
      );

      test('does not proceed when cubit is closed', () async {
        forgotPasswordCubit.emailChanged('<EMAIL>');
        await forgotPasswordCubit.close();

        expect(
          () => forgotPasswordCubit.sendPasswordResetEmail(),
          returnsNormally,
        );

        verifyNever(
          () => mockAuthRepository.sendPasswordResetEmail(
            email: any(named: 'email'),
          ),
        );
      });
    });

    group('lifecycle management', () {
      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'handles cubit closure during password reset operation',
        build: () {
          when(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: any(named: 'email'),
            ),
          ).thenAnswer((_) async {
            await Future<void>.delayed(const Duration(milliseconds: 100));
          });
          return ForgotPasswordCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit.emailChanged('<EMAIL>');
          final future = cubit.sendPasswordResetEmail();
          await cubit.close();
          await future;
        },
        expect: () => [
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.valid,
          ),
          const ForgotPasswordState(
            email: Email.dirty('<EMAIL>'),
            status: FormStatus.submissionInProgress,
          ),
        ],
      );
    });

    group('edge cases', () {
      blocTest<ForgotPasswordCubit, ForgotPasswordState>(
        'handles rapid form submissions',
        build: () {
          when(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: any(named: 'email'),
            ),
          ).thenAnswer((_) async {});
          return ForgotPasswordCubit(mockAuthRepository);
        },
        act: (cubit) async {
          cubit.emailChanged('<EMAIL>');
          await Future.wait([
            cubit.sendPasswordResetEmail(),
            cubit.sendPasswordResetEmail(),
            cubit.sendPasswordResetEmail(),
          ]);
        },
        verify: (cubit) {
          // Should only call repository once due to form status checks
          verify(
            () => mockAuthRepository.sendPasswordResetEmail(
              email: '<EMAIL>',
            ),
          ).called(1);
        },
      );

      test('handles special email formats', () {
        const specialEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in specialEmails) {
          forgotPasswordCubit.emailChanged(email);
          expect(forgotPasswordCubit.state.status, FormStatus.valid);
        }
      });
    });
  });
}
