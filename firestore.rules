rules_version = '2';

// Firestore Security Rules for Face Verification Videos
// These rules ensure that only authenticated users can access their own video metadata

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Face verification videos collection
    match /face_verification_videos/{videoId} {
      // Allow read access for authenticated users accessing their own videos
      allow read: if request.auth != null
                  && request.auth.uid == resource.data.userId;

      // Allow create with validation for new video metadata
      allow create: if request.auth != null
                   && request.auth.uid == request.resource.data.userId
                   && isValidVideoMetadata(request.resource.data);

      // Allow updates only for authenticated users updating their own videos
      // and only for specific fields that can be modified
      allow update: if request.auth != null
                   && request.auth.uid == resource.data.userId
                   && request.auth.uid == request.resource.data.userId
                   && isValidVideoMetadataUpdate(request.resource.data, resource.data);

      // Allow delete for authenticated users deleting their own videos
      allow delete: if request.auth != null
                   && request.auth.uid == resource.data.userId;
    }

    // Helper functions for validation
    function isValidVideoMetadata(data) {
      return data.keys().hasAll(['userId', 'fileName', 'downloadUrl', 'storagePath',
                                'uploadTimestamp', 'fileSize', 'durationSeconds', 'qualityScore',
                                'faceCoverageStats', 'isProcessed']) &&
             data.userId is string &&
             data.fileName is string &&
             data.downloadUrl is string &&
             data.storagePath is string &&
             data.uploadTimestamp is timestamp &&
             data.fileSize is number &&
             data.fileSize > 0 &&
             data.durationSeconds is number &&
             data.durationSeconds > 0 &&
             data.qualityScore is number &&
             data.qualityScore >= 0 &&
             data.qualityScore <= 100 &&
             data.faceCoverageStats is map &&
             data.isProcessed is bool;
    }

    function isValidVideoMetadataUpdate(newData, existingData) {
      // Only allow updates to specific fields, not core metadata
      return newData.userId == existingData.userId &&
             newData.fileName == existingData.fileName &&
             newData.downloadUrl == existingData.downloadUrl &&
             newData.storagePath == existingData.storagePath &&
             newData.uploadTimestamp == existingData.uploadTimestamp &&
             newData.fileSize == existingData.fileSize &&
             // Allow updates to processing status, thumbnail info, etc.
             newData.keys().hasAll(existingData.keys());
    }
  }
}
