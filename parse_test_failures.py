#!/usr/bin/env python3
"""
Test Failure Analysis Script

Parses test_log.txt and clusters failures by module and error signature.
Creates a table with Module, File, Error type, Root symptom, and Count.
"""

import re
import json
from collections import defaultdict, Counter
from pathlib import Path

def extract_module_from_path(file_path):
    """Extract the module name from a file path."""
    if not file_path:
        return "unknown"
    
    parts = file_path.split('/')
    
    # Find test directory and extract module
    if 'test' in parts:
        test_index = parts.index('test')
        if test_index + 1 < len(parts):
            next_part = parts[test_index + 1]
            if next_part in ['features', 'auth', 'core', 'shared', 'home', 'integration']:
                if next_part == 'features' and test_index + 2 < len(parts):
                    return f"features/{parts[test_index + 2]}"
                return next_part
    
    # Fallback: look for common module patterns
    for part in parts:
        if part in ['auth', 'core', 'shared', 'home', 'features', 'onboarding', 'face_verification', 'video_gallery']:
            return part
    
    return "unknown"

def categorize_error(error_text):
    """Categorize the error based on its content."""
    error_lower = error_text.lower()
    
    if "renderflex overflowed" in error_lower:
        return "Layout/Overflow"
    elif "no material ancestor" in error_lower:
        return "Widget/Material"
    elif "exception" in error_lower and "caught" in error_lower:
        return "Exception"
    elif "assertion" in error_lower:
        return "Assertion"
    elif "test failed" in error_lower:
        return "Test Failure"
    elif "timeout" in error_lower:
        return "Timeout"
    elif "network" in error_lower:
        return "Network"
    elif "firebase" in error_lower:
        return "Firebase"
    elif "camera" in error_lower:
        return "Camera"
    elif "permission" in error_lower:
        return "Permission"
    else:
        return "Other"

def extract_root_symptom(error_text, context_lines):
    """Extract the root symptom from error text and context."""
    # Common patterns for extracting meaningful error symptoms
    patterns = [
        r"A RenderFlex overflowed by ([0-9.]+) pixels",
        r"Exception: (.+?)(?:\n|$)",
        r"The following assertion was thrown.+?:\s*(.+?)(?:\n|$)",
        r"FlutterError: (.+?)(?:\n|$)",
        r"Error: (.+?)(?:\n|$)",
        r"Failed to (.+?)(?:\n|$)",
        r"Cannot (.+?)(?:\n|$)",
        r"Unable to (.+?)(?:\n|$)",
    ]
    
    full_text = error_text + " " + " ".join(context_lines)
    
    for pattern in patterns:
        match = re.search(pattern, full_text, re.IGNORECASE | re.MULTILINE)
        if match:
            symptom = match.group(1).strip()
            # Clean up the symptom
            symptom = re.sub(r'\s+', ' ', symptom)
            return symptom[:100]  # Limit length
    
    # Fallback: extract first meaningful line
    lines = error_text.split('\n')
    for line in lines:
        line = line.strip()
        if line and not line.startswith('#') and len(line) > 10:
            return line[:100]
    
    return "Unknown error"

def parse_test_log(log_file_path):
    """Parse the test log file and extract failure information."""
    failures = []
    current_test_file = None
    current_test_name = None
    in_error_block = False
    error_lines = []
    context_lines = []
    
    with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Extract test file path from lines like:
        # 00:07 +142 -1: /path/to/test_file.dart: Test description [E]
        test_match = re.search(r'(\d+:\d+)\s+\+\d+\s+-\d+:\s+([^:]+\.dart):\s+(.+?)\s+\[E\]', line)
        if test_match:
            current_test_file = test_match.group(2)
            current_test_name = test_match.group(3)
            in_error_block = True
            error_lines = []
            context_lines = []
            
            # Look back for error context
            for j in range(max(0, i-50), i):
                prev_line = lines[j].strip()
                if "exception caught" in prev_line.lower() or "renderflex overflowed" in prev_line.lower():
                    # Found start of error, collect error lines
                    for k in range(j, i):
                        error_lines.append(lines[k].strip())
                    break
            
            # Look forward for more context
            for j in range(i+1, min(len(lines), i+10)):
                next_line = lines[j].strip()
                context_lines.append(next_line)
                if next_line.startswith('00:') and '+' in next_line:
                    break
            
            if current_test_file and error_lines:
                module = extract_module_from_path(current_test_file)
                error_text = '\n'.join(error_lines)
                error_type = categorize_error(error_text)
                root_symptom = extract_root_symptom(error_text, context_lines)
                
                failures.append({
                    'module': module,
                    'file': current_test_file,
                    'test_name': current_test_name,
                    'error_type': error_type,
                    'root_symptom': root_symptom,
                    'error_text': error_text,
                    'line_number': i + 1
                })
        
        # Also look for direct error patterns
        elif "exception caught" in line.lower() or "renderflex overflowed" in line.lower():
            # Collect error block
            error_lines = [line]
            j = i + 1
            while j < len(lines) and j < i + 20:
                next_line = lines[j].strip()
                if next_line.startswith('00:') and '+' in next_line and '-' in next_line:
                    break
                error_lines.append(next_line)
                j += 1
            
            # Look for associated test
            for k in range(max(0, i-10), min(len(lines), i+10)):
                test_line = lines[k].strip()
                test_match = re.search(r'(\d+:\d+)\s+\+\d+\s+-\d+:\s+([^:]+\.dart):\s+(.+?)\s+\[E\]', test_line)
                if test_match:
                    current_test_file = test_match.group(2)
                    current_test_name = test_match.group(3)
                    break
            
            if current_test_file:
                module = extract_module_from_path(current_test_file)
                error_text = '\n'.join(error_lines)
                error_type = categorize_error(error_text)
                root_symptom = extract_root_symptom(error_text, [])
                
                failures.append({
                    'module': module,
                    'file': current_test_file,
                    'test_name': current_test_name or "unknown",
                    'error_type': error_type,
                    'root_symptom': root_symptom,
                    'error_text': error_text,
                    'line_number': i + 1
                })
        
        i += 1
    
    return failures

def create_cluster_analysis(failures):
    """Create clustered analysis of failures."""
    # Group by module and error type
    clusters = defaultdict(lambda: defaultdict(list))
    error_counts = Counter()
    module_counts = Counter()
    symptom_counts = Counter()
    
    for failure in failures:
        module = failure['module']
        error_type = failure['error_type']
        root_symptom = failure['root_symptom']
        
        clusters[module][error_type].append(failure)
        error_counts[error_type] += 1
        module_counts[module] += 1
        symptom_counts[root_symptom] += 1
    
    return clusters, error_counts, module_counts, symptom_counts

def generate_markdown_report(clusters, error_counts, module_counts, symptom_counts, total_failures):
    """Generate markdown report with the analysis."""
    
    report = f"""# Test Failures Analysis

## Summary
- **Total Failures**: {total_failures}
- **Modules Affected**: {len(module_counts)}
- **Error Types**: {len(error_counts)}
- **Unique Symptoms**: {len(symptom_counts)}

## Failure Distribution by Module

| Module | File | Error Type | Root Symptom | Count |
|--------|------|------------|--------------|-------|
"""
    
    # Create the main table
    table_rows = []
    for module, error_types in clusters.items():
        for error_type, failures in error_types.items():
            # Group by symptom within this module/error_type
            symptom_groups = defaultdict(list)
            for failure in failures:
                symptom_groups[failure['root_symptom']].append(failure)
            
            for symptom, symptom_failures in symptom_groups.items():
                files = list(set(f['file'].split('/')[-1] for f in symptom_failures))
                file_list = ', '.join(files[:3])  # Limit to first 3 files
                if len(files) > 3:
                    file_list += f", +{len(files)-3} more"
                
                table_rows.append({
                    'module': module,
                    'files': file_list,
                    'error_type': error_type,
                    'symptom': symptom,
                    'count': len(symptom_failures)
                })
    
    # Sort by count descending
    table_rows.sort(key=lambda x: x['count'], reverse=True)
    
    for row in table_rows:
        report += f"| {row['module']} | {row['files']} | {row['error_type']} | {row['symptom']} | {row['count']} |\n"
    
    # Add priority analysis
    report += f"""

## Priority Analysis (Common Root Causes)

### Top Error Types
"""
    for error_type, count in error_counts.most_common(10):
        report += f"- **{error_type}**: {count} occurrences\n"
    
    report += f"""

### Most Affected Modules
"""
    for module, count in module_counts.most_common(10):
        report += f"- **{module}**: {count} failures\n"
    
    report += f"""

### Most Common Symptoms
"""
    for symptom, count in symptom_counts.most_common(15):
        report += f"- **{symptom}**: {count} occurrences\n"
    
    # Add detailed breakdown by module
    report += f"""

## Detailed Breakdown by Module

"""
    
    for module, error_types in sorted(clusters.items()):
        total_module_failures = sum(len(failures) for failures in error_types.values())
        report += f"### {module} ({total_module_failures} failures)\n\n"
        
        for error_type, failures in sorted(error_types.items(), key=lambda x: len(x[1]), reverse=True):
            report += f"#### {error_type} ({len(failures)} failures)\n"
            
            # Group by symptom
            symptom_groups = defaultdict(list)
            for failure in failures:
                symptom_groups[failure['root_symptom']].append(failure)
            
            for symptom, symptom_failures in sorted(symptom_groups.items(), key=lambda x: len(x[1]), reverse=True):
                files = set(f['file'].split('/')[-1] for f in symptom_failures)
                report += f"- **{symptom}** ({len(symptom_failures)} occurrences)\n"
                report += f"  - Files: {', '.join(sorted(files))}\n"
            
            report += "\n"
    
    return report

def main():
    """Main function to parse log and generate analysis."""
    log_file = "test_log.txt"
    
    if not Path(log_file).exists():
        print(f"Error: {log_file} not found")
        return
    
    print("Parsing test log...")
    failures = parse_test_log(log_file)
    
    print(f"Found {len(failures)} test failures")
    
    print("Creating cluster analysis...")
    clusters, error_counts, module_counts, symptom_counts = create_cluster_analysis(failures)
    
    print("Generating markdown report...")
    report = generate_markdown_report(clusters, error_counts, module_counts, symptom_counts, len(failures))
    
    # Write report to file
    output_file = "docs/test_failures_analysis.md"
    Path("docs").mkdir(exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Analysis complete! Report saved to {output_file}")
    
    # Print summary to console
    print("\n=== SUMMARY ===")
    print(f"Total failures: {len(failures)}")
    print(f"Modules affected: {len(module_counts)}")
    print(f"Top error types:")
    for error_type, count in error_counts.most_common(5):
        print(f"  - {error_type}: {count}")
    print(f"Most affected modules:")
    for module, count in module_counts.most_common(5):
        print(f"  - {module}: {count}")

if __name__ == "__main__":
    main()
