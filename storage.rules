rules_version = '2';

// Firebase Storage Security Rules for Face Verification Videos
// These rules ensure that only authenticated users can upload and access their own videos

service firebase.storage {
  match /b/{bucket}/o {
    
    // Face verification videos - user-specific access
    match /face_verification_videos/{userId}/{videoId} {
      // Allow read access for authenticated users accessing their own videos
      allow read: if request.auth != null
                  && request.auth.uid == userId;

      // Allow create with validation for uploads
      allow create: if request.auth != null
                   && request.auth.uid == userId
                   && isValidVideoUpload();

      // Allow updates only for metadata changes (not file content)
      allow update: if request.auth != null
                   && request.auth.uid == userId
                   && request.resource.size == resource.size;

      // Allow delete for authenticated users accessing their own videos
      allow delete: if request.auth != null
                   && request.auth.uid == userId;
    }
    
    // Video thumbnails - user-specific access
    match /face_verification_thumbnails/{userId}/{thumbnailId} {
      // Allow read access for authenticated users accessing their own thumbnails
      allow read: if request.auth != null
                  && request.auth.uid == userId;

      // Allow create with validation for thumbnail uploads
      allow create: if request.auth != null
                   && request.auth.uid == userId
                   && isValidThumbnailUpload();

      // Allow updates for authenticated users accessing their own thumbnails
      allow update: if request.auth != null
                   && request.auth.uid == userId;

      // Allow delete for authenticated users accessing their own thumbnails
      allow delete: if request.auth != null
                   && request.auth.uid == userId;
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}

// Helper function to validate video uploads
function isValidVideoUpload() {
  return request.resource.size <= 50 * 1024 * 1024  // Max 50MB
      && request.resource.contentType.matches('video/.*')  // Must be video
      && request.resource.name.matches('.*\\.mp4$');  // Must be .mp4 file
}

// Helper function to validate thumbnail uploads
function isValidThumbnailUpload() {
  return request.resource.size <= 5 * 1024 * 1024  // Max 5MB
      && request.resource.contentType.matches('image/.*')  // Must be image
      && request.resource.name.matches('.*\\.(jpg|jpeg|png)$');  // Must be image file
}
