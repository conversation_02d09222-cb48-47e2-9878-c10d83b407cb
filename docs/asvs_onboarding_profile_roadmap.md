# ASVS Onboarding + Profile System Roadmap

## Overview

This document outlines the comprehensive roadmap for implementing the ASVS (Automated Subjective Vitality Scale) Onboarding and Profile System in the Bloomg Flutter application. The system will collect health and vitality data from users through an intuitive onboarding flow and provide profile management capabilities.

## Technical Architecture

### Core Technologies
- **Framework**: Flutter (SDK ^3.5.0)
- **State Management**: BLoC pattern with flutter_bloc (^8.1.6)
- **Local Storage**: Hive with type-safe adapters
- **Cloud Storage**: Firebase Firestore for sync
- **UI Framework**: shadcn_flutter/getwidget components
- **Onboarding**: flutter_onboarding_slider for multi-screen wizard
- **Validation**: formz package for field-level validation
- **Navigation**: go_router for type-safe routing

### Data Flow Architecture
```
User Input → Formz Validation → BLoC State Management → Hive Local Storage → Firebase Sync
     ↓                ↓                    ↓                    ↓              ↓
UI Updates ← State Updates ← Event Processing ← Data Persistence ← Cloud Backup
```

### Integration Points
- **Firebase Auth**: Seamless integration with existing authentication system
- **User Profile**: Extension of existing UserModel with ASVS data
- **Navigation**: Integration with existing go_router setup
- **Design System**: Consistent with existing AppColors, AppTextStyles, AppDimensions

## Data Models

### AsvsUserProfile Model
```dart
@HiveType(typeId: 1)
class AsvsUserProfile extends Equatable {
  // Personal Information
  final String? firstName;
  final String? lastName;
  final DateTime? dateOfBirth;
  final Gender? gender;
  final UnitPreference unitPreference;
  
  // Physical Measurements
  final double? heightCm;
  final double? weightKg;
  final double? bmi; // Calculated field
  
  // Health Information
  final SmokingStatus? smokingStatus;
  final int? exerciseFrequency; // days per week
  final int? sleepHours; // average hours per night
  final List<String> medications;
  final List<String> allergies;
  final List<String> medicalConditions;
  
  // ASVS Specific
  final int? stressLevel; // 1-10 scale
  final int? energyLevel; // 1-10 scale
  final int? moodRating; // 1-10 scale
  
  // Metadata
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isOnboardingComplete;
}
```

### Supporting Enums
```dart
@HiveType(typeId: 2)
enum UnitPreference {
  @HiveField(0) metric,
  @HiveField(1) imperial,
}

@HiveType(typeId: 3)
enum Gender {
  @HiveField(0) male,
  @HiveField(1) female,
  @HiveField(2) nonBinary,
  @HiveField(3) preferNotToSay,
}

@HiveType(typeId: 4)
enum SmokingStatus {
  @HiveField(0) never,
  @HiveField(1) former,
  @HiveField(2) current,
  @HiveField(3) occasional,
}
```

## Implementation Phases

### Phase 1: Dependencies and Models Setup ✅ COMPLETE
**Duration**: 1-2 days (Completed)
**Status**: ✅ **COMPLETED** - All dependencies, models, and Hive integration implemented successfully

#### Dependencies Added ✅
- ✅ `flutter_onboarding_slider: ^1.0.11` - Multi-screen onboarding wizard
- ✅ `dropdown_button2: ^2.3.9` - Enhanced dropdown components
- ✅ `flutter_datetime_picker_plus: ^2.2.0` - Date/time picker widgets

#### Tasks ✅ COMPLETED
- ✅ Add required dependencies to pubspec.yaml
- ✅ Create AsvsUserProfile model with Hive annotations (typeId: 1)
- ✅ Create supporting enums with Hive annotations (typeIds: 2-4)
- ✅ Generate Hive adapters using build_runner
- ✅ Update HiveService to register new adapters
- ⚠️ Create unit tests for models (PARTIAL - only basic model tests exist)

#### Phase 1 Implementation Summary
**✅ Completed Components:**
- ✅ All required dependencies added to pubspec.yaml and verified working
- ✅ AsvsUserProfile model with comprehensive Hive annotations (typeId: 1)
- ✅ Supporting enums: UnitPreference (typeId: 2), Gender (typeId: 3), SmokingStatus (typeId: 4)
- ✅ Hive adapters generated and properly registered in HiveService
- ✅ Type-safe data persistence with proper serialization/deserialization
- ✅ Extension methods for enum display names and utility functions
- ✅ JSON serialization support for Firebase integration
- ✅ Comprehensive model properties including BMI calculation and completion tracking

**🔧 Technical Verification:**
- ✅ HiveService initialization confirmed working (all adapters registered with correct typeIds)
- ✅ Dependency injection properly configured for onboarding components
- ✅ App startup successful with no initialization errors
- ✅ Data persistence layer fully functional

**⚠️ Remaining Work:**
- ⚠️ Comprehensive unit tests needed for BLoC components and services
- ⚠️ Widget tests for onboarding screens

#### File Structure ✅ IMPLEMENTED
```
lib/features/onboarding/
├── models/
│   ├── asvs_user_profile.dart ✅
│   ├── asvs_user_profile.g.dart ✅ (generated)
│   ├── unit_preference.dart ✅
│   ├── unit_preference.g.dart ✅ (generated)
│   ├── gender.dart ✅
│   ├── gender.g.dart ✅ (generated)
│   ├── smoking_status.dart ✅
│   ├── smoking_status.g.dart ✅ (generated)
│   └── models.dart ✅ (barrel export)
```

### Phase 2: BLoC Architecture Setup ✅ COMPLETE
**Duration**: 2-3 days (Completed)
**Dependencies**: Phase 1 complete
**Status**: ✅ **COMPLETED** - All BLoC components implemented with zero flutter analyze issues

#### Components
- **OnboardingBloc**: Manages onboarding flow state
- **ProfileBloc**: Handles profile CRUD operations
- **Repository Layer**: Data access abstraction
- **Service Layer**: Business logic and validation

#### Events & States
```dart
// OnboardingBloc Events
abstract class OnboardingEvent {}
class StartOnboarding extends OnboardingEvent {}
class NextStep extends OnboardingEvent {}
class PreviousStep extends OnboardingEvent {}
class UpdateProfile extends OnboardingEvent {}
class CompleteOnboarding extends OnboardingEvent {}

// OnboardingBloc States
abstract class OnboardingState {}
class OnboardingInitial extends OnboardingState {}
class OnboardingInProgress extends OnboardingState {}
class OnboardingCompleted extends OnboardingState {}
class OnboardingError extends OnboardingState {}
```

#### File Structure
```
lib/features/onboarding/
├── bloc/
│   ├── onboarding_bloc.dart
│   ├── onboarding_event.dart
│   ├── onboarding_state.dart
│   └── profile_bloc.dart
├── repository/
│   ├── onboarding_repository.dart
│   └── profile_repository.dart
└── services/
    ├── profile_service.dart
    └── validation_service.dart
```

#### Phase 2 Implementation Summary
**✅ Completed Components:**
- ✅ OnboardingBloc with comprehensive event handling (StartOnboarding, NextStep, PreviousStep, UpdateProfile, CompleteOnboarding)
- ✅ ProfileBloc with full CRUD operations (LoadProfile, UpdateProfile, SaveProfile, DeleteProfile, RefreshProfile)
- ✅ Repository layer with HiveService integration
- ✅ OnboardingService with validation and health risk assessment
- ✅ Comprehensive state management with proper error handling
- ✅ Type-safe enum handling (Gender, UnitPreference, SmokingStatus)
- ✅ BMI and age calculations with profile completion tracking
- ✅ Structured logging with '[ONBOARDING] Action: Details' format
- ✅ Zero flutter analyze issues achieved

**🔧 Technical Decisions:**
- Used `currentProfile` property naming for consistency with existing codebase patterns
- Implemented proper null safety with type casting for enum values
- Integrated with existing HiveService architecture
- Followed BLoC pattern established in face_verification module
- Maintained 80-character line limits and proper import ordering

### Phase 3: UI Components and Screens ✅ COMPLETE
**Duration**: 3-4 days (Completed)
**Dependencies**: ✅ Phase 2 complete
**Status**: ✅ **COMPLETED** - All 7 onboarding screens implemented with flutter_onboarding_slider integration

#### Onboarding Screens ✅ IMPLEMENTED
1. ✅ **Welcome Screen**: Introduction and privacy notice
2. ✅ **Personal Info**: Name, date of birth, gender
3. ✅ **Physical Measurements**: Height, weight, unit preferences
4. ✅ **Health Information**: Smoking, exercise, sleep patterns
5. ✅ **Medical History**: Medications, allergies, conditions
6. ✅ **ASVS Assessment**: Stress, energy, mood ratings
7. ✅ **Completion**: Summary and confirmation

#### UI Components ✅ IMPLEMENTED
- ✅ Custom form fields with validation using formz
- ✅ Progress indicators with OnboardingProgressIndicator
- ✅ Dropdown selectors with dropdown_button2
- ✅ Date pickers with flutter_datetime_picker_plus
- ✅ Slider components for ratings (stress, energy, mood)
- ✅ Multi-select chips for medications, allergies, conditions

#### Design System Integration ✅ COMPLETE
- ✅ Consistent with existing AppColors palette
- ✅ Follow AppTextStyles typography
- ✅ Use AppDimensions spacing
- ✅ Responsive design with responsive_framework
- ✅ shadcn_flutter/getwidget UI components integration

#### Phase 3 Implementation Summary
**✅ Completed Components:**
- ✅ OnboardingFlowPage with flutter_onboarding_slider integration
- ✅ All 7 onboarding screens with proper BLoC integration
- ✅ Custom form validation with field-level error display
- ✅ Responsive UI design across mobile, tablet, and desktop
- ✅ Progress tracking and navigation between screens
- ✅ Integration with OnboardingBloc and ProfileBloc
- ✅ Structured logging with '[ONBOARDING] Action: Details' format
- ✅ go_router navigation integration

**🔧 Technical Decisions:**
- Used flutter_onboarding_slider for smooth screen transitions
- Implemented field-level validation showing errors only for touched fields
- Integrated with existing design system (AppColors, AppTextStyles, AppDimensions)
- Used BlocConsumer pattern for state management and navigation
- Maintained responsive design with responsive_framework breakpoints

**⚠️ Known Issues (86 flutter analyze warnings):**
- Deprecated `withOpacity` usage (needs migration to `withValues`)
- Line length violations (80-character limit)
- Generated code warnings (unnecessary_breaks in .g.dart files)
- Minor style issues (flutter_style_todos, use_if_null_to_convert_nulls_to_bools)

### Phase 4: Data Persistence and Sync ✅ PARTIAL COMPLETE
**Duration**: 2-3 days
**Dependencies**: ✅ Phase 3 complete
**Status**: ✅ **LOCAL STORAGE COMPLETE** - Cloud sync and advanced features pending

#### Local Storage (Hive) ✅ FULLY IMPLEMENTED
- ✅ Profile data persistence with AsvsUserProfile model
- ✅ Offline capability through HiveService integration
- ✅ Type-safe adapters for enums (Gender, UnitPreference, SmokingStatus)
- ✅ Repository pattern with CRUD operations
- ✅ Data persistence during onboarding flow verified
- ⏳ Data migration support (planned for future versions)
- ⏳ Backup/restore functionality (planned for future versions)

#### Cloud Sync (Firebase) ⏳ PENDING
- ⏳ Firestore integration for profile sync
- ⏳ Real-time synchronization
- ⏳ Conflict resolution
- ⏳ Data validation

#### Security Considerations ⏳ PENDING
- ⏳ Data encryption at rest
- ⏳ Secure transmission
- ⏳ Privacy compliance
- ⏳ User consent management

#### Phase 4 Current Status
**✅ Completed:**
- ✅ Hive local storage integration verified working
- ✅ Profile repository with CRUD operations implemented
- ✅ Data persistence during onboarding flow functional
- ✅ AsvsProfileRepository with Firebase integration structure
- ✅ Dependency injection for all onboarding components

**⏳ Next Steps:**
- ⏳ Implement Firebase Firestore sync functionality
- ⏳ Add data validation and conflict resolution
- ⏳ Implement security measures and encryption
- ⏳ Add comprehensive error handling for cloud operations

### Phase 5: Navigation and Integration ✅ COMPLETE
**Duration**: 1-2 days (Completed)
**Dependencies**: ✅ Phase 4 local storage complete
**Status**: ✅ **COMPLETED** - Full navigation integration implemented

#### Navigation Flow ✅ IMPLEMENTED
```
Authentication → Onboarding Check → Profile/Onboarding → Main App
```

#### Integration Points ✅ COMPLETED
- ✅ Auth system integration with go_router
- ✅ Main app navigation with proper redirects
- ✅ OnboardingFlowPage integrated with app routing
- ✅ State persistence across app restarts via Hive
- ✅ Route protection for authenticated/unauthenticated users
- ✅ Onboarding completion flow to home page

#### Phase 5 Implementation Summary
**✅ Completed Components:**
- ✅ go_router configuration with onboarding routes
- ✅ Route guards and authentication-based redirects
- ✅ OnboardingFlowPage with flutter_onboarding_slider integration
- ✅ Navigation between all 7 onboarding screens
- ✅ Completion flow from onboarding to main app
- ✅ Profile view and edit page routing
- ✅ Proper state management during navigation

**🔧 Technical Verification:**
- ✅ App startup and navigation confirmed working
- ✅ Route redirects functioning properly
- ✅ Authentication state management integrated
- ✅ No navigation-related runtime errors

### Phase 6: Testing and Quality Assurance ⚠️ IN PROGRESS
**Duration**: 2-3 days
**Dependencies**: ✅ Phase 5 complete
**Status**: ⚠️ **PARTIAL** - Basic tests exist, comprehensive coverage needed

#### Testing Strategy ⚠️ PARTIAL IMPLEMENTATION
- ✅ Unit tests for models (basic coverage)
- ❌ Widget tests for UI components (missing)
- ❌ Integration tests for complete flows (missing)
- ❌ BLoC tests for state management (missing)
- ❌ Repository tests for data layer (missing)

#### Quality Metrics ⚠️ NEEDS IMPROVEMENT
- ❌ Code coverage target: 80%+ (currently low)
- ❌ Flutter analyze: 0 issues (currently 48 issues)
- ⏳ Performance benchmarks (not yet measured)
- ⏳ Accessibility compliance (not yet tested)

#### Phase 6 Current Status
**✅ Completed:**
- ✅ Basic unit tests for AsvsUserProfile model
- ✅ Enum extension method tests
- ✅ Model serialization/deserialization tests

**❌ Missing Critical Tests:**
- ❌ OnboardingBloc tests (state transitions, event handling)
- ❌ ProfileBloc tests (CRUD operations, error handling)
- ❌ OnboardingService tests (validation, business logic)
- ❌ AsvsProfileRepository tests (data persistence)
- ❌ Widget tests for all 7 onboarding screens
- ❌ Integration tests for complete onboarding flow

**⚠️ Code Quality Issues:**
- ⚠️ 48 flutter analyze issues remaining (down from 86)
- ⚠️ Generated code warnings (unnecessary_breaks in .g.dart files)
- ⚠️ Line length violations (80-character limit)
- ⚠️ Style issues (use_if_null_to_convert_nulls_to_bools, flutter_style_todos)

### Phase 7: Documentation and Deployment
**Duration**: 1-2 days
**Dependencies**: Phase 6 complete

#### Documentation
- API documentation
- User guides
- Developer documentation
- Deployment guides

#### Deployment
- Build configuration
- Environment setup
- Release preparation
- Monitoring setup

## Design Decisions

### User Experience
- **Progressive Disclosure**: Information collected in logical steps
- **Smart Defaults**: Reasonable default values where applicable
- **Validation Feedback**: Real-time validation with clear error messages
- **Skip Options**: Allow users to skip non-essential fields
- **Progress Indication**: Clear progress through onboarding flow

### Technical Decisions
- **Hive TypeIds**: Sequential allocation (1-4) to avoid conflicts
- **State Management**: BLoC pattern for consistency with existing codebase
- **Validation**: Field-level validation with formz for immediate feedback
- **Data Sync**: Optimistic updates with conflict resolution
- **Error Handling**: Comprehensive error handling with user-friendly messages

### Performance Considerations
- **Lazy Loading**: Load screens as needed
- **Memory Management**: Efficient state management
- **Network Optimization**: Batch operations where possible
- **Caching Strategy**: Intelligent caching for offline support

## Success Metrics

### User Engagement
- Onboarding completion rate: >85%
- Profile completion rate: >90%
- Time to complete onboarding: <5 minutes
- User satisfaction score: >4.5/5

### Technical Metrics
- App startup time impact: <200ms
- Memory usage increase: <50MB
- Network requests: Optimized batching
- Error rate: <1%

## Risk Mitigation

### Technical Risks
- **Data Migration**: Comprehensive testing of schema changes
- **Performance Impact**: Regular performance monitoring
- **Integration Issues**: Thorough integration testing
- **Security Vulnerabilities**: Security audit and penetration testing

### User Experience Risks
- **Onboarding Fatigue**: A/B testing of flow length
- **Data Privacy Concerns**: Clear privacy policy and consent
- **Accessibility Issues**: Comprehensive accessibility testing
- **Cross-platform Consistency**: Platform-specific testing

## Future Enhancements

### Phase 8: Advanced Features (Future)
- Health data integration (HealthKit/Google Fit)
- AI-powered health insights
- Social features and sharing
- Wearable device integration
- Advanced analytics and reporting

### Phase 9: Optimization (Future)
- Performance optimizations
- Advanced caching strategies
- Offline-first architecture
- Real-time collaboration features

---

## 📋 Current Implementation Audit (December 2024)

### Overall Progress Summary
**Implementation Status**: 🟢 **CORE FUNCTIONALITY COMPLETE** - Ready for testing and refinement

#### Phase Completion Status:
- ✅ **Phase 1**: Dependencies and Models Setup (100% Complete)
- ✅ **Phase 2**: BLoC Architecture Setup (100% Complete)
- ✅ **Phase 3**: UI Components and Screens (100% Complete)
- ✅ **Phase 4**: Data Persistence (Local Storage 100%, Cloud Sync Pending)
- ✅ **Phase 5**: Navigation and Integration (100% Complete)
- ⚠️ **Phase 6**: Testing and Quality Assurance (30% Complete)
- ⏳ **Phase 7**: Documentation and Deployment (Pending)

### ✅ Successfully Implemented Features
1. **Complete 7-Screen Onboarding Flow**: Welcome → Personal Info → Physical Measurements → Health Info → Medical History → ASVS Assessment → Completion
2. **Robust Data Models**: AsvsUserProfile with comprehensive health data fields
3. **BLoC State Management**: OnboardingBloc and ProfileBloc with full event handling
4. **Local Data Persistence**: Hive integration with type-safe adapters
5. **Navigation Integration**: go_router with authentication-based routing
6. **Form Validation**: Field-level validation with formz package
7. **Responsive Design**: Works across mobile, tablet, and desktop
8. **Dependency Injection**: Proper service registration and lifecycle management

### ⚠️ Critical Issues Requiring Attention

#### 1. Test Coverage Gap (HIGH PRIORITY)
- **Missing**: BLoC tests, widget tests, integration tests
- **Impact**: No automated verification of core functionality
- **Recommendation**: Implement comprehensive test suite following face_verification patterns

#### 2. Flutter Analyze Issues (MEDIUM PRIORITY)
- **Current**: 48 issues (down from 86)
- **Types**: Generated code warnings, line length violations, style issues
- **Target**: 0 issues for production readiness

#### 3. Firebase Cloud Sync (LOW PRIORITY)
- **Status**: Local storage complete, cloud sync architecture ready
- **Impact**: No real-time data synchronization across devices
- **Recommendation**: Implement when multi-device support is required

### 🎯 Immediate Next Steps (Priority Order)

#### Week 1: Test Implementation
1. **BLoC Tests**: OnboardingBloc and ProfileBloc state transitions
2. **Service Tests**: OnboardingService validation and business logic
3. **Repository Tests**: Data persistence and error handling
4. **Widget Tests**: All 7 onboarding screens

#### Week 2: Code Quality
1. **Flutter Analyze**: Address remaining 48 issues
2. **Code Coverage**: Achieve 80%+ coverage target
3. **Performance Testing**: Measure and optimize onboarding flow

#### Week 3: Advanced Features (Optional)
1. **Firebase Cloud Sync**: Implement Firestore integration
2. **Security Enhancements**: Data encryption and privacy compliance
3. **Advanced Validation**: Cross-field validation and health risk assessment

### 🔧 Technical Verification Completed
- ✅ App builds and runs successfully on web platform
- ✅ Hive initialization and adapter registration working
- ✅ Dependency injection properly configured
- ✅ Navigation flow functional
- ✅ No critical runtime errors detected
- ✅ All onboarding screens accessible and functional

### 📊 Quality Metrics Status
| Metric | Target | Current | Status |
|--------|--------|---------|--------|
| Flutter Analyze Issues | 0 | 48 | ⚠️ Needs Work |
| Test Coverage | 80%+ | ~15% | ❌ Critical Gap |
| Core Functionality | 100% | 95% | ✅ Nearly Complete |
| Documentation | 100% | 90% | ✅ Good |
| Performance | Baseline | Not Measured | ⏳ Pending |

---

## Getting Started

The ASVS Onboarding system is **functionally complete** and ready for end-to-end testing. The core implementation provides a solid foundation with all major features working.

**For immediate use**: The onboarding flow can be tested by navigating to `/onboarding` route after authentication.

**For production readiness**: Complete the test implementation and address flutter analyze issues as outlined in the next steps above.

For questions or clarifications, refer to the existing codebase patterns and maintain consistency with the established architecture and design principles.
