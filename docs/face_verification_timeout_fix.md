# Face Verification Processing Timeout Fix

## Problem Analysis

The face verification feature was getting stuck on the "Processing video..." screen indefinitely after video recording completed. Through analysis of the logs and code flow, I identified the root cause:

### Root Cause
1. **Race Condition**: The camera widget was being disposed before Camer<PERSON><PERSON><PERSON> could complete the video recording and trigger the `onMediaTap` callback
2. **Missing Event**: The `VideoRecordingCompleted` event was never being sent to the BLoC because the callback was never called
3. **Infinite Wait**: The BLoC remained in the `Processing` state indefinitely, waiting for an event that would never come

### Flow Analysis
```
1. Recording completes → BLoC emits Processing state
2. Camera widget detects Processing state → calls _stopCameraRecording()
3. Camera widget gets disposed (due to navigation/state changes)
4. Camer<PERSON><PERSON><PERSON> finishes writing video file but widget is disposed
5. onMediaTap callback is never called or called on disposed widget
6. VideoRecordingCompleted event is never sent to BLoC
7. BLoC stays in Processing state forever
```

## Solution Implementation

### 1. Added Timeout Mechanism

**New Event**: `ProcessingTimeout`
```dart
class ProcessingTimeout extends FaceVideoCaptureEvent {
  const ProcessingTimeout();
}
```

**New Timer Field**: Added `_processingTimeoutTimer` to the BLoC

**Timeout Logic**: 
- Start 15-second timeout when entering Processing state
- Cancel timeout when `VideoRecordingCompleted` is received
- Trigger fallback processing if timeout expires

### 2. Fallback Video Processing

When timeout occurs, the BLoC attempts to:
1. Get expected video path from repository
2. Validate video file exists and has content
3. Calculate coverage statistics from detection results
4. Emit Success/Failure state based on validation

### 3. Key Code Changes

**BLoC Event Handler Registration**:
```dart
on<ProcessingTimeout>(_onProcessingTimeout);
```

**Timeout Timer Management**:
```dart
// Start timeout in _onStopRecording
_startProcessingTimeout();

// Cancel timeout in _onVideoRecordingCompleted  
_processingTimeoutTimer?.cancel();

// Cancel in cleanup methods
_processingTimeoutTimer?.cancel();
```

**Fallback Processing**:
```dart
Future<void> _onProcessingTimeout(
  ProcessingTimeout event,
  Emitter<FaceVideoCaptureState> emit,
) async {
  // Validate expected video path exists
  // Process detection results
  // Emit Success/Failure based on quality
}
```

## Benefits

1. **Prevents Infinite Hang**: App will never get stuck in processing state
2. **Graceful Degradation**: Falls back to direct file validation if callback fails
3. **User Experience**: Clear error messages if processing fails completely
4. **Robust Error Handling**: Handles various failure scenarios

## Testing Strategy

The timeout mechanism can be tested by:
1. Triggering the timeout manually in tests
2. Simulating camera widget disposal scenarios
3. Verifying fallback processing works with valid video files
4. Ensuring proper error handling for invalid scenarios

## Configuration

- **Timeout Duration**: 15 seconds (configurable)
- **Fallback Validation**: Uses existing video validation service
- **Error Recovery**: Maintains existing error handling patterns

This solution ensures the face verification feature is robust and provides a good user experience even when the underlying camera integration has timing issues.
