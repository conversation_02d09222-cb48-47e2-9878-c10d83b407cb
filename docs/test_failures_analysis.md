# Test Failures Analysis

## Summary
- **Total Failures**: 208
- **Modules Affected**: 4
- **Error Types**: 2
- **Unique Symptoms**: 16

## Failure Distribution by Module

| Module | File | Error Type | Root Symptom | Count |
|--------|------|------------|--------------|-------|
| features/onboarding | physical_measurements_screen_test.dart, medical_history_screen_test.dart, completion_screen_test.dart, +3 more | Exception | ══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════ | 78 |
| features/face_verification | face_video_capture_page_test.dart, video_validation_service_test.dart, face_guide_overlay_test.dart, +1 more | Exception | ══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════ | 47 |
| features/onboarding | welcome_screen_test.dart | Layout/Overflow | 132 | 23 |
| features/face_verification | face_video_capture_page_test.dart, video_upload_service_auth_test.dart, face_guide_overlay_test.dart | Exception | ══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════ | 16 |
| features/onboarding | medical_history_screen_test.dart, physical_measurements_screen_test.dart, asvs_assessment_screen_test.dart | Exception | The finder "Found 0 widgets with text "Previous": []" (used in a call to "tap()") could not find any | 6 |
| features/onboarding | medical_history_screen_test.dart, asvs_assessment_screen_test.dart | Layout/Overflow | 13 | 5 |
| features/onboarding | physical_measurements_screen_test.dart, personal_info_screen_test.dart | Exception | ══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════ | 5 |
| auth | auth_routing_test.dart | Layout/Overflow | 47 | 4 |
| features/video_gallery | video_gallery_page_test.dart, video_gallery_service_test.dart | Exception | ══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════ | 3 |
| features/onboarding | welcome_screen_test.dart | Layout/Overflow | 568 | 3 |
| features/onboarding | medical_history_screen_test.dart | Layout/Overflow | 2.5 | 3 |
| features/face_verification | face_video_capture_page_test.dart | Exception | The finder "Found 0 widgets with type "FloatingActionButton": []" (used in a call to "tap()") could | 2 |
| features/face_verification | face_video_capture_page_test.dart | Exception | The finder "Found 0 widgets with text "Try Again": []" (used in a call to "tap()") could not find | 2 |
| features/video_gallery | video_gallery_page_test.dart | Layout/Overflow | 132 | 2 |
| features/onboarding | welcome_screen_test.dart | Exception | The finder "Found 0 widgets with text "Get Started": []" (used in a call to "tap()") could not find | 2 |
| features/onboarding | completion_screen_test.dart | Exception | The finder "Found 0 widgets with text "Continue to App": []" (used in a call to "tap()") could not | 2 |
| features/onboarding | medical_history_screen_test.dart, asvs_assessment_screen_test.dart | Exception | The finder "Found 0 widgets with text "Next": []" (used in a call to "tap()") could not find any | 2 |
| features/face_verification | face_video_capture_page_test.dart | Exception | ══╡ EXCEPTION CAUGHT BY WIDGET INSPECTOR ╞══════════════════════════════════════════════════════════ | 1 |
| features/video_gallery | video_gallery_service_test.dart | Exception | No GoRouter found in context | 1 |
| features/onboarding | completion_screen_test.dart | Exception | easily create an instance of `UpdateProfile`, consider defining a `Fake`: | 1 |


## Priority Analysis (Common Root Causes)

### Top Error Types
- **Exception**: 168 occurrences
- **Layout/Overflow**: 40 occurrences


### Most Affected Modules
- **features/onboarding**: 130 failures
- **features/face_verification**: 68 failures
- **features/video_gallery**: 6 failures
- **auth**: 4 failures


### Most Common Symptoms
- **══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════**: 128 occurrences
- **132**: 25 occurrences
- **══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════**: 21 occurrences
- **The finder "Found 0 widgets with text "Previous": []" (used in a call to "tap()") could not find any**: 6 occurrences
- **13**: 5 occurrences
- **47**: 4 occurrences
- **568**: 3 occurrences
- **2.5**: 3 occurrences
- **The finder "Found 0 widgets with type "FloatingActionButton": []" (used in a call to "tap()") could**: 2 occurrences
- **The finder "Found 0 widgets with text "Try Again": []" (used in a call to "tap()") could not find**: 2 occurrences
- **The finder "Found 0 widgets with text "Get Started": []" (used in a call to "tap()") could not find**: 2 occurrences
- **The finder "Found 0 widgets with text "Continue to App": []" (used in a call to "tap()") could not**: 2 occurrences
- **The finder "Found 0 widgets with text "Next": []" (used in a call to "tap()") could not find any**: 2 occurrences
- **══╡ EXCEPTION CAUGHT BY WIDGET INSPECTOR ╞══════════════════════════════════════════════════════════**: 1 occurrences
- **No GoRouter found in context**: 1 occurrences


## Detailed Breakdown by Module

### auth (4 failures)

#### Layout/Overflow (4 failures)
- **47** (4 occurrences)
  - Files: auth_routing_test.dart

### features/face_verification (68 failures)

#### Exception (68 failures)
- **══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════** (47 occurrences)
  - Files: face_guide_overlay_test.dart, face_video_capture_page_test.dart, video_upload_service_auth_test.dart, video_validation_service_test.dart
- **══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════** (16 occurrences)
  - Files: face_guide_overlay_test.dart, face_video_capture_page_test.dart, video_upload_service_auth_test.dart
- **The finder "Found 0 widgets with type "FloatingActionButton": []" (used in a call to "tap()") could** (2 occurrences)
  - Files: face_video_capture_page_test.dart
- **The finder "Found 0 widgets with text "Try Again": []" (used in a call to "tap()") could not find** (2 occurrences)
  - Files: face_video_capture_page_test.dart
- **══╡ EXCEPTION CAUGHT BY WIDGET INSPECTOR ╞══════════════════════════════════════════════════════════** (1 occurrences)
  - Files: face_video_capture_page_test.dart

### features/onboarding (130 failures)

#### Exception (96 failures)
- **══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════** (78 occurrences)
  - Files: asvs_assessment_screen_test.dart, completion_screen_test.dart, medical_history_screen_test.dart, personal_info_screen_test.dart, physical_measurements_screen_test.dart, welcome_screen_test.dart
- **The finder "Found 0 widgets with text "Previous": []" (used in a call to "tap()") could not find any** (6 occurrences)
  - Files: asvs_assessment_screen_test.dart, medical_history_screen_test.dart, physical_measurements_screen_test.dart
- **══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════** (5 occurrences)
  - Files: personal_info_screen_test.dart, physical_measurements_screen_test.dart
- **The finder "Found 0 widgets with text "Get Started": []" (used in a call to "tap()") could not find** (2 occurrences)
  - Files: welcome_screen_test.dart
- **The finder "Found 0 widgets with text "Continue to App": []" (used in a call to "tap()") could not** (2 occurrences)
  - Files: completion_screen_test.dart
- **The finder "Found 0 widgets with text "Next": []" (used in a call to "tap()") could not find any** (2 occurrences)
  - Files: asvs_assessment_screen_test.dart, medical_history_screen_test.dart
- **easily create an instance of `UpdateProfile`, consider defining a `Fake`:** (1 occurrences)
  - Files: completion_screen_test.dart

#### Layout/Overflow (34 failures)
- **132** (23 occurrences)
  - Files: welcome_screen_test.dart
- **13** (5 occurrences)
  - Files: asvs_assessment_screen_test.dart, medical_history_screen_test.dart
- **568** (3 occurrences)
  - Files: welcome_screen_test.dart
- **2.5** (3 occurrences)
  - Files: medical_history_screen_test.dart

### features/video_gallery (6 failures)

#### Exception (4 failures)
- **══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════** (3 occurrences)
  - Files: video_gallery_page_test.dart, video_gallery_service_test.dart
- **No GoRouter found in context** (1 occurrences)
  - Files: video_gallery_service_test.dart

#### Layout/Overflow (2 failures)
- **132** (2 occurrences)
  - Files: video_gallery_page_test.dart

