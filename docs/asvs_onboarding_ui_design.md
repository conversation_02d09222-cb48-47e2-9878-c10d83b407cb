# ASVS Onboarding UI Design Specifications

## Overview

This document outlines the specific UI design requirements for the ASVS Onboarding flow based on client-provided mockups. The design follows a clean, minimalist approach with clear visual hierarchy and intuitive user interactions.

## Design Principles

- **Minimalist Design**: Clean white background with subtle gray text
- **Progressive Disclosure**: One primary action per screen
- **Clear Visual Hierarchy**: Large headings, consistent spacing
- **Touch-Friendly**: Large buttons and input areas
- **Progress Indication**: Visual progress indicators at bottom

## Screen Flow and Components

### Screen 1: Personal Information
**Fields:**
- First Name (text input)
- Last Name (text input)
- Birthdate (date picker with "Set Date" button)
- Gender (dropdown with "Set Gender" button)

**UI Elements:**
- Large field labels in gray text
- Black action buttons with white text
- User Agreement link at bottom
- Progress dots (1 of 4 active)
- Bottom progress bar

### Screen 2: Physical Measurements
**Fields:**
- Units (dropdown with "Set Units" button)
- Weight (input with "Set Weight" button)
- Height (input with "Set Height" button)

**UI Elements:**
- Consistent button styling from Screen 1
- Progress dots (2 of 4 active)
- Same layout pattern as previous screen

### Screen 3: Health Conditions
**Header:** "I HAVE" (large, centered)

**Fields:**
- Hypertension (selection button)
- Diabetes (selection button)

**UI Elements:**
- Centered layout
- Large condition labels
- "Select" buttons for each condition
- Progress dots (3 of 4 active)

### Screen 4: Meal Timing
**Header:** "LUNCH TIME" (large, centered)

**Components:**
- Time display: "00 : 00" (large blue text)
- Time picker wheel showing hours (15-19 visible range)
- "Get Started" button (final action)

**UI Elements:**
- Blue accent color for time display
- Scrollable time picker
- Final progress state (4 of 4)

## Color Palette

### Primary Colors
- **Background**: White (#FFFFFF)
- **Text Primary**: Black (#000000)
- **Text Secondary**: Gray (#666666)
- **Accent**: Blue (#007AFF) - for time display and active states

### Button Styling
- **Primary Buttons**: Black background, white text, rounded corners
- **Button Text**: Medium weight, clear contrast
- **Button Size**: Large touch targets (minimum 44pt height)

## Typography

### Hierarchy
- **Screen Headers**: Large, bold, centered
- **Field Labels**: Medium size, gray color, left-aligned
- **Button Text**: Medium weight, high contrast
- **Time Display**: Large, blue, monospace-style

### Spacing
- **Vertical Spacing**: Consistent gaps between elements
- **Horizontal Margins**: Adequate side margins for readability
- **Button Spacing**: Clear separation between interactive elements

## Interactive Elements

### Input Fields
- Text inputs with clear labels
- Date/time pickers with dedicated buttons
- Dropdown selectors with action buttons

### Navigation
- Progress indicators at bottom
- No back buttons visible (swipe navigation)
- Clear forward progression

### Feedback
- Visual state changes for selections
- Progress indication throughout flow
- Clear completion state

## Responsive Considerations

- **Mobile-First**: Optimized for phone screens
- **Touch Targets**: Minimum 44pt for all interactive elements
- **Scrolling**: Vertical scroll when needed
- **Orientation**: Portrait orientation primary

## Accessibility

- **High Contrast**: Clear text/background contrast
- **Large Text**: Readable font sizes
- **Touch Targets**: Adequate button sizes
- **Screen Reader**: Semantic markup for assistive technology

## Implementation Notes

### Flutter Widgets
- Use `flutter_onboarding_slider` for main flow
- `dropdown_button2` for enhanced dropdowns
- `flutter_datetime_picker_plus` for date/time selection
- Custom styling to match design specifications

### State Management
- BLoC pattern for form state
- Validation on field completion
- Progress tracking through flow

### Data Collection
- Real-time validation
- Local storage during flow
- Final submission on completion

---

## Next Steps

1. Implement UI components matching these specifications
2. Create custom themes for consistent styling
3. Build responsive layouts for different screen sizes
4. Add accessibility features and testing
5. Integrate with BLoC state management
