# Firestore Video Metadata Schema

## Overview
This document describes the Firestore collection structure for storing face verification video metadata in the `bloomg-flutter` project.

## Collection: `face_verification_videos`

### Document Structure

Each document represents metadata for a single face verification video uploaded by a user.

```typescript
{
  // Document ID: Auto-generated by Firestore
  
  // User Information
  userId: string,                    // Firebase Auth UID of the user who uploaded the video
  
  // File Information
  fileName: string,                  // Original filename (e.g., "face_verification_2024-01-15_10-30-45.mp4")
  downloadUrl: string,               // Firebase Storage download URL for the video
  storagePath: string,               // Firebase Storage path (e.g., "face_verification_videos/{userId}/{videoId}")
  fileSize: number,                  // File size in bytes
  
  // Timing Information
  uploadTimestamp: Timestamp,        // When the video was uploaded to Firebase
  durationSeconds: number,           // Video duration in seconds (typically 9)
  
  // Quality Information
  qualityScore: number,              // Overall quality score (0-100) from face verification
  faceCoverageStats: {               // Detailed face detection statistics
    total_frames: number,            // Total number of frames analyzed
    frames_with_face: number,        // Frames where a face was detected
    frames_with_valid_coverage: number, // Frames with ≥80% face coverage
    average_coverage: number,        // Average face coverage percentage
    minimum_coverage: number,        // Minimum face coverage recorded
    maximum_coverage: number,        // Maximum face coverage recorded
    recording_duration_ms: number,   // Recording duration in milliseconds
    quality_score: number,           // Quality score from validation service
    detection_results: [             // Array of individual frame results
      {
        timestamp_ms: number,        // Frame timestamp in milliseconds
        face_detected: boolean,      // Whether a face was detected
        coverage_percentage: number, // Face coverage percentage for this frame
        confidence: number           // Detection confidence (0-1)
      }
    ]
  },
  
  // Thumbnail Information (Optional)
  thumbnailUrl?: string,             // Firebase Storage download URL for thumbnail
  thumbnailStoragePath?: string,     // Firebase Storage path for thumbnail
  
  // Processing Status
  isProcessed: boolean,              // Whether video processing is complete (default: true)
  processingError?: string           // Error message if processing failed
}
```

### Indexes

The following Firestore indexes are recommended for optimal query performance:

1. **User Videos Query**
   - Collection: `face_verification_videos`
   - Fields: `userId` (Ascending), `uploadTimestamp` (Descending)
   - Query scope: Collection

2. **User Videos by Quality**
   - Collection: `face_verification_videos`
   - Fields: `userId` (Ascending), `qualityScore` (Descending), `uploadTimestamp` (Descending)
   - Query scope: Collection

### Security Rules

The collection is protected by Firebase Storage security rules that ensure:

1. **Authentication Required**: Only authenticated users can access the collection
2. **User Isolation**: Users can only access their own video metadata
3. **Read/Write Permissions**: Users can create, read, update, and delete their own video metadata
4. **Data Validation**: Uploaded data must conform to expected structure

### Usage Examples

#### Save Video Metadata
```dart
final metadata = FirebaseVideoMetadata(
  id: '', // Will be set by Firestore
  userId: currentUser.uid,
  fileName: 'face_verification_2024-01-15_10-30-45.mp4',
  downloadUrl: 'https://firebasestorage.googleapis.com/...',
  storagePath: 'face_verification_videos/user123/video456',
  uploadTimestamp: DateTime.now(),
  fileSize: 2048000,
  duration: Duration(seconds: 9),
  qualityScore: 85.5,
  faceCoverageStats: coverageStats,
);

final documentId = await metadataService.saveVideoMetadata(metadata);
```

#### Fetch User Videos
```dart
final videos = await metadataService.getUserVideos(currentUser.uid);
```

#### Real-time Updates
```dart
final stream = metadataService.getUserVideosStream(currentUser.uid);
stream.listen((videos) {
  // Handle real-time video list updates
});
```

### Migration Notes

When migrating from local storage to Firebase:

1. **Existing Videos**: Local videos will need to be uploaded to Firebase Storage
2. **Metadata Extraction**: Quality scores and face coverage stats need to be extracted from existing videos
3. **Thumbnail Generation**: Thumbnails should be regenerated and uploaded to Firebase Storage
4. **User Association**: Videos need to be associated with the correct Firebase Auth user ID

### Performance Considerations

1. **Pagination**: For users with many videos, implement pagination using Firestore's `limit()` and `startAfter()` methods
2. **Caching**: Implement local caching for frequently accessed video metadata
3. **Offline Support**: Use Firestore's offline persistence for better user experience
4. **Batch Operations**: Use batch writes when updating multiple video metadata documents

### Monitoring and Analytics

Consider tracking the following metrics:

1. **Upload Success Rate**: Percentage of successful video uploads
2. **Average Upload Time**: Time taken to upload videos and save metadata
3. **Storage Usage**: Total storage used per user and across the app
4. **Quality Distribution**: Distribution of video quality scores
5. **Error Rates**: Frequency and types of upload/processing errors
