# Face Verification Edge Cases

## Catalogue of Public Contracts

### Events
- `InitializeCamera`
- `StartCountdown`
- `CountdownTick`
- `StartRecording`
- `StopRecording`
- `VideoRecordingCompleted`
- `ProcessFrame`
- `RecordingProgress`
- `FaceDetectionStatusChanged`
- `AbortCountdown`
- `ResetCapture`
- `DisposeResources`

### States
- `Initial`
- `CameraInitializing`
- `CameraReady`
- `CountdownInProgress`
- `Recording`
- `Processing`
- `Success`
- `Failure`
- `Error`

### Service Methods
- `CameraService::initialize`, `startRecording`, `stopRecording`, `dispose`
- `FaceDetectionService::initialize`, `processImage`, `dispose`
- `VideoUploadService::uploadVideoWithProgress`, `deleteVideo`
- `VerificationApiService::submitVideoForVerification`

### Implicit Invariants
- Timers (`_countdownTimer`, `_recordingTimer`, `_faceDetectionTimer`) must be canceled on `dispose()`.
- Proper resource disposal on navigation and widget disposal.
- Camera and storage services must be initialized before use.
- Video file path generation and validation.
- Transitions between states must handle lifecycle changes like `AppLifecycleState`.

## Edge-Case Matrix

| Async Path | Error Branch | Timeout | Required Tests |
|------------|--------------|---------|----------------|
| Camera Initialization | Initialization failure | Retry logic | Test for initialization failure and recovery logic |
| Countdown Start | No face detected/recognized | Abort & retry | Validate countdown aborts when detection conditions not met |
| Recording | Storage failure, low coverage | Error handling, abort on low coverage | Verify recording stop on errors |
| Video Validation | File not found, poor quality | Fail fast | Confirm validation fails on file absence or poor score |
| API Submission | Network failure, invalid video | Retries, backoff | Handle API submission failures and recoverability |
