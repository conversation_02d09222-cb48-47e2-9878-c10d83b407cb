import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template home_page}
/// The home page of the application.
/// {@endtemplate}
class HomePage extends StatelessWidget {
  /// {@macro home_page}
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<AuthCubit>(),
      child: const HomeView(),
    );
  }
}

/// {@template home_view}
/// The home view widget.
/// {@endtemplate}
class HomeView extends StatelessWidget {
  /// {@macro home_view}
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bloomg'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _showLogoutDialog(context),
            tooltip: 'Logout',
          ),
        ],
      ),
      body: ResponsiveBreakpoints.of(context).isMobile
          ? const _MobileHomeContent()
          : const _DesktopHomeContent(),
    );
  }

  /// Shows the logout confirmation dialog.
  void _showLogoutDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              context.read<AuthCubit>().signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}

/// {@template mobile_home_content}
/// Mobile layout for the home content.
/// {@endtemplate}
class _MobileHomeContent extends StatelessWidget {
  /// {@macro mobile_home_content}
  const _MobileHomeContent();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final user = state.user;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppDimensions.paddingL),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back!',
                      style: AppTextStyles.heading1.copyWith(
                        color: AppColors.primary,
                        fontSize: 24,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.spacingM),
                    Text(
                      user.displayName ?? 'User',
                      style: AppTextStyles.bodyLarge,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppDimensions.spacingXL),

              // User information section
              _UserInfoSection(user: user),

              const SizedBox(height: AppDimensions.spacingXL),

              // Quick actions section
              const _QuickActionsSection(),
            ],
          ),
        );
      },
    );
  }
}

/// {@template desktop_home_content}
/// Desktop layout for the home content.
/// {@endtemplate}
class _DesktopHomeContent extends StatelessWidget {
  /// {@macro desktop_home_content}
  const _DesktopHomeContent();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final user = state.user;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left column - User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppDimensions.paddingL),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(AppDimensions.radiusM),
                        border: Border.all(
                          color: AppColors.primary.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back!',
                            style: AppTextStyles.heading1.copyWith(
                              color: AppColors.primary,
                              fontSize: 24,
                            ),
                          ),
                          const SizedBox(height: AppDimensions.spacingM),
                          Text(
                            user.displayName ?? 'User',
                            style: AppTextStyles.bodyLarge,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: AppDimensions.spacingXL),

                    // User information
                    _UserInfoSection(user: user),
                  ],
                ),
              ),

              const SizedBox(width: AppDimensions.spacingXL),

              // Right column - Quick actions
              const Expanded(
                child: _QuickActionsSection(),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// {@template user_info_section}
/// Section displaying user information.
/// {@endtemplate}
class _UserInfoSection extends StatelessWidget {
  /// {@macro user_info_section}
  const _UserInfoSection({
    required this.user,
  });

  /// The user to display information for.
  final UserModel user;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Account Information',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            _InfoRow(
              label: 'Email',
              value: user.email,
              icon: Icons.email,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _InfoRow(
              label: 'User ID',
              value: user.uid,
              icon: Icons.person,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _InfoRow(
              label: 'Email Verified',
              value: user.emailVerified ? 'Yes' : 'No',
              icon: user.emailVerified ? Icons.verified : Icons.warning,
              valueColor:
                  user.emailVerified ? AppColors.success : AppColors.warning,
            ),
            if (user.createdAt != null) ...[
              const SizedBox(height: AppDimensions.spacingS),
              _InfoRow(
                label: 'Member Since',
                value: _formatDate(user.createdAt!),
                icon: Icons.calendar_today,
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// {@template info_row}
/// A row displaying label and value information.
/// {@endtemplate}
class _InfoRow extends StatelessWidget {
  /// {@macro info_row}
  const _InfoRow({
    required this.label,
    required this.value,
    required this.icon,
    this.valueColor,
  });

  /// The label text.
  final String label;

  /// The value text.
  final String value;

  /// The icon to display.
  final IconData icon;

  /// Optional color for the value text.
  final Color? valueColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: valueColor ?? AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// {@template quick_actions_section}
/// Section with quick action buttons.
/// {@endtemplate}
class _QuickActionsSection extends StatelessWidget {
  /// {@macro quick_actions_section}
  const _QuickActionsSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Container(
        constraints: const BoxConstraints(maxHeight: 400),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
            Text(
              'Quick Actions',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            _ActionButton(
              icon: Icons.settings,
              label: 'Settings',
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Settings feature coming soon!'),
                  ),
                );
              },
            ),
            // Mobile-only features
            if (!kIsWeb) ...[
              const SizedBox(height: AppDimensions.spacingS),
              _ActionButton(
                icon: Icons.face,
                label: 'Face Verification',
                onPressed: () {
                  context.goToFaceVerification();
                },
              ),
              const SizedBox(height: AppDimensions.spacingS),
              _ActionButton(
                icon: Icons.video_library,
                label: 'Video Gallery',
                onPressed: () {
                  context.goToVideoGallery();
                },
              ),
            ],
            // Web-only message for mobile features
            if (kIsWeb) ...[
              const SizedBox(height: AppDimensions.spacingS),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(
                    color: Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.mobile_friendly,
                      color: Colors.grey[600],
                      size: 32,
                    ),
                    const SizedBox(height: AppDimensions.spacingS),
                    Text(
                      'Mobile Features',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.spacingXS),
                    Text(
                      'Face Verification and Video Gallery are available '
                      'on mobile devices',
                      textAlign: TextAlign.center,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: AppDimensions.spacingS),
            _ActionButton(
              icon: Icons.person,
              label: 'My Profile',
              onPressed: () {
                context.goToProfile();
              },
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _ActionButton(
              icon: Icons.assignment,
              label: 'Health Onboarding',
              onPressed: () {
                context.goToOnboarding();
              },
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _ActionButton(
              icon: Icons.help,
              label: 'Help & Support',
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Help feature coming soon!'),
                  ),
                );
              },
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _ActionButton(
              icon: Icons.logout,
              label: 'Logout',
              onPressed: () {
                showDialog<void>(
                  context: context,
                  builder: (dialogContext) => AlertDialog(
                    title: const Text('Confirm Logout'),
                    content: const Text('Are you sure you want to logout?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        child: const Text('Cancel'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(dialogContext).pop();
                          context.read<AuthCubit>().signOut();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.error,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Logout'),
                      ),
                    ],
                  ),
                );
              },
              isDestructive: true,
            ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}

/// {@template action_button}
/// A button for quick actions.
/// {@endtemplate}
class _ActionButton extends StatelessWidget {
  /// {@macro action_button}
  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onPressed,
    this.isDestructive = false,
  });

  /// The icon to display.
  final IconData icon;

  /// The button label.
  final String label;

  /// The callback when pressed.
  final VoidCallback onPressed;

  /// Whether this is a destructive action.
  final bool isDestructive;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: isDestructive ? AppColors.error : AppColors.primary,
        ),
        label: Text(
          label,
          style: TextStyle(
            color: isDestructive ? AppColors.error : AppColors.primary,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: isDestructive ? AppColors.error : AppColors.primary,
          ),
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.paddingM,
            horizontal: AppDimensions.paddingL,
          ),
        ),
      ),
    );
  }
}
