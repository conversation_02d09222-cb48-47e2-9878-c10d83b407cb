import 'package:bloomg_flutter/core/storage/hive_service.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// {@template asvs_profile_repository}
/// Repository for managing ASVS user profile data.
/// 
/// Handles local storage with Hive and cloud synchronization with Firebase.
/// Provides CRUD operations for ASVS user profiles with offline support.
/// {@endtemplate}
abstract class AsvsProfileRepository {
  /// Loads the current user's ASVS profile
  Future<AsvsUserProfile?> loadProfile();

  /// Saves the ASVS profile locally and syncs to cloud
  Future<void> saveProfile(AsvsUserProfile profile);

  /// Updates an existing ASVS profile
  Future<void> updateProfile(AsvsUserProfile profile);

  /// Deletes the current user's ASVS profile
  Future<void> deleteProfile();

  /// Checks if a profile exists for the current user
  Future<bool> hasProfile();

  /// Syncs local profile with cloud storage
  Future<void> syncProfile();

  /// Initializes the repository
  Future<void> initialize();

  /// Disposes resources
  Future<void> dispose();
}

/// {@template firebase_asvs_profile_repository}
/// Firebase implementation of AsvsProfileRepository.
/// 
/// Uses Hive for local storage and Firestore for cloud synchronization.
/// {@endtemplate}
class FirebaseAsvsProfileRepository implements AsvsProfileRepository {
  /// {@macro firebase_asvs_profile_repository}
  FirebaseAsvsProfileRepository({
    required HiveService hiveService,
    required FirebaseFirestore firestore,
    required FirebaseAuth firebaseAuth,
  })  : _hiveService = hiveService,
        _firestore = firestore,
        _firebaseAuth = firebaseAuth;

  final HiveService _hiveService;
  final FirebaseFirestore _firestore;
  final FirebaseAuth _firebaseAuth;
  final LoggerService _logger = LoggerService();

  static const String _collectionName = 'asvs_profiles';
  static const String _profileKey = 'current_profile';

  @override
  Future<void> initialize() async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Repository initialization started',
        ),
      );

      // Ensure Hive service is initialized
      await _hiveService.initialize();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Repository initialized successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Repository initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<AsvsUserProfile?> loadProfile() async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Loading profile',
        ),
      );

      // Try to load from local storage first
      final localProfile = await _loadLocalProfile();
      if (localProfile != null) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.onboardingModule,
            'Profile loaded from local storage',
          ),
        );
        return localProfile;
      }

      // If no local profile, try to load from cloud
      final cloudProfile = await _loadCloudProfile();
      if (cloudProfile != null) {
        // Save to local storage for offline access
        await _saveLocalProfile(cloudProfile);
        
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.onboardingModule,
            'Profile loaded from cloud and cached locally',
          ),
        );
        return cloudProfile;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'No profile found',
        ),
      );
      return null;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to load profile: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> saveProfile(AsvsUserProfile profile) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Saving profile',
          'Onboarding complete: ${profile.isOnboardingComplete}',
        ),
      );

      // Save to local storage first for immediate access
      await _saveLocalProfile(profile);

      // Then sync to cloud
      await _saveCloudProfile(profile);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile saved successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to save profile: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> updateProfile(AsvsUserProfile profile) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Updating profile',
        ),
      );

      // Update the profile with current timestamp
      final updatedProfile = profile.copyWith(
        updatedAt: DateTime.now(),
      );

      await saveProfile(updatedProfile);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile updated successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to update profile: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> deleteProfile() async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Deleting profile',
        ),
      );

      // Delete from local storage
      await _deleteLocalProfile();

      // Delete from cloud storage
      await _deleteCloudProfile();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile deleted successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to delete profile: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<bool> hasProfile() async {
    try {
      final profile = await loadProfile();
      return profile != null;
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Error checking profile existence',
          'Error: $error',
        ),
      );
      return false;
    }
  }

  @override
  Future<void> syncProfile() async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Syncing profile',
        ),
      );

      final localProfile = await _loadLocalProfile();
      if (localProfile == null) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.onboardingModule,
            'No local profile to sync',
          ),
        );
        return;
      }

      await _saveCloudProfile(localProfile);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile synced successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to sync profile: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Repository disposed',
      ),
    );
  }

  // Private helper methods

  Future<AsvsUserProfile?> _loadLocalProfile() async {
    try {
      final box = _hiveService.asvsProfileBox;
      return box.get(_profileKey);
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Failed to load local profile',
          'Error: $error',
        ),
      );
      return null;
    }
  }

  Future<void> _saveLocalProfile(AsvsUserProfile profile) async {
    final box = _hiveService.asvsProfileBox;
    await box.put(_profileKey, profile);
  }

  Future<void> _deleteLocalProfile() async {
    final box = _hiveService.asvsProfileBox;
    await box.delete(_profileKey);
  }

  Future<AsvsUserProfile?> _loadCloudProfile() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'No authenticated user for cloud profile load',
        ),
      );
      return null;
    }

    try {
      final doc = await _firestore
          .collection(_collectionName)
          .doc(user.uid)
          .get();

      if (!doc.exists || doc.data() == null) {
        return null;
      }

      return AsvsUserProfile.fromJson(doc.data()!);
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Failed to load cloud profile',
          'Error: $error',
        ),
      );
      return null;
    }
  }

  Future<void> _saveCloudProfile(AsvsUserProfile profile) async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'No authenticated user for cloud profile save',
        ),
      );
      return;
    }

    await _firestore
        .collection(_collectionName)
        .doc(user.uid)
        .set(profile.toJson());
  }

  Future<void> _deleteCloudProfile() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'No authenticated user for cloud profile delete',
        ),
      );
      return;
    }

    await _firestore
        .collection(_collectionName)
        .doc(user.uid)
        .delete();
  }
}
