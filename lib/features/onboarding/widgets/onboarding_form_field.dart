import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// {@template onboarding_form_field}
/// Reusable form field widget for onboarding screens.
/// Provides consistent styling and validation display.
/// {@endtemplate}
class OnboardingFormField extends StatelessWidget {
  /// {@macro onboarding_form_field}
  const OnboardingFormField({
    required this.label,
    required this.child,
    this.error,
    this.isRequired = false,
    super.key,
  });

  /// Field label text
  final String label;

  /// Input widget (TextField, DropdownButton, etc.)
  final Widget child;

  /// Error message to display
  final String? error;

  /// Whether the field is required
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        Row(
          children: [
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: AppDimensions.spacingXS),
              Text(
                '*',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),

        // Input field
        child,

        // Error message
        if (error != null) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            error!,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.error,
            ),
          ),
        ],

        const SizedBox(height: AppDimensions.spacingL),
      ],
    );
  }
}

/// {@template onboarding_text_field}
/// Styled text field for onboarding forms.
/// {@endtemplate}
class OnboardingTextField extends StatelessWidget {
  /// {@macro onboarding_text_field}
  const OnboardingTextField({
    required this.hintText,
    this.controller,
    this.onChanged,
    this.keyboardType,
    this.textInputAction,
    this.maxLines = 1,
    this.enabled = true,
    this.inputFormatters,
    super.key,
  });

  /// Hint text for the field
  final String hintText;

  /// Text editing controller
  final TextEditingController? controller;

  /// Callback when text changes
  final ValueChanged<String>? onChanged;

  /// Keyboard type
  final TextInputType? keyboardType;

  /// Text input action
  final TextInputAction? textInputAction;

  /// Maximum number of lines
  final int maxLines;

  /// Whether the field is enabled
  final bool enabled;

  /// Input formatters
  final List<TextInputFormatter>? inputFormatters;

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      onChanged: onChanged,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      maxLines: maxLines,
      enabled: enabled,
      inputFormatters: inputFormatters,
      style: AppTextStyles.input,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary.withValues(alpha: 0.6),
        ),
        filled: true,
        fillColor: AppColors.inputBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(
            color: AppColors.error,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingM,
        ),
      ),
    );
  }
}

/// {@template onboarding_action_button}
/// Styled action button for onboarding forms.
/// {@endtemplate}
class OnboardingActionButton extends StatelessWidget {
  /// {@macro onboarding_action_button}
  const OnboardingActionButton({
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    super.key,
  });

  /// Button text
  final String text;

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is in loading state
  final bool isLoading;

  /// Whether the button is enabled
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          disabledBackgroundColor: AppColors.surface,
          disabledForegroundColor: AppColors.textSecondary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.textOnPrimary,
                  ),
                ),
              )
            : Text(
                text,
                style: AppTextStyles.button,
              ),
      ),
    );
  }
}
