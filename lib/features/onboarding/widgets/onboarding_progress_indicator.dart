import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:flutter/material.dart';

/// {@template onboarding_progress_indicator}
/// Progress indicator widget for onboarding flow.
/// Shows current step and overall progress.
/// {@endtemplate}
class OnboardingProgressIndicator extends StatelessWidget {
  /// {@macro onboarding_progress_indicator}
  const OnboardingProgressIndicator({
    required this.currentStep,
    required this.totalSteps,
    this.showStepNumbers = false,
    super.key,
  });

  /// Current step (0-based)
  final int currentStep;

  /// Total number of steps
  final int totalSteps;

  /// Whether to show step numbers
  final bool showStepNumbers;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Progress dots
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            totalSteps,
            _buildProgressDot,
          ),
        ),

        const SizedBox(height: AppDimensions.spacingM),

        // Progress bar
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: (currentStep + 1) / totalSteps,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),

        if (showStepNumbers) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'Step ${currentStep + 1} of $totalSteps',
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildProgressDot(int index) {
    final isActive = index <= currentStep;
    final isCompleted = index < currentStep;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingXS,
      ),
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive ? AppColors.primary : AppColors.surface,
        border: isCompleted
            ? null
            : Border.all(
                color: isActive
                    ? AppColors.primary
                    : AppColors.textSecondary.withValues(alpha: 0.3),
              ),
      ),
      child: isCompleted
          ? const Icon(
              Icons.check,
              size: 8,
              color: AppColors.textOnPrimary,
            )
          : null,
    );
  }
}

/// {@template onboarding_step_header}
/// Header widget for onboarding steps with title and subtitle.
/// {@endtemplate}
class OnboardingStepHeader extends StatelessWidget {
  /// {@macro onboarding_step_header}
  const OnboardingStepHeader({
    required this.title,
    this.subtitle,
    super.key,
  });

  /// Step title
  final String title;

  /// Optional subtitle
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: AppColors.textPrimary,
            fontSize: 28,
            fontWeight: FontWeight.w300,
          ),
        ),
        if (subtitle != null) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            subtitle!,
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: 16,
            ),
          ),
        ],
        const SizedBox(height: AppDimensions.spacingXXL),
      ],
    );
  }
}
