import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart'
    as date_picker;

/// {@template onboarding_dropdown_field}
/// Dropdown field widget for onboarding forms.
/// {@endtemplate}
class OnboardingDropdownField<T> extends StatelessWidget {
  /// {@macro onboarding_dropdown_field}
  const OnboardingDropdownField({
    required this.value,
    required this.items,
    required this.onChanged,
    required this.hintText,
    this.itemBuilder,
    super.key,
  });

  /// Current selected value
  final T? value;

  /// List of dropdown items
  final List<T> items;

  /// Callback when selection changes
  final ValueChanged<T?> onChanged;

  /// Hint text when no value is selected
  final String hintText;

  /// Custom item builder
  final Widget Function(T item)? itemBuilder;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<T>(
        value: value,
        hint: Text(
          hintText,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary.withValues(alpha: 0.6),
          ),
        ),
        items: items.map((T item) {
          return DropdownMenuItem<T>(
            value: item,
            child: itemBuilder?.call(item) ??
                Text(
                  item.toString(),
                  style: AppTextStyles.input,
                ),
          );
        }).toList(),
        onChanged: onChanged,
        buttonStyleData: ButtonStyleData(
          height: AppDimensions.buttonHeight,
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
          ),
          decoration: BoxDecoration(
            color: AppColors.inputBackground,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
            ),
          ),
          maxHeight: 200,
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 40,
          padding: EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
          ),
        ),
      ),
    );
  }
}

/// {@template onboarding_date_picker_field}
/// Date picker field widget for onboarding forms.
/// {@endtemplate}
class OnboardingDatePickerField extends StatelessWidget {
  /// {@macro onboarding_date_picker_field}
  const OnboardingDatePickerField({
    required this.value,
    required this.onChanged,
    required this.hintText,
    this.firstDate,
    this.lastDate,
    super.key,
  });

  /// Current selected date
  final DateTime? value;

  /// Callback when date changes
  final ValueChanged<DateTime?> onChanged;

  /// Hint text when no date is selected
  final String hintText;

  /// Earliest selectable date
  final DateTime? firstDate;

  /// Latest selectable date
  final DateTime? lastDate;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showDatePicker(context),
      child: Container(
        height: AppDimensions.buttonHeight,
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingM,
        ),
        decoration: BoxDecoration(
          color: AppColors.inputBackground,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                value != null ? _formatDate(value!) : hintText,
                style: value != null
                    ? AppTextStyles.input
                    : AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary.withValues(alpha: 0.6),
                      ),
              ),
            ),
            const Icon(
              Icons.calendar_today,
              color: AppColors.textSecondary,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _showDatePicker(BuildContext context) {
    date_picker.DatePicker.showDatePicker(
      context,
      minTime: firstDate ?? DateTime(1900),
      maxTime: lastDate ?? DateTime.now(),
      currentTime: value ?? DateTime.now(),
      theme: const date_picker.DatePickerTheme(
        backgroundColor: AppColors.surface,
        itemStyle: TextStyle(
          color: AppColors.textPrimary,
          fontSize: 18,
        ),
        doneStyle: TextStyle(
          color: AppColors.primary,
          fontSize: 16,
        ),
        cancelStyle: TextStyle(
          color: AppColors.textSecondary,
          fontSize: 16,
        ),
      ),
      onConfirm: onChanged,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// {@template onboarding_slider_field}
/// Slider field widget for rating inputs (1-10 scale).
/// {@endtemplate}
class OnboardingSliderField extends StatelessWidget {
  /// {@macro onboarding_slider_field}
  const OnboardingSliderField({
    required this.value,
    required this.onChanged,
    required this.label,
    this.min = 1,
    this.max = 10,
    this.divisions,
    super.key,
  });

  /// Current slider value
  final double value;

  /// Callback when value changes
  final ValueChanged<double> onChanged;

  /// Slider label
  final String label;

  /// Minimum value
  final double min;

  /// Maximum value
  final double max;

  /// Number of divisions
  final int? divisions;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                label,
                style: AppTextStyles.bodyMedium,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.spacingM,
                vertical: AppDimensions.spacingS,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Text(
                value.round().toString(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.primary,
            inactiveTrackColor: AppColors.surface,
            thumbColor: AppColors.primary,
            overlayColor: AppColors.primary.withValues(alpha: 0.2),
            valueIndicatorColor: AppColors.primary,
            valueIndicatorTextStyle: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textOnPrimary,
            ),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions ?? (max - min).round(),
            onChanged: onChanged,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              min.round().toString(),
              style: AppTextStyles.bodySmall,
            ),
            Text(
              max.round().toString(),
              style: AppTextStyles.bodySmall,
            ),
          ],
        ),
      ],
    );
  }
}
