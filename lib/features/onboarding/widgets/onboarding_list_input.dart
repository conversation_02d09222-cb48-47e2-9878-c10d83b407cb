import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:flutter/material.dart';

/// {@template onboarding_list_input}
/// Widget for inputting and managing lists of items (medications, allergies, etc.).
/// {@endtemplate}
class OnboardingListInput extends StatefulWidget {
  /// {@macro onboarding_list_input}
  const OnboardingListInput({
    required this.items,
    required this.onChanged,
    required this.hintText,
    this.maxItems,
    super.key,
  });

  /// Current list of items
  final List<String> items;

  /// Callback when list changes
  final ValueChanged<List<String>> onChanged;

  /// Hint text for input field
  final String hintText;

  /// Maximum number of items allowed
  final int? maxItems;

  @override
  State<OnboardingListInput> createState() => _OnboardingListInputState();
}

class _OnboardingListInputState extends State<OnboardingListInput> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _addItem() {
    final text = _controller.text.trim();
    if (text.isNotEmpty && !widget.items.contains(text)) {
      if (widget.maxItems == null || widget.items.length < widget.maxItems!) {
        final updatedItems = [...widget.items, text];
        widget.onChanged(updatedItems);
        _controller.clear();
      }
    }
  }

  void _removeItem(String item) {
    final updatedItems = widget.items.where((i) => i != item).toList();
    widget.onChanged(updatedItems);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Input field
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                style: AppTextStyles.input,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary.withValues(alpha: 0.6),
                  ),
                  filled: true,
                  fillColor: AppColors.inputBackground,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    borderSide: const BorderSide(
                      color: AppColors.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingM,
                  ),
                ),
                onSubmitted: (_) => _addItem(),
                textInputAction: TextInputAction.done,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingS),
            IconButton(
              onPressed: _addItem,
              icon: const Icon(
                Icons.add_circle,
                color: AppColors.primary,
                size: 28,
              ),
            ),
          ],
        ),

        // Items list
        if (widget.items.isNotEmpty) ...[
          const SizedBox(height: AppDimensions.spacingM),
          Wrap(
            spacing: AppDimensions.spacingS,
            runSpacing: AppDimensions.spacingS,
            children: widget.items.map(_buildItemChip).toList(),
          ),
        ],

        // Max items indicator
        if (widget.maxItems != null) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            '${widget.items.length}/${widget.maxItems} items',
            style: AppTextStyles.bodySmall.copyWith(
              color: widget.items.length >= widget.maxItems!
                  ? AppColors.warning
                  : AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildItemChip(String item) {
    return Chip(
      label: Text(
        item,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textPrimary,
        ),
      ),
      deleteIcon: const Icon(
        Icons.close,
        size: 16,
        color: AppColors.textSecondary,
      ),
      onDeleted: () => _removeItem(item),
      backgroundColor: AppColors.surface,
      side: BorderSide(
        color: AppColors.primary.withValues(alpha: 0.3),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
    );
  }
}

/// {@template onboarding_predefined_list_input}
/// Widget for selecting from predefined options with custom input capability.
/// {@endtemplate}
class OnboardingPredefinedListInput extends StatelessWidget {
  /// {@macro onboarding_predefined_list_input}
  const OnboardingPredefinedListInput({
    required this.selectedItems,
    required this.predefinedOptions,
    required this.onChanged,
    required this.customInputHint,
    this.maxSelections,
    super.key,
  });

  /// Currently selected items
  final List<String> selectedItems;

  /// Predefined options to choose from
  final List<String> predefinedOptions;

  /// Callback when selection changes
  final ValueChanged<List<String>> onChanged;

  /// Hint text for custom input
  final String customInputHint;

  /// Maximum number of selections allowed
  final int? maxSelections;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Predefined options
        if (predefinedOptions.isNotEmpty) ...[
          Text(
            'Common options:',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Wrap(
            spacing: AppDimensions.spacingS,
            runSpacing: AppDimensions.spacingS,
            children: predefinedOptions.map(_buildOptionChip).toList(),
          ),
          const SizedBox(height: AppDimensions.spacingL),
        ],

        // Custom input
        OnboardingListInput(
          items: selectedItems
              .where((item) => !predefinedOptions.contains(item))
              .toList(),
          onChanged: (customItems) {
            final predefinedSelected =
                selectedItems.where(predefinedOptions.contains).toList();
            onChanged([...predefinedSelected, ...customItems]);
          },
          hintText: customInputHint,
          maxItems: maxSelections != null
              ? maxSelections! -
                  selectedItems.where(predefinedOptions.contains).length
              : null,
        ),
      ],
    );
  }

  Widget _buildOptionChip(String option) {
    final isSelected = selectedItems.contains(option);
    final canSelect = maxSelections == null ||
        selectedItems.length < maxSelections! ||
        isSelected;

    return FilterChip(
      label: Text(
        option,
        style: AppTextStyles.bodySmall.copyWith(
          color: isSelected ? AppColors.textOnPrimary : AppColors.textPrimary,
        ),
      ),
      selected: isSelected,
      onSelected: canSelect
          ? (selected) {
              final updatedItems = [...selectedItems];
              if (selected) {
                updatedItems.add(option);
              } else {
                updatedItems.remove(option);
              }
              onChanged(updatedItems);
            }
          : null,
      backgroundColor: AppColors.surface,
      selectedColor: AppColors.primary,
      checkmarkColor: AppColors.textOnPrimary,
      side: BorderSide(
        color: isSelected
            ? AppColors.primary
            : AppColors.primary.withValues(alpha: 0.3),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
    );
  }
}
