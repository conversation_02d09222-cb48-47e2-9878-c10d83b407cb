import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/asvs_assessment_screen.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/completion_screen.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/health_information_screen.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/medical_history_screen.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/personal_info_screen.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/physical_measurements_screen.dart';
import 'package:bloomg_flutter/features/onboarding/view/screens/welcome_screen.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_onboarding_slider/flutter_onboarding_slider.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template onboarding_flow_page}
/// Main onboarding flow page that manages the multi-step onboarding process.
/// Uses flutter_onboarding_slider for navigation between steps.
/// {@endtemplate}
class OnboardingFlowPage extends StatefulWidget {
  /// {@macro onboarding_flow_page}
  const OnboardingFlowPage({super.key});

  @override
  State<OnboardingFlowPage> createState() => _OnboardingFlowPageState();
}

class _OnboardingFlowPageState extends State<OnboardingFlowPage> {
  static final LoggerService _logger = LoggerService();
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Onboarding flow started',
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OnboardingBloc(
        repository: getIt<AsvsProfileRepository>(),
        onboardingService: getIt<OnboardingService>(),
      )..add(const StartOnboarding()),
      child: ResponsiveBreakpoints.builder(
        child: Scaffold(
          backgroundColor: AppColors.background,
          body: SafeArea(
            child: BlocConsumer<OnboardingBloc, OnboardingState>(
              listener: _handleStateChanges,
              builder: _buildContent,
            ),
          ),
        ),
        breakpoints: const [
          Breakpoint(start: 0, end: 450, name: MOBILE),
          Breakpoint(start: 451, end: 800, name: TABLET),
          Breakpoint(start: 801, end: 1920, name: DESKTOP),
          Breakpoint(start: 1921, end: double.infinity, name: '4K'),
        ],
      ),
    );
  }

  void _handleStateChanges(BuildContext context, OnboardingState state) {
    if (state is OnboardingCompleted) {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Onboarding completed successfully',
        ),
      );

      // Navigate to home page
      context.go(AppRouter.homePath);
    } else if (state is OnboardingError) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Onboarding error occurred',
          'Error: ${state.error}',
        ),
      );

      // Show error dialog
      _showErrorDialog(context, state.error ?? 'An unexpected error occurred');
    } else if (state is OnboardingInProgress) {
      // Navigate to the current step
      _navigateToStep(state.stepIndex);
    }
  }

  Widget _buildContent(BuildContext context, OnboardingState state) {
    if (state is OnboardingLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (state is OnboardingInProgress) {
      return _buildOnboardingSlider(context, state);
    }

    // Initial state or error state
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
      ),
    );
  }

  Widget _buildOnboardingSlider(
    BuildContext context,
    OnboardingInProgress state,
  ) {
    return OnBoardingSlider(
      headerBackgroundColor: AppColors.background,
      finishButtonText: 'Complete',
      finishButtonStyle: const FinishButtonStyle(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
      ),
      skipTextButton: const Text(
        'Skip',
        style: TextStyle(
          fontSize: 16,
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w600,
        ),
      ),
      trailing: const Text(
        'Next',
        style: TextStyle(
          fontSize: 16,
          color: AppColors.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
      background: [
        Container(color: AppColors.background),
        Container(color: AppColors.background),
        Container(color: AppColors.background),
        Container(color: AppColors.background),
        Container(color: AppColors.background),
        Container(color: AppColors.background),
        Container(color: AppColors.background),
      ],
      totalPage: 7,
      speed: 1.8,
      pageBodies: const [
        WelcomeScreen(),
        PersonalInfoScreen(),
        PhysicalMeasurementsScreen(),
        HealthInformationScreen(),
        MedicalHistoryScreen(),
        AsvsAssessmentScreen(),
        CompletionScreen(),
      ],
      onFinish: () {
        context.read<OnboardingBloc>().add(const CompleteOnboarding());
      },
      controllerColor: AppColors.primary,
    );
  }

  void _navigateToStep(int stepIndex) {
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        stepIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showErrorDialog(BuildContext context, String error) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        title: const Text(
          'Error',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        content: Text(
          error,
          style: const TextStyle(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go(AppRouter.homePath);
            },
            child: const Text(
              'OK',
              style: TextStyle(color: AppColors.primary),
            ),
          ),
        ],
      ),
    );
  }
}
