import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/profile_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template profile_edit_page}
/// Profile edit page that allows users to modify
/// their ASVS profile information.
/// {@endtemplate}
class ProfileEditPage extends StatefulWidget {
  /// {@macro profile_edit_page}
  const ProfileEditPage({super.key});

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  static final LoggerService _logger = LoggerService();
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Profile edit page displayed',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileBloc(
        repository: getIt<AsvsProfileRepository>(),
        onboardingService: getIt<OnboardingService>(),
      )..add(const LoadProfile()),
      child: ResponsiveBreakpoints.builder(
        child: PopScope(
          canPop: !_hasUnsavedChanges,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop && _hasUnsavedChanges) {
              _showUnsavedChangesDialog();
            }
          },
          child: Scaffold(
            backgroundColor: AppColors.background,
            appBar: AppBar(
              backgroundColor: AppColors.background,
              title: Text(
                'Edit Profile',
                style: AppTextStyles.heading1.copyWith(fontSize: 20),
              ),
              leading: IconButton(
                icon:
                    const Icon(Icons.arrow_back, color: AppColors.textPrimary),
                onPressed: _handleBackPressed,
              ),
              actions: [
                BlocBuilder<ProfileBloc, ProfileState>(
                  builder: (context, state) {
                    return TextButton(
                      onPressed: state is ProfileLoaded
                          ? () => _saveProfile(context)
                          : null,
                      child: Text(
                        'Save',
                        style: AppTextStyles.button.copyWith(
                          color: state is ProfileLoaded
                              ? AppColors.primary
                              : AppColors.textSecondary,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
            body: SafeArea(
              child: BlocConsumer<ProfileBloc, ProfileState>(
                listener: _handleStateChanges,
                builder: _buildContent,
              ),
            ),
          ),
        ),
        breakpoints: const [
          Breakpoint(start: 0, end: 450, name: MOBILE),
          Breakpoint(start: 451, end: 800, name: TABLET),
          Breakpoint(start: 801, end: 1920, name: DESKTOP),
          Breakpoint(start: 1921, end: double.infinity, name: '4K'),
        ],
      ),
    );
  }

  void _handleStateChanges(BuildContext context, ProfileState state) {
    if (state is ProfileError) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile edit error',
          'Error: ${state.error}',
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.error ?? 'An error occurred'),
          backgroundColor: AppColors.error,
        ),
      );
    } else if (state is ProfileLoaded && state.message != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message!),
          backgroundColor: AppColors.success,
        ),
      );

      if (state.message!.contains('saved')) {
        setState(() {
          _hasUnsavedChanges = false;
        });

        // Navigate back to profile view
        context.go(AppRouter.profilePath);
      }
    }
  }

  Widget _buildContent(BuildContext context, ProfileState state) {
    if (state is ProfileLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (state is ProfileEmpty) {
      return _buildEmptyState();
    }

    if (state is ProfileLoaded) {
      return _buildEditForm(state);
    }

    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_outline,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppDimensions.spacingL),
            Text(
              'No Profile Found',
              style: AppTextStyles.heading1.copyWith(fontSize: 24),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            const Text(
              'Complete the onboarding process first to create your profile.',
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingXXL),
            ElevatedButton(
              onPressed: () => context.go(AppRouter.onboardingPath),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
              ),
              child: const Text('Start Onboarding'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditForm(ProfileLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Edit instructions
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    'To edit your profile information, please complete the onboarding flow again. This ensures all data is properly validated and calculated.',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppDimensions.spacingXXL),

          // Quick actions
          _buildQuickActions(),

          const SizedBox(height: AppDimensions.spacingXXL),

          // Profile summary (read-only)
          _buildProfileSummary(state.currentProfile),

          const SizedBox(height: AppDimensions.spacingXXL),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),

        // Restart onboarding
        _buildActionCard(
          icon: Icons.refresh,
          title: 'Update Profile Information',
          description:
              'Go through the onboarding process to update your profile',
          onTap: () => context.go(AppRouter.onboardingPath),
        ),

        // Delete profile
        _buildActionCard(
          icon: Icons.delete_outline,
          title: 'Delete Profile',
          description: 'Permanently delete your profile data',
          onTap: _showDeleteConfirmation,
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Material(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isDestructive ? AppColors.error : AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isDestructive
                              ? AppColors.error
                              : AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.spacingXS),
                      Text(
                        description,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSummary(AsvsUserProfile profile) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Profile Summary',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            'Completion: ${profile.completionPercentage.toStringAsFixed(0)}%',
            style: AppTextStyles.bodySmall,
          ),
          if (profile.firstName != null && profile.lastName != null)
            Text(
              'Name: ${profile.firstName} ${profile.lastName}',
              style: AppTextStyles.bodySmall,
            ),
          if (profile.bmi != null)
            Text(
              'BMI: ${profile.bmi!.toStringAsFixed(1)}',
              style: AppTextStyles.bodySmall,
            ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            'Last updated: ${profile.updatedAt != null ? _formatDate(profile.updatedAt!) : 'Unknown'}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  void _handleBackPressed() {
    if (_hasUnsavedChanges) {
      _showUnsavedChangesDialog();
    } else {
      context.go(AppRouter.profilePath);
    }
  }

  void _saveProfile(BuildContext context) {
    context.read<ProfileBloc>().add(const SaveProfile());
  }

  void _showUnsavedChangesDialog() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        title: const Text(
          'Unsaved Changes',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        content: const Text(
          'You have unsaved changes. Do you want to discard them?',
          style: TextStyle(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go(AppRouter.profilePath);
            },
            child: const Text(
              'Discard',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        title: const Text(
          'Delete Profile',
          style: TextStyle(color: AppColors.error),
        ),
        content: const Text(
          'Are you sure you want to delete your profile? This action cannot be undone.',
          style: TextStyle(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<ProfileBloc>().add(const DeleteProfile());
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
