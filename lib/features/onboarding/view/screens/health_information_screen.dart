import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template health_information_screen}
/// Health information screen for the ASVS onboarding flow.
/// Collects smoking status, exercise frequency, and sleep hours.
/// {@endtemplate}
class HealthInformationScreen extends StatefulWidget {
  /// {@macro health_information_screen}
  const HealthInformationScreen({super.key});

  @override
  State<HealthInformationScreen> createState() =>
      _HealthInformationScreenState();
}

class _HealthInformationScreenState extends State<HealthInformationScreen> {
  static final LoggerService _logger = LoggerService();

  final _exerciseController = TextEditingController();
  final _sleepController = TextEditingController();
  SmokingStatus? _selectedSmokingStatus;

  // Form validation state
  final Map<String, bool> _touchedFields = {};
  final Map<String, String?> _fieldErrors = {};

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Health information screen displayed',
      ),
    );

    _loadExistingData();
  }

  @override
  void dispose() {
    _exerciseController.dispose();
    _sleepController.dispose();
    super.dispose();
  }

  void _loadExistingData() {
    final state = context.read<OnboardingBloc>().state;
    if (state is OnboardingInProgress) {
      final profile = state.profile;
      _selectedSmokingStatus = profile.smokingStatus;
      _exerciseController.text = profile.exerciseFrequency?.toString() ?? '';
      _sleepController.text = profile.sleepHours?.toString() ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 3;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      const OnboardingStepHeader(
                        title: 'Health Information',
                        subtitle: 'Tell us about your lifestyle habits',
                      ),

                      // Smoking Status
                      OnboardingFormField(
                        label: 'Smoking Status',
                        isRequired: true,
                        error: _getFieldError('smokingStatus'),
                        child: OnboardingDropdownField<SmokingStatus>(
                          value: _selectedSmokingStatus,
                          hintText: 'Select your smoking status',
                          items: SmokingStatus.values,
                          itemBuilder: (status) => Text(
                            status.displayName,
                            style: AppTextStyles.input,
                          ),
                          onChanged: _onSmokingStatusChanged,
                        ),
                      ),

                      // Exercise Frequency
                      OnboardingFormField(
                        label: 'Exercise Frequency (days per week)',
                        isRequired: true,
                        error: _getFieldError('exerciseFrequency'),
                        child: OnboardingTextField(
                          controller: _exerciseController,
                          hintText: 'Enter days per week (0-7)',
                          keyboardType: TextInputType.number,
                          textInputAction: TextInputAction.next,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(1),
                          ],
                          onChanged: (value) =>
                              _onFieldChanged('exerciseFrequency', value),
                        ),
                      ),

                      // Sleep Hours
                      OnboardingFormField(
                        label: 'Average Sleep Hours (per night)',
                        isRequired: true,
                        error: _getFieldError('sleepHours'),
                        child: OnboardingTextField(
                          controller: _sleepController,
                          hintText: 'Enter hours per night (4-12)',
                          keyboardType: TextInputType.number,
                          textInputAction: TextInputAction.done,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(2),
                          ],
                          onChanged: (value) =>
                              _onFieldChanged('sleepHours', value),
                        ),
                      ),

                      const SizedBox(height: AppDimensions.spacingL),

                      // Health tips
                      _buildHealthTips(),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Validation summary
                      if (_hasValidationErrors()) _buildValidationSummary(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _onSmokingStatusChanged(SmokingStatus? status) {
    setState(() {
      _selectedSmokingStatus = status;
      _touchedFields['smokingStatus'] = true;
    });

    _validateField('smokingStatus', status?.toString() ?? '');
    _updateProfile();
  }

  void _onFieldChanged(String fieldName, String value) {
    setState(() {
      _touchedFields[fieldName] = true;
    });

    _validateField(fieldName, value);
    _updateProfile();
  }

  void _validateField(String fieldName, String value) {
    String? error;

    switch (fieldName) {
      case 'smokingStatus':
        if (_selectedSmokingStatus == null) {
          error = 'Smoking status is required';
        }
      case 'exerciseFrequency':
        if (value.trim().isEmpty) {
          error = 'Exercise frequency is required';
        } else {
          final frequency = int.tryParse(value);
          if (frequency == null || frequency < 0 || frequency > 7) {
            error = 'Exercise frequency must be between 0 and 7 days';
          }
        }
      case 'sleepHours':
        if (value.trim().isEmpty) {
          error = 'Sleep hours is required';
        } else {
          final hours = int.tryParse(value);
          if (hours == null || hours < 4 || hours > 12) {
            error = 'Sleep hours must be between 4 and 12 hours';
          }
        }
    }

    setState(() {
      _fieldErrors[fieldName] = error;
    });
  }

  String? _getFieldError(String fieldName) {
    if (_touchedFields[fieldName] == true) {
      return _fieldErrors[fieldName];
    }
    return null;
  }

  bool _hasValidationErrors() {
    return _fieldErrors.values.any((error) => error != null);
  }

  void _updateProfile() {
    final exerciseFreq = int.tryParse(_exerciseController.text);
    final sleepHours = int.tryParse(_sleepController.text);

    final updates = <String, dynamic>{
      'smokingStatus': _selectedSmokingStatus,
      'exerciseFrequency': exerciseFreq,
      'sleepHours': sleepHours,
    };

    context.read<OnboardingBloc>().add(UpdateProfile(updates));

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Health information updated',
        'Smoking: ${_selectedSmokingStatus?.name}, Exercise: $exerciseFreq days, Sleep: $sleepHours hours',
      ),
    );
  }

  Widget _buildHealthTips() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Health Tips',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            '• Aim for 150+ minutes of moderate exercise per week\n'
            '• Adults need 7-9 hours of sleep per night\n'
            '• Quitting smoking has immediate health benefits',
            style: AppTextStyles.bodySmall.copyWith(
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationSummary() {
    final errors = _fieldErrors.values.where((error) => error != null).toList();

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: AppColors.error,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Please fix the following issues:',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          ...errors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: AppDimensions.spacingL),
              child: Text(
                '• $error',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
