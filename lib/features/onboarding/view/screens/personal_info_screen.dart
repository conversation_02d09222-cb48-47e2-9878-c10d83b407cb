import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template personal_info_screen}
/// Personal information screen for the ASVS onboarding flow.
/// Collects name, date of birth, and gender information.
/// {@endtemplate}
class PersonalInfoScreen extends StatefulWidget {
  /// {@macro personal_info_screen}
  const PersonalInfoScreen({super.key});

  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  static final LoggerService _logger = LoggerService();

  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  DateTime? _selectedDate;
  Gender? _selectedGender;

  // Form validation state
  final Map<String, bool> _touchedFields = {};
  final Map<String, String?> _fieldErrors = {};

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Personal info screen displayed',
      ),
    );

    // Load existing data if available
    _loadExistingData();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  void _loadExistingData() {
    final state = context.read<OnboardingBloc>().state;
    if (state is OnboardingInProgress) {
      final profile = state.profile;
      _firstNameController.text = profile.firstName ?? '';
      _lastNameController.text = profile.lastName ?? '';
      _selectedDate = profile.dateOfBirth;
      _selectedGender = profile.gender;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 1;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      const OnboardingStepHeader(
                        title: 'Personal Information',
                        subtitle: 'Tell us a bit about yourself',
                      ),

                      // First Name
                      OnboardingFormField(
                        label: 'First Name',
                        isRequired: true,
                        error: _getFieldError('firstName'),
                        child: OnboardingTextField(
                          controller: _firstNameController,
                          hintText: 'Enter your first name',
                          textInputAction: TextInputAction.next,
                          onChanged: (value) =>
                              _onFieldChanged('firstName', value),
                        ),
                      ),

                      // Last Name
                      OnboardingFormField(
                        label: 'Last Name',
                        isRequired: true,
                        error: _getFieldError('lastName'),
                        child: OnboardingTextField(
                          controller: _lastNameController,
                          hintText: 'Enter your last name',
                          textInputAction: TextInputAction.next,
                          onChanged: (value) =>
                              _onFieldChanged('lastName', value),
                        ),
                      ),

                      // Date of Birth
                      OnboardingFormField(
                        label: 'Date of Birth',
                        isRequired: true,
                        error: _getFieldError('dateOfBirth'),
                        child: OnboardingDatePickerField(
                          value: _selectedDate,
                          hintText: 'Select your date of birth',
                          lastDate: DateTime.now().subtract(
                            const Duration(
                              days: 365 * 13,
                            ),
                          ), // Minimum 13 years old
                          firstDate: DateTime.now().subtract(
                            const Duration(
                              days: 365 * 120,
                            ),
                          ), // Maximum 120 years old
                          onChanged: _onDateChanged,
                        ),
                      ),

                      // Gender
                      OnboardingFormField(
                        label: 'Gender',
                        isRequired: true,
                        error: _getFieldError('gender'),
                        child: OnboardingDropdownField<Gender>(
                          value: _selectedGender,
                          hintText: 'Select your gender',
                          items: Gender.values,
                          itemBuilder: (gender) => Text(
                            gender.displayName,
                            style: AppTextStyles.input,
                          ),
                          onChanged: _onGenderChanged,
                        ),
                      ),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Validation summary
                      if (_hasValidationErrors()) _buildValidationSummary(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _onFieldChanged(String fieldName, String value) {
    setState(() {
      _touchedFields[fieldName] = true;
    });

    _validateField(fieldName, value);
    _updateProfile();
  }

  void _onDateChanged(DateTime? date) {
    setState(() {
      _selectedDate = date;
      _touchedFields['dateOfBirth'] = true;
    });

    _validateField('dateOfBirth', date?.toString() ?? '');
    _updateProfile();
  }

  void _onGenderChanged(Gender? gender) {
    setState(() {
      _selectedGender = gender;
      _touchedFields['gender'] = true;
    });

    _validateField('gender', gender?.toString() ?? '');
    _updateProfile();
  }

  void _validateField(String fieldName, String value) {
    String? error;

    switch (fieldName) {
      case 'firstName':
        if (value.trim().isEmpty) {
          error = 'First name is required';
        } else if (value.trim().length < 2) {
          error = 'First name must be at least 2 characters';
        }
      case 'lastName':
        if (value.trim().isEmpty) {
          error = 'Last name is required';
        } else if (value.trim().length < 2) {
          error = 'Last name must be at least 2 characters';
        }
      case 'dateOfBirth':
        if (_selectedDate == null) {
          error = 'Date of birth is required';
        } else {
          final age = DateTime.now().difference(_selectedDate!).inDays ~/ 365;
          if (age < 13) {
            error = 'You must be at least 13 years old';
          } else if (age > 120) {
            error = 'Please enter a valid date of birth';
          }
        }
      case 'gender':
        if (_selectedGender == null) {
          error = 'Gender selection is required';
        }
    }

    setState(() {
      _fieldErrors[fieldName] = error;
    });
  }

  String? _getFieldError(String fieldName) {
    if (_touchedFields[fieldName] == true) {
      return _fieldErrors[fieldName];
    }
    return null;
  }

  bool _hasValidationErrors() {
    return _fieldErrors.values.any((error) => error != null);
  }

  void _updateProfile() {
    final updates = <String, dynamic>{
      'firstName': _firstNameController.text.trim(),
      'lastName': _lastNameController.text.trim(),
      'dateOfBirth': _selectedDate,
      'gender': _selectedGender,
    };

    context.read<OnboardingBloc>().add(UpdateProfile(updates));

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Personal info updated',
        'Fields: ${updates.keys.join(', ')}',
      ),
    );
  }

  Widget _buildValidationSummary() {
    final errors = _fieldErrors.values.where((error) => error != null).toList();

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: AppColors.error,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Please fix the following issues:',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          ...errors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: AppDimensions.spacingL),
              child: Text(
                '• $error',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
