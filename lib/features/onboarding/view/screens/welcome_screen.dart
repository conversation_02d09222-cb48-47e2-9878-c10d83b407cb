import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/onboarding_progress_indicator.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template welcome_screen}
/// Welcome screen for the ASVS onboarding flow.
/// Introduces the user to the onboarding process and privacy notice.
/// {@endtemplate}
class WelcomeScreen extends StatelessWidget {
  /// {@macro welcome_screen}
  const WelcomeScreen({super.key});

  static final LoggerService _logger = LoggerService();

  @override
  Widget build(BuildContext context) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Welcome screen displayed',
      ),
    );

    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 0;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Welcome content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      const OnboardingStepHeader(
                        title: 'Welcome to ASVS',
                        subtitle: 'Automated Subjective Vitality Scale',
                      ),

                      // Description
                      Text(
                        "We'll collect some information about your health and "
                        'vitality to provide you with personalized insights.',
                        style: AppTextStyles.bodyLarge.copyWith(
                          height: 1.5,
                        ),
                      ),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Features list
                      _buildFeaturesList(),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Privacy notice
                      _buildPrivacyNotice(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      {
        'icon': Icons.health_and_safety,
        'title': 'Health Assessment',
        'description': 'Comprehensive health and vitality evaluation',
      },
      {
        'icon': Icons.analytics,
        'title': 'Personalized Insights',
        'description': 'Get tailored recommendations based on your data',
      },
      {
        'icon': Icons.security,
        'title': 'Secure & Private',
        'description': 'Your data is encrypted and stored securely',
      },
    ];

    return Column(
      children: features
          .map(
            (feature) => _buildFeatureItem(
              icon: feature['icon']! as IconData,
              title: feature['title']! as String,
              description: feature['description']! as String,
            ),
          )
          .toList(),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingL),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.spacingM),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppDimensions.spacingXS),
                Text(
                  description,
                  style: AppTextStyles.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyNotice() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.privacy_tip,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Privacy Notice',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'Your health information is confidential and will be used only '
            'to provide personalized insights. We follow strict privacy '
            'guidelines and never share your data without consent.',
            style: AppTextStyles.bodySmall.copyWith(
              height: 1.4,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          GestureDetector(
            onTap: () {
              _logger.info(
                LoggingConstants.formatMessage(
                  LoggingConstants.onboardingModule,
                  'Privacy policy link tapped',
                ),
              );
              // TODO(privacy): Navigate to privacy policy
            },
            child: const Text(
              'Read our Privacy Policy',
              style: AppTextStyles.link,
            ),
          ),
        ],
      ),
    );
  }
}
