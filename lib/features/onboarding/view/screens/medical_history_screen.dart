import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template medical_history_screen}
/// Medical history screen for the ASVS onboarding flow.
/// Collects medications, allergies, and medical conditions.
/// {@endtemplate}
class MedicalHistoryScreen extends StatefulWidget {
  /// {@macro medical_history_screen}
  const MedicalHistoryScreen({super.key});

  @override
  State<MedicalHistoryScreen> createState() => _MedicalHistoryScreenState();
}

class _MedicalHistoryScreenState extends State<MedicalHistoryScreen> {
  static final LoggerService _logger = LoggerService();

  List<String> _medications = [];
  List<String> _allergies = [];
  List<String> _medicalConditions = [];

  // Predefined options
  static const List<String> _commonMedications = [
    'Aspirin',
    'Ibuprofen',
    'Acetaminophen',
    'Metformin',
    'Lisinopril',
    'Atorvastatin',
    'Levothyroxine',
    'Omeprazole',
  ];

  static const List<String> _commonAllergies = [
    'Peanuts',
    'Tree nuts',
    'Shellfish',
    'Eggs',
    'Milk',
    'Soy',
    'Wheat',
    'Penicillin',
    'Latex',
    'Pollen',
  ];

  static const List<String> _commonConditions = [
    'Hypertension',
    'Diabetes',
    'Asthma',
    'Arthritis',
    'Depression',
    'Anxiety',
    'High cholesterol',
    'Heart disease',
    'Thyroid disorder',
  ];

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Medical history screen displayed',
      ),
    );

    _loadExistingData();
  }

  void _loadExistingData() {
    final state = context.read<OnboardingBloc>().state;
    if (state is OnboardingInProgress) {
      final profile = state.profile;
      _medications = List.from(profile.medications);
      _allergies = List.from(profile.allergies);
      _medicalConditions = List.from(profile.medicalConditions);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 4;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      const OnboardingStepHeader(
                        title: 'Medical History',
                        subtitle: 'Help us understand your medical background',
                      ),

                      // Medications
                      OnboardingFormField(
                        label: 'Current Medications',
                        child: OnboardingPredefinedListInput(
                          selectedItems: _medications,
                          predefinedOptions: _commonMedications,
                          customInputHint: 'Add custom medication',
                          onChanged: (medications) {
                            setState(() {
                              _medications = medications;
                            });
                            _updateProfile();
                          },
                        ),
                      ),

                      // Allergies
                      OnboardingFormField(
                        label: 'Known Allergies',
                        child: OnboardingPredefinedListInput(
                          selectedItems: _allergies,
                          predefinedOptions: _commonAllergies,
                          customInputHint: 'Add custom allergy',
                          onChanged: (allergies) {
                            setState(() {
                              _allergies = allergies;
                            });
                            _updateProfile();
                          },
                        ),
                      ),

                      // Medical Conditions
                      OnboardingFormField(
                        label: 'Medical Conditions',
                        child: OnboardingPredefinedListInput(
                          selectedItems: _medicalConditions,
                          predefinedOptions: _commonConditions,
                          customInputHint: 'Add custom condition',
                          onChanged: (conditions) {
                            setState(() {
                              _medicalConditions = conditions;
                            });
                            _updateProfile();
                          },
                        ),
                      ),

                      const SizedBox(height: AppDimensions.spacingL),

                      // Medical disclaimer
                      _buildMedicalDisclaimer(),

                      const SizedBox(height: AppDimensions.spacingXXL),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _updateProfile() {
    final updates = <String, dynamic>{
      'medications': _medications,
      'allergies': _allergies,
      'medicalConditions': _medicalConditions,
    };

    context.read<OnboardingBloc>().add(UpdateProfile(updates));

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Medical history updated',
        'Medications: ${_medications.length}, Allergies: ${_allergies.length}, Conditions: ${_medicalConditions.length}',
      ),
    );
  }

  Widget _buildMedicalDisclaimer() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.medical_information,
                color: AppColors.warning,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  'Medical Disclaimer',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.warning,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'This information is for assessment purposes only and does not '
            'replace professional medical advice. Always consult with your '
            'healthcare provider for medical decisions.',
            style: AppTextStyles.bodySmall.copyWith(
              height: 1.4,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'All medical information is kept confidential and secure.',
            style: AppTextStyles.bodySmall.copyWith(
              height: 1.4,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
}
