import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template asvs_assessment_screen}
/// ASVS assessment screen for the onboarding flow.
/// Collects stress level, energy level, and mood rating on 1-10 scales.
/// {@endtemplate}
class AsvsAssessmentScreen extends StatefulWidget {
  /// {@macro asvs_assessment_screen}
  const AsvsAssessmentScreen({super.key});

  @override
  State<AsvsAssessmentScreen> createState() => _AsvsAssessmentScreenState();
}

class _AsvsAssessmentScreenState extends State<AsvsAssessmentScreen> {
  static final LoggerService _logger = LoggerService();

  double _stressLevel = 5;
  double _energyLevel = 5;
  double _moodRating = 5;

  // Form validation state
  final Map<String, bool> _touchedFields = {};

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'ASVS assessment screen displayed',
      ),
    );

    _loadExistingData();
  }

  void _loadExistingData() {
    final state = context.read<OnboardingBloc>().state;
    if (state is OnboardingInProgress) {
      final profile = state.profile;
      _stressLevel = profile.stressLevel?.toDouble() ?? 5.0;
      _energyLevel = profile.energyLevel?.toDouble() ?? 5.0;
      _moodRating = profile.moodRating?.toDouble() ?? 5.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 5;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      const OnboardingStepHeader(
                        title: 'ASVS Assessment',
                        subtitle: 'Rate your current vitality levels',
                      ),

                      // Assessment description
                      _buildAssessmentDescription(),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Stress Level
                      OnboardingSliderField(
                        value: _stressLevel,
                        label: 'Current Stress Level',
                        onChanged: (value) {
                          setState(() {
                            _stressLevel = value;
                            _touchedFields['stressLevel'] = true;
                          });
                          _updateProfile();
                        },
                      ),

                      const SizedBox(height: AppDimensions.spacingL),

                      // Stress level description
                      _buildScaleDescription(
                        'Stress Level Scale',
                        '1 = Very relaxed, no stress',
                        '10 = Extremely stressed, overwhelmed',
                      ),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Energy Level
                      OnboardingSliderField(
                        value: _energyLevel,
                        label: 'Current Energy Level',
                        onChanged: (value) {
                          setState(() {
                            _energyLevel = value;
                            _touchedFields['energyLevel'] = true;
                          });
                          _updateProfile();
                        },
                      ),

                      const SizedBox(height: AppDimensions.spacingL),

                      // Energy level description
                      _buildScaleDescription(
                        'Energy Level Scale',
                        '1 = Completely exhausted, no energy',
                        '10 = Extremely energetic, full of vitality',
                      ),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Mood Rating
                      OnboardingSliderField(
                        value: _moodRating,
                        label: 'Current Mood Rating',
                        onChanged: (value) {
                          setState(() {
                            _moodRating = value;
                            _touchedFields['moodRating'] = true;
                          });
                          _updateProfile();
                        },
                      ),

                      const SizedBox(height: AppDimensions.spacingL),

                      // Mood rating description
                      _buildScaleDescription(
                        'Mood Rating Scale',
                        '1 = Very sad, depressed mood',
                        '10 = Very happy, excellent mood',
                      ),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Assessment summary
                      _buildAssessmentSummary(),

                      const SizedBox(height: AppDimensions.spacingXXL),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _updateProfile() {
    final updates = <String, dynamic>{
      'stressLevel': _stressLevel.round(),
      'energyLevel': _energyLevel.round(),
      'moodRating': _moodRating.round(),
    };

    context.read<OnboardingBloc>().add(UpdateProfile(updates));

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'ASVS assessment updated',
        'Stress: ${_stressLevel.round()}, '
            'Energy: ${_energyLevel.round()}, '
            'Mood: ${_moodRating.round()}',
      ),
    );
  }

  Widget _buildAssessmentDescription() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.psychology,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'About ASVS',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'The Automated Subjective Vitality Scale measures your current '
            'psychological and physical well-being. Please rate how you feel '
            'right now, not how you usually feel.',
            style: AppTextStyles.bodySmall.copyWith(
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScaleDescription(String title, String lowEnd, String highEnd) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingXS),
          Text(
            lowEnd,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            highEnd,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssessmentSummary() {
    final averageScore = (_stressLevel + _energyLevel + _moodRating) / 3;
    String overallAssessment;
    Color assessmentColor;

    if (averageScore >= 8) {
      overallAssessment = 'Excellent vitality';
      assessmentColor = AppColors.success;
    } else if (averageScore >= 6) {
      overallAssessment = 'Good vitality';
      assessmentColor = AppColors.primary;
    } else if (averageScore >= 4) {
      overallAssessment = 'Moderate vitality';
      assessmentColor = AppColors.warning;
    } else {
      overallAssessment = 'Low vitality';
      assessmentColor = AppColors.error;
    }

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: assessmentColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Assessment Summary',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Stress Level:',
                style: AppTextStyles.bodySmall,
              ),
              Text(
                '${_stressLevel.round()}/10',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Energy Level:',
                style: AppTextStyles.bodySmall,
              ),
              Text(
                '${_energyLevel.round()}/10',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Mood Rating:',
                style: AppTextStyles.bodySmall,
              ),
              Text(
                '${_moodRating.round()}/10',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.spacingM,
              vertical: AppDimensions.spacingS,
            ),
            decoration: BoxDecoration(
              color: assessmentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: assessmentColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.assessment,
                  color: assessmentColor,
                  size: 16,
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Text(
                  overallAssessment,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: assessmentColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
