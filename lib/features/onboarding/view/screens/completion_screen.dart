import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template completion_screen}
/// Completion screen for the ASVS onboarding flow.
/// Shows summary of collected data and BMI calculation.
/// {@endtemplate}
class CompletionScreen extends StatelessWidget {
  /// {@macro completion_screen}
  const CompletionScreen({super.key});

  static final LoggerService _logger = LoggerService();

  @override
  Widget build(BuildContext context) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Completion screen displayed',
      ),
    );

    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 6;
        final profile = state.profile;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      const OnboardingStepHeader(
                        title: 'Profile Complete!',
                        subtitle: 'Review your health profile summary',
                      ),

                      // Completion message
                      _buildCompletionMessage(),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Profile summary
                      _buildProfileSummary(profile),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Next steps
                      _buildNextSteps(),

                      const SizedBox(height: AppDimensions.spacingXXL),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCompletionMessage() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 48,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            'Congratulations!',
            style: AppTextStyles.heading1.copyWith(
              fontSize: 24,
              color: AppColors.success,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            "You've successfully completed your ASVS health profile. "
            'Your information will help us provide personalized insights '
            'and recommendations.',
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSummary(AsvsUserProfile profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Summary',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),

        // Personal Information
        _buildSummarySection(
          'Personal Information',
          [
            if (profile.firstName != null && profile.lastName != null)
              'Name: ${profile.firstName} ${profile.lastName}',
            if (profile.dateOfBirth != null)
              'Age: ${_calculateAge(profile.dateOfBirth!)} years',
            if (profile.gender != null)
              'Gender: ${profile.gender!.displayName}',
          ],
        ),

        // Physical Measurements
        _buildSummarySection(
          'Physical Measurements',
          [
            if (profile.heightCm != null)
              'Height: ${profile.heightCm!.toStringAsFixed(1)} cm',
            if (profile.weightKg != null)
              'Weight: ${profile.weightKg!.toStringAsFixed(1)} kg',
            if (profile.bmi != null)
              'BMI: ${profile.bmi!.toStringAsFixed(1)} '
                  '(${_getBMICategory(profile.bmi!)})',
          ],
        ),

        // Health Information
        _buildSummarySection(
          'Health Information',
          [
            if (profile.smokingStatus != null)
              'Smoking: ${profile.smokingStatus!.displayName}',
            if (profile.exerciseFrequency != null)
              'Exercise: ${profile.exerciseFrequency} days/week',
            if (profile.sleepHours != null)
              'Sleep: ${profile.sleepHours} hours/night',
          ],
        ),

        // ASVS Assessment
        _buildSummarySection(
          'ASVS Assessment',
          [
            if (profile.stressLevel != null)
              'Stress Level: ${profile.stressLevel}/10',
            if (profile.energyLevel != null)
              'Energy Level: ${profile.energyLevel}/10',
            if (profile.moodRating != null)
              'Mood Rating: ${profile.moodRating}/10',
          ],
        ),

        // Medical History
        if (profile.medications.isNotEmpty ||
            profile.allergies.isNotEmpty ||
            profile.medicalConditions.isNotEmpty)
          _buildSummarySection(
            'Medical History',
            [
              if (profile.medications.isNotEmpty)
                'Medications: ${profile.medications.length} items',
              if (profile.allergies.isNotEmpty)
                'Allergies: ${profile.allergies.length} items',
              if (profile.medicalConditions.isNotEmpty)
                'Conditions: ${profile.medicalConditions.length} items',
            ],
          ),
      ],
    );
  }

  Widget _buildSummarySection(String title, List<String> items) {
    if (items.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          ...items.map(
            (item) => Padding(
              padding: const EdgeInsets.only(bottom: AppDimensions.spacingXS),
              child: Text(
                '• $item',
                style: AppTextStyles.bodySmall,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextSteps() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.rocket_launch,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                "What's Next?",
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildNextStepItem(
            Icons.analytics,
            'Personalized Insights',
            'Get tailored health recommendations based on your profile',
          ),
          _buildNextStepItem(
            Icons.track_changes,
            'Progress Tracking',
            'Monitor your vitality levels over time',
          ),
          _buildNextStepItem(
            Icons.face,
            'Face Verification',
            'Complete face verification for enhanced features',
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    var age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  String _getBMICategory(double bmi) {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal weight';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }
}
