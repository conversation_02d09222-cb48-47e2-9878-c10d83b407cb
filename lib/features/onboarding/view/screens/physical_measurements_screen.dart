import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/widgets/widgets.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// {@template physical_measurements_screen}
/// Physical measurements screen for the ASVS onboarding flow.
/// Collects height, weight, and unit preferences.
/// {@endtemplate}
class PhysicalMeasurementsScreen extends StatefulWidget {
  /// {@macro physical_measurements_screen}
  const PhysicalMeasurementsScreen({super.key});

  @override
  State<PhysicalMeasurementsScreen> createState() =>
      _PhysicalMeasurementsScreenState();
}

class _PhysicalMeasurementsScreenState
    extends State<PhysicalMeasurementsScreen> {
  static final LoggerService _logger = LoggerService();

  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  UnitPreference _selectedUnits = UnitPreference.metric;

  // Form validation state
  final Map<String, bool> _touchedFields = {};
  final Map<String, String?> _fieldErrors = {};

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Physical measurements screen displayed',
      ),
    );

    _loadExistingData();
  }

  @override
  void dispose() {
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _loadExistingData() {
    final state = context.read<OnboardingBloc>().state;
    if (state is OnboardingInProgress) {
      final profile = state.profile;
      _selectedUnits = profile.unitPreference;

      if (profile.heightCm != null) {
        if (_selectedUnits == UnitPreference.imperial) {
          final heightInches = profile.heightCm! / 2.54;
          _heightController.text = heightInches.toStringAsFixed(1);
        } else {
          _heightController.text = profile.heightCm!.toStringAsFixed(1);
        }
      }

      if (profile.weightKg != null) {
        if (_selectedUnits == UnitPreference.imperial) {
          final weightLbs = profile.weightKg! * 2.20462;
          _weightController.text = weightLbs.toStringAsFixed(1);
        } else {
          _weightController.text = profile.weightKg!.toStringAsFixed(1);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OnboardingBloc, OnboardingState>(
      builder: (context, state) {
        final currentStep = state is OnboardingInProgress ? state.stepIndex : 2;

        return Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              OnboardingProgressIndicator(
                currentStep: currentStep,
                totalSteps: 7,
              ),

              const SizedBox(height: AppDimensions.spacingXXL),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      const OnboardingStepHeader(
                        title: 'Physical Measurements',
                        subtitle: 'Help us understand your physical profile',
                      ),

                      // Unit Preference
                      OnboardingFormField(
                        label: 'Measurement Units',
                        isRequired: true,
                        child: OnboardingDropdownField<UnitPreference>(
                          value: _selectedUnits,
                          hintText: 'Select measurement system',
                          items: UnitPreference.values,
                          itemBuilder: (units) => Text(
                            units.displayName,
                            style: AppTextStyles.input,
                          ),
                          onChanged: _onUnitsChanged,
                        ),
                      ),

                      // Height
                      OnboardingFormField(
                        label: _getHeightLabel(),
                        isRequired: true,
                        error: _getFieldError('height'),
                        child: OnboardingTextField(
                          controller: _heightController,
                          hintText: _getHeightHint(),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          textInputAction: TextInputAction.next,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d*\.?\d*'),
                            ),
                          ],
                          onChanged: (value) =>
                              _onFieldChanged('height', value),
                        ),
                      ),

                      // Weight
                      OnboardingFormField(
                        label: _getWeightLabel(),
                        isRequired: true,
                        error: _getFieldError('weight'),
                        child: OnboardingTextField(
                          controller: _weightController,
                          hintText: _getWeightHint(),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          textInputAction: TextInputAction.done,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d*\.?\d*'),
                            ),
                          ],
                          onChanged: (value) =>
                              _onFieldChanged('weight', value),
                        ),
                      ),

                      const SizedBox(height: AppDimensions.spacingL),

                      // BMI Display
                      if (_canCalculateBMI()) _buildBMIDisplay(),

                      const SizedBox(height: AppDimensions.spacingXXL),

                      // Validation summary
                      if (_hasValidationErrors()) _buildValidationSummary(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getHeightLabel() {
    return _selectedUnits == UnitPreference.imperial
        ? 'Height (inches)'
        : 'Height (cm)';
  }

  String _getHeightHint() {
    return _selectedUnits == UnitPreference.imperial
        ? 'Enter height in inches (e.g., 70.5)'
        : 'Enter height in centimeters (e.g., 175.0)';
  }

  String _getWeightLabel() {
    return _selectedUnits == UnitPreference.imperial
        ? 'Weight (lbs)'
        : 'Weight (kg)';
  }

  String _getWeightHint() {
    return _selectedUnits == UnitPreference.imperial
        ? 'Enter weight in pounds (e.g., 150.5)'
        : 'Enter weight in kilograms (e.g., 70.0)';
  }

  void _onUnitsChanged(UnitPreference? units) {
    if (units == null) return;

    setState(() {
      _selectedUnits = units;
    });

    // Convert existing values
    _convertExistingValues();
    _updateProfile();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Unit preference changed',
        'Units: ${units.name}',
      ),
    );
  }

  void _convertExistingValues() {
    // Convert height
    if (_heightController.text.isNotEmpty) {
      final currentHeight = double.tryParse(_heightController.text);
      if (currentHeight != null) {
        if (_selectedUnits == UnitPreference.imperial) {
          // Convert cm to inches
          final heightInches = currentHeight / 2.54;
          _heightController.text = heightInches.toStringAsFixed(1);
        } else {
          // Convert inches to cm
          final heightCm = currentHeight * 2.54;
          _heightController.text = heightCm.toStringAsFixed(1);
        }
      }
    }

    // Convert weight
    if (_weightController.text.isNotEmpty) {
      final currentWeight = double.tryParse(_weightController.text);
      if (currentWeight != null) {
        if (_selectedUnits == UnitPreference.imperial) {
          // Convert kg to lbs
          final weightLbs = currentWeight * 2.20462;
          _weightController.text = weightLbs.toStringAsFixed(1);
        } else {
          // Convert lbs to kg
          final weightKg = currentWeight / 2.20462;
          _weightController.text = weightKg.toStringAsFixed(1);
        }
      }
    }
  }

  void _onFieldChanged(String fieldName, String value) {
    setState(() {
      _touchedFields[fieldName] = true;
    });

    _validateField(fieldName, value);
    _updateProfile();
  }

  void _validateField(String fieldName, String value) {
    String? error;
    final numValue = double.tryParse(value);

    switch (fieldName) {
      case 'height':
        if (value.trim().isEmpty) {
          error = 'Height is required';
        } else if (numValue == null) {
          error = 'Please enter a valid number';
        } else {
          if (_selectedUnits == UnitPreference.imperial) {
            if (numValue < 36 || numValue > 96) {
              error = 'Height must be between 36 and 96 inches';
            }
          } else {
            if (numValue < 90 || numValue > 250) {
              error = 'Height must be between 90 and 250 cm';
            }
          }
        }
      case 'weight':
        if (value.trim().isEmpty) {
          error = 'Weight is required';
        } else if (numValue == null) {
          error = 'Please enter a valid number';
        } else {
          if (_selectedUnits == UnitPreference.imperial) {
            if (numValue < 50 || numValue > 1000) {
              error = 'Weight must be between 50 and 1000 lbs';
            }
          } else {
            if (numValue < 20 || numValue > 500) {
              error = 'Weight must be between 20 and 500 kg';
            }
          }
        }
    }

    setState(() {
      _fieldErrors[fieldName] = error;
    });
  }

  String? _getFieldError(String fieldName) {
    if (_touchedFields[fieldName] == true) {
      return _fieldErrors[fieldName];
    }
    return null;
  }

  bool _hasValidationErrors() {
    return _fieldErrors.values.any((error) => error != null);
  }

  bool _canCalculateBMI() {
    final height = double.tryParse(_heightController.text);
    final weight = double.tryParse(_weightController.text);
    return height != null && weight != null && height > 0 && weight > 0;
  }

  double _calculateBMI() {
    final height = double.parse(_heightController.text);
    final weight = double.parse(_weightController.text);

    // Convert to metric for BMI calculation
    final heightCm =
        _selectedUnits == UnitPreference.imperial ? height * 2.54 : height;
    final weightKg =
        _selectedUnits == UnitPreference.imperial ? weight / 2.20462 : weight;

    final heightM = heightCm / 100;
    return weightKg / (heightM * heightM);
  }

  void _updateProfile() {
    final height = double.tryParse(_heightController.text);
    final weight = double.tryParse(_weightController.text);

    // Convert to metric for storage
    double? heightCm;
    double? weightKg;

    if (height != null) {
      heightCm =
          _selectedUnits == UnitPreference.imperial ? height * 2.54 : height;
    }

    if (weight != null) {
      weightKg =
          _selectedUnits == UnitPreference.imperial ? weight / 2.20462 : weight;
    }

    final updates = <String, dynamic>{
      'unitPreference': _selectedUnits,
      'heightCm': heightCm,
      'weightKg': weightKg,
    };

    context.read<OnboardingBloc>().add(UpdateProfile(updates));

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Physical measurements updated',
        'Height: $heightCm cm, Weight: $weightKg kg',
      ),
    );
  }

  Widget _buildBMIDisplay() {
    final bmi = _calculateBMI();
    String category;
    Color categoryColor;

    if (bmi < 18.5) {
      category = 'Underweight';
      categoryColor = AppColors.warning;
    } else if (bmi < 25) {
      category = 'Normal weight';
      categoryColor = AppColors.success;
    } else if (bmi < 30) {
      category = 'Overweight';
      categoryColor = AppColors.warning;
    } else {
      category = 'Obese';
      categoryColor = AppColors.error;
    }

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Body Mass Index (BMI)',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'BMI: ${bmi.toStringAsFixed(1)}',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.spacingM,
                  vertical: AppDimensions.spacingXS,
                ),
                decoration: BoxDecoration(
                  color: categoryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(
                    color: categoryColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  category,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: categoryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildValidationSummary() {
    final errors = _fieldErrors.values.where((error) => error != null).toList();

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: AppColors.error,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Please fix the following issues:',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          ...errors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: AppDimensions.spacingL),
              child: Text(
                '• $error',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
