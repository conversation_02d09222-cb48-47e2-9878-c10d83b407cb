import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/profile_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template profile_view_page}
/// Profile view page that displays the user's ASVS profile information.
/// {@endtemplate}
class ProfileViewPage extends StatefulWidget {
  /// {@macro profile_view_page}
  const ProfileViewPage({super.key});

  @override
  State<ProfileViewPage> createState() => _ProfileViewPageState();
}

class _ProfileViewPageState extends State<ProfileViewPage> {
  static final LoggerService _logger = LoggerService();

  @override
  void initState() {
    super.initState();
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Profile view page displayed',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileBloc(
        repository: getIt<AsvsProfileRepository>(),
        onboardingService: getIt<OnboardingService>(),
      )..add(const LoadProfile()),
      child: ResponsiveBreakpoints.builder(
        child: Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            backgroundColor: AppColors.background,
            title: Text(
              'My Profile',
              style: AppTextStyles.heading1.copyWith(fontSize: 20),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
              onPressed: () => context.go(AppRouter.homePath),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.edit, color: AppColors.primary),
                onPressed: () => context.go(AppRouter.profileEditPath),
              ),
            ],
          ),
          body: SafeArea(
            child: BlocConsumer<ProfileBloc, ProfileState>(
              listener: _handleStateChanges,
              builder: _buildContent,
            ),
          ),
        ),
        breakpoints: const [
          Breakpoint(start: 0, end: 450, name: MOBILE),
          Breakpoint(start: 451, end: 800, name: TABLET),
          Breakpoint(start: 801, end: 1920, name: DESKTOP),
          Breakpoint(start: 1921, end: double.infinity, name: '4K'),
        ],
      ),
    );
  }

  void _handleStateChanges(BuildContext context, ProfileState state) {
    if (state is ProfileError) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile view error',
          'Error: ${state.error}',
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.error ?? 'An error occurred'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Widget _buildContent(BuildContext context, ProfileState state) {
    if (state is ProfileLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (state is ProfileEmpty) {
      return _buildEmptyState();
    }

    if (state is ProfileLoaded) {
      return _buildProfileContent(state);
    }

    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_outline,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppDimensions.spacingL),
            Text(
              'No Profile Found',
              style: AppTextStyles.heading1.copyWith(fontSize: 24),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            const Text(
              'Complete the onboarding process to create your health profile.',
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingXXL),
            ElevatedButton(
              onPressed: () => context.go(AppRouter.onboardingPath),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
              ),
              child: const Text('Start Onboarding'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(ProfileLoaded state) {
    final profile = state.currentProfile;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile completion indicator
          _buildCompletionIndicator(state.completionPercentage.toDouble()),

          const SizedBox(height: AppDimensions.spacingXXL),

          // Personal Information
          _buildSection(
            'Personal Information',
            Icons.person,
            [
              if (profile.firstName != null && profile.lastName != null)
                _buildInfoRow(
                  'Name',
                  '${profile.firstName} ${profile.lastName}',
                ),
              if (profile.dateOfBirth != null)
                _buildInfoRow(
                  'Age',
                  '${_calculateAge(profile.dateOfBirth!)} years',
                ),
              if (profile.gender != null)
                _buildInfoRow('Gender', profile.gender!.displayName),
            ],
          ),

          // Physical Measurements
          _buildSection(
            'Physical Measurements',
            Icons.straighten,
            [
              if (profile.heightCm != null)
                _buildInfoRow(
                  'Height',
                  '${profile.heightCm!.toStringAsFixed(1)} cm',
                ),
              if (profile.weightKg != null)
                _buildInfoRow(
                  'Weight',
                  '${profile.weightKg!.toStringAsFixed(1)} kg',
                ),
              if (profile.bmi != null)
                _buildInfoRow(
                  'BMI',
                  '${profile.bmi!.toStringAsFixed(1)} (${_getBMICategory(profile.bmi!)})',
                ),
            ],
          ),

          // Health Information
          _buildSection(
            'Health Information',
            Icons.health_and_safety,
            [
              if (profile.smokingStatus != null)
                _buildInfoRow(
                  'Smoking Status',
                  profile.smokingStatus!.displayName,
                ),
              if (profile.exerciseFrequency != null)
                _buildInfoRow(
                  'Exercise Frequency',
                  '${profile.exerciseFrequency} days/week',
                ),
              if (profile.sleepHours != null)
                _buildInfoRow(
                  'Sleep Hours',
                  '${profile.sleepHours} hours/night',
                ),
            ],
          ),

          // ASVS Assessment
          _buildSection(
            'ASVS Assessment',
            Icons.psychology,
            [
              if (profile.stressLevel != null)
                _buildInfoRow('Stress Level', '${profile.stressLevel}/10'),
              if (profile.energyLevel != null)
                _buildInfoRow('Energy Level', '${profile.energyLevel}/10'),
              if (profile.moodRating != null)
                _buildInfoRow('Mood Rating', '${profile.moodRating}/10'),
            ],
          ),

          // Medical History
          if (profile.medications.isNotEmpty ||
              profile.allergies.isNotEmpty ||
              profile.medicalConditions.isNotEmpty)
            _buildSection(
              'Medical History',
              Icons.medical_information,
              [
                if (profile.medications.isNotEmpty)
                  _buildListRow('Medications', profile.medications),
                if (profile.allergies.isNotEmpty)
                  _buildListRow('Allergies', profile.allergies),
                if (profile.medicalConditions.isNotEmpty)
                  _buildListRow(
                    'Medical Conditions',
                    profile.medicalConditions,
                  ),
              ],
            ),

          const SizedBox(height: AppDimensions.spacingXXL),
        ],
      ),
    );
  }

  Widget _buildCompletionIndicator(double completionPercentage) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Profile Completion',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${completionPercentage.toStringAsFixed(0)}%',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          LinearProgressIndicator(
            value: completionPercentage / 100,
            backgroundColor: AppColors.inputBackground,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    if (children.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingL),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 20),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListRow(String label, List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label:',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingXS),
          if (items.isEmpty)
            Text(
              'None',
              style: AppTextStyles.bodySmall.copyWith(
                fontStyle: FontStyle.italic,
              ),
            )
          else
            ...items.map(
              (item) => Padding(
                padding: const EdgeInsets.only(left: AppDimensions.spacingM),
                child: Text(
                  '• $item',
                  style: AppTextStyles.bodySmall,
                ),
              ),
            ),
        ],
      ),
    );
  }

  int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    var age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  String _getBMICategory(double bmi) {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal weight';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }
}
