import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'asvs_user_profile.g.dart';

/// {@template asvs_user_profile}
/// ASVS (Automated Subjective Vitality Scale) user profile model.
/// Contains comprehensive health and vitality data for users.
/// {@endtemplate}
@HiveType(typeId: 1)
class AsvsUserProfile extends Equatable {
  /// {@macro asvs_user_profile}
  const AsvsUserProfile({
    // Personal Information
    this.firstName,
    this.lastName,
    this.dateOfBirth,
    this.gender,
    this.unitPreference = UnitPreference.metric,
    // Physical Measurements
    this.heightCm,
    this.weightKg,
    this.bmi,
    // Health Information
    this.smokingStatus,
    this.exerciseFrequency,
    this.sleepHours,
    this.medications = const [],
    this.allergies = const [],
    this.medicalConditions = const [],
    // ASVS Specific
    this.stressLevel,
    this.energyLevel,
    this.moodRating,
    // Metadata
    this.createdAt,
    this.updatedAt,
    this.isOnboardingComplete = false,
  });

  /// Creates an AsvsUserProfile from a JSON map
  factory AsvsUserProfile.fromJson(Map<String, dynamic> json) {
    return AsvsUserProfile(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      gender: json['gender'] != null
          ? Gender.values.firstWhere(
              (e) => e.name == json['gender'],
              orElse: () => Gender.preferNotToSay,
            )
          : null,
      unitPreference: UnitPreference.values.firstWhere(
        (e) => e.name == (json['unitPreference'] ?? 'metric'),
        orElse: () => UnitPreference.metric,
      ),
      heightCm: (json['heightCm'] as num?)?.toDouble(),
      weightKg: (json['weightKg'] as num?)?.toDouble(),
      bmi: (json['bmi'] as num?)?.toDouble(),
      smokingStatus: json['smokingStatus'] != null
          ? SmokingStatus.values.firstWhere(
              (e) => e.name == json['smokingStatus'],
              orElse: () => SmokingStatus.never,
            )
          : null,
      exerciseFrequency: json['exerciseFrequency'] as int?,
      sleepHours: json['sleepHours'] as int?,
      medications: (json['medications'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      allergies: (json['allergies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      medicalConditions: (json['medicalConditions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      stressLevel: json['stressLevel'] as int?,
      energyLevel: json['energyLevel'] as int?,
      moodRating: json['moodRating'] as int?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      isOnboardingComplete: json['isOnboardingComplete'] as bool? ?? false,
    );
  }

  /// Creates an empty profile.
  const AsvsUserProfile.empty()
      : firstName = null,
        lastName = null,
        dateOfBirth = null,
        gender = null,
        unitPreference = UnitPreference.metric,
        heightCm = null,
        weightKg = null,
        bmi = null,
        smokingStatus = null,
        exerciseFrequency = null,
        sleepHours = null,
        medications = const [],
        allergies = const [],
        medicalConditions = const [],
        stressLevel = null,
        energyLevel = null,
        moodRating = null,
        createdAt = null,
        updatedAt = null,
        isOnboardingComplete = false;

  // Personal Information
  /// User's first name
  @HiveField(0)
  final String? firstName;

  /// User's last name
  @HiveField(1)
  final String? lastName;

  /// User's date of birth
  @HiveField(2)
  final DateTime? dateOfBirth;

  /// User's gender identity
  @HiveField(3)
  final Gender? gender;

  /// User's preferred unit system
  @HiveField(4)
  final UnitPreference unitPreference;

  // Physical Measurements
  /// User's height in centimeters
  @HiveField(5)
  final double? heightCm;

  /// User's weight in kilograms
  @HiveField(6)
  final double? weightKg;

  /// User's calculated BMI (Body Mass Index)
  @HiveField(7)
  final double? bmi;

  // Health Information
  /// User's smoking status
  @HiveField(8)
  final SmokingStatus? smokingStatus;

  /// Exercise frequency (days per week, 0-7)
  @HiveField(9)
  final int? exerciseFrequency;

  /// Average sleep hours per night
  @HiveField(10)
  final int? sleepHours;

  /// List of current medications
  @HiveField(11)
  final List<String> medications;

  /// List of known allergies
  @HiveField(12)
  final List<String> allergies;

  /// List of medical conditions
  @HiveField(13)
  final List<String> medicalConditions;

  // ASVS Specific
  /// Current stress level (1-10 scale)
  @HiveField(14)
  final int? stressLevel;

  /// Current energy level (1-10 scale)
  @HiveField(15)
  final int? energyLevel;

  /// Current mood rating (1-10 scale)
  @HiveField(16)
  final int? moodRating;

  // Metadata
  /// When the profile was created
  @HiveField(17)
  final DateTime? createdAt;

  /// When the profile was last updated
  @HiveField(18)
  final DateTime? updatedAt;

  /// Whether the onboarding process is complete
  @HiveField(19)
  final bool isOnboardingComplete;

  /// User's full name
  String get fullName {
    final first = firstName ?? '';
    final last = lastName ?? '';
    return '$first $last'.trim();
  }

  /// User's age in years (calculated from date of birth)
  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    final age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      return age - 1;
    }
    return age;
  }

  /// Calculated BMI from height and weight
  double? get calculatedBmi {
    if (heightCm == null || weightKg == null) return null;
    final heightM = heightCm! / 100;
    return weightKg! / (heightM * heightM);
  }

  /// BMI category based on WHO standards
  String? get bmiCategory {
    final bmiValue = bmi ?? calculatedBmi;
    if (bmiValue == null) return null;

    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal weight';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  /// Whether the profile has basic information
  bool get hasBasicInfo {
    return firstName != null && lastName != null && dateOfBirth != null;
  }

  /// Whether the profile has physical measurements
  bool get hasPhysicalMeasurements {
    return heightCm != null && weightKg != null;
  }

  /// Whether the profile has health information
  bool get hasHealthInfo {
    return smokingStatus != null ||
        exerciseFrequency != null ||
        sleepHours != null;
  }

  /// Whether the profile has ASVS ratings
  bool get hasAsvsRatings {
    return stressLevel != null || energyLevel != null || moodRating != null;
  }

  /// Profile completion percentage (0-100)
  int get completionPercentage {
    var completed = 0;
    const totalFields = 8; // Key fields for completion

    if (firstName != null && firstName!.isNotEmpty) completed++;
    if (lastName != null && lastName!.isNotEmpty) completed++;
    if (dateOfBirth != null) completed++;
    if (gender != null) completed++;
    if (heightCm != null) completed++;
    if (weightKg != null) completed++;
    if (smokingStatus != null) completed++;
    if (exerciseFrequency != null) completed++;

    return (completed / totalFields * 100).round();
  }

  /// Creates a copy of this profile with the given fields replaced.
  AsvsUserProfile copyWith({
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    Gender? gender,
    UnitPreference? unitPreference,
    double? heightCm,
    double? weightKg,
    double? bmi,
    SmokingStatus? smokingStatus,
    int? exerciseFrequency,
    int? sleepHours,
    List<String>? medications,
    List<String>? allergies,
    List<String>? medicalConditions,
    int? stressLevel,
    int? energyLevel,
    int? moodRating,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isOnboardingComplete,
  }) {
    return AsvsUserProfile(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      unitPreference: unitPreference ?? this.unitPreference,
      heightCm: heightCm ?? this.heightCm,
      weightKg: weightKg ?? this.weightKg,
      bmi: bmi ?? this.bmi,
      smokingStatus: smokingStatus ?? this.smokingStatus,
      exerciseFrequency: exerciseFrequency ?? this.exerciseFrequency,
      sleepHours: sleepHours ?? this.sleepHours,
      medications: medications ?? this.medications,
      allergies: allergies ?? this.allergies,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      stressLevel: stressLevel ?? this.stressLevel,
      energyLevel: energyLevel ?? this.energyLevel,
      moodRating: moodRating ?? this.moodRating,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isOnboardingComplete: isOnboardingComplete ?? this.isOnboardingComplete,
    );
  }

  /// Converts this profile to a JSON map for Firestore storage
  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender?.name,
      'unitPreference': unitPreference.name,
      'heightCm': heightCm,
      'weightKg': weightKg,
      'bmi': bmi,
      'smokingStatus': smokingStatus?.name,
      'exerciseFrequency': exerciseFrequency,
      'sleepHours': sleepHours,
      'medications': medications,
      'allergies': allergies,
      'medicalConditions': medicalConditions,
      'stressLevel': stressLevel,
      'energyLevel': energyLevel,
      'moodRating': moodRating,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isOnboardingComplete': isOnboardingComplete,
    };
  }

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        dateOfBirth,
        gender,
        unitPreference,
        heightCm,
        weightKg,
        bmi,
        smokingStatus,
        exerciseFrequency,
        sleepHours,
        medications,
        allergies,
        medicalConditions,
        stressLevel,
        energyLevel,
        moodRating,
        createdAt,
        updatedAt,
        isOnboardingComplete,
      ];
}
