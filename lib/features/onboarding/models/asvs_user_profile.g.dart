// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asvs_user_profile.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AsvsUserProfileAdapter extends TypeAdapter<AsvsUserProfile> {
  @override
  final int typeId = 1;

  @override
  AsvsUserProfile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AsvsUserProfile(
      firstName: fields[0] as String?,
      lastName: fields[1] as String?,
      dateOfBirth: fields[2] as DateTime?,
      gender: fields[3] as Gender?,
      unitPreference: fields[4] as UnitPreference,
      heightCm: fields[5] as double?,
      weightKg: fields[6] as double?,
      bmi: fields[7] as double?,
      smokingStatus: fields[8] as SmokingStatus?,
      exerciseFrequency: fields[9] as int?,
      sleepHours: fields[10] as int?,
      medications: (fields[11] as List).cast<String>(),
      allergies: (fields[12] as List).cast<String>(),
      medicalConditions: (fields[13] as List).cast<String>(),
      stressLevel: fields[14] as int?,
      energyLevel: fields[15] as int?,
      moodRating: fields[16] as int?,
      createdAt: fields[17] as DateTime?,
      updatedAt: fields[18] as DateTime?,
      isOnboardingComplete: fields[19] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, AsvsUserProfile obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.firstName)
      ..writeByte(1)
      ..write(obj.lastName)
      ..writeByte(2)
      ..write(obj.dateOfBirth)
      ..writeByte(3)
      ..write(obj.gender)
      ..writeByte(4)
      ..write(obj.unitPreference)
      ..writeByte(5)
      ..write(obj.heightCm)
      ..writeByte(6)
      ..write(obj.weightKg)
      ..writeByte(7)
      ..write(obj.bmi)
      ..writeByte(8)
      ..write(obj.smokingStatus)
      ..writeByte(9)
      ..write(obj.exerciseFrequency)
      ..writeByte(10)
      ..write(obj.sleepHours)
      ..writeByte(11)
      ..write(obj.medications)
      ..writeByte(12)
      ..write(obj.allergies)
      ..writeByte(13)
      ..write(obj.medicalConditions)
      ..writeByte(14)
      ..write(obj.stressLevel)
      ..writeByte(15)
      ..write(obj.energyLevel)
      ..writeByte(16)
      ..write(obj.moodRating)
      ..writeByte(17)
      ..write(obj.createdAt)
      ..writeByte(18)
      ..write(obj.updatedAt)
      ..writeByte(19)
      ..write(obj.isOnboardingComplete);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AsvsUserProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
