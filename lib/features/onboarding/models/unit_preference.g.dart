// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unit_preference.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UnitPreferenceAdapter extends TypeAdapter<UnitPreference> {
  @override
  final int typeId = 2;

  @override
  UnitPreference read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return UnitPreference.metric;
      case 1:
        return UnitPreference.imperial;
      default:
        return UnitPreference.metric;
    }
  }

  @override
  void write(BinaryWriter writer, UnitPreference obj) {
    switch (obj) {
      case UnitPreference.metric:
        writer.writeByte(0);
        break;
      case UnitPreference.imperial:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UnitPreferenceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
