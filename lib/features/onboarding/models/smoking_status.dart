import 'package:hive/hive.dart';

part 'smoking_status.g.dart';

/// {@template smoking_status}
/// Enumeration for user's smoking status.
/// {@endtemplate}
@HiveType(typeId: 4)
enum SmokingStatus {
  /// Never smoked
  @HiveField(0)
  never,

  /// Former smoker (quit smoking)
  @HiveField(1)
  former,

  /// Current smoker
  @HiveField(2)
  current,

  /// Occasional smoker (social/infrequent)
  @HiveField(3)
  occasional,
}

/// Extension methods for SmokingStatus
extension SmokingStatusExtension on SmokingStatus {
  /// Display name for the smoking status
  String get displayName {
    switch (this) {
      case SmokingStatus.never:
        return 'Never smoked';
      case SmokingStatus.former:
        return 'Former smoker';
      case SmokingStatus.current:
        return 'Current smoker';
      case SmokingStatus.occasional:
        return 'Occasional smoker';
    }
  }

  /// Description for the smoking status
  String get description {
    switch (this) {
      case SmokingStatus.never:
        return 'I have never smoked tobacco products';
      case SmokingStatus.former:
        return 'I used to smoke but have quit';
      case SmokingStatus.current:
        return 'I currently smoke regularly';
      case SmokingStatus.occasional:
        return 'I smoke occasionally or socially';
    }
  }

  /// Risk level associated with smoking status (for health calculations)
  int get riskLevel {
    switch (this) {
      case SmokingStatus.never:
        return 0;
      case SmokingStatus.former:
        return 1;
      case SmokingStatus.occasional:
        return 2;
      case SmokingStatus.current:
        return 3;
    }
  }
}
