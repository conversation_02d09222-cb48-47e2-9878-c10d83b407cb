// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'smoking_status.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SmokingStatusAdapter extends TypeAdapter<SmokingStatus> {
  @override
  final int typeId = 4;

  @override
  SmokingStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SmokingStatus.never;
      case 1:
        return SmokingStatus.former;
      case 2:
        return SmokingStatus.current;
      case 3:
        return SmokingStatus.occasional;
      default:
        return SmokingStatus.never;
    }
  }

  @override
  void write(BinaryWriter writer, SmokingStatus obj) {
    switch (obj) {
      case SmokingStatus.never:
        writer.writeByte(0);
        break;
      case SmokingStatus.former:
        writer.writeByte(1);
        break;
      case SmokingStatus.current:
        writer.writeByte(2);
        break;
      case SmokingStatus.occasional:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmokingStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
