import 'package:hive/hive.dart';

part 'gender.g.dart';

/// {@template gender}
/// Enumeration for user's gender identity.
/// {@endtemplate}
@HiveType(typeId: 3)
enum Gender {
  /// Male gender identity
  @HiveField(0)
  male,

  /// Female gender identity
  @HiveField(1)
  female,

  /// Non-binary gender identity
  @HiveField(2)
  nonBinary,

  /// Prefer not to disclose gender identity
  @HiveField(3)
  preferNotToSay,
}

/// Extension methods for Gender
extension GenderExtension on Gender {
  /// Display name for the gender
  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.nonBinary:
        return 'Non-binary';
      case Gender.preferNotToSay:
        return 'Prefer not to say';
    }
  }

  /// Short code for the gender (used in forms)
  String get code {
    switch (this) {
      case Gender.male:
        return 'M';
      case Gender.female:
        return 'F';
      case Gender.nonBinary:
        return 'NB';
      case Gender.preferNotToSay:
        return 'PNS';
    }
  }
}
