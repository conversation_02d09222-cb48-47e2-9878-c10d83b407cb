import 'package:hive/hive.dart';

part 'unit_preference.g.dart';

/// {@template unit_preference}
/// Enumeration for user's preferred unit system.
/// {@endtemplate}
@HiveType(typeId: 2)
enum UnitPreference {
  /// Metric system (kg, cm, °C)
  @HiveField(0)
  metric,

  /// Imperial system (lbs, ft/in, °F)
  @HiveField(1)
  imperial,
}

/// Extension methods for UnitPreference
extension UnitPreferenceExtension on UnitPreference {
  /// Display name for the unit preference
  String get displayName {
    switch (this) {
      case UnitPreference.metric:
        return 'Metric (kg, cm)';
      case UnitPreference.imperial:
        return 'Imperial (lbs, ft/in)';
    }
  }

  /// Weight unit label
  String get weightUnit {
    switch (this) {
      case UnitPreference.metric:
        return 'kg';
      case UnitPreference.imperial:
        return 'lbs';
    }
  }

  /// Height unit label
  String get heightUnit {
    switch (this) {
      case UnitPreference.metric:
        return 'cm';
      case UnitPreference.imperial:
        return 'ft/in';
    }
  }

  /// Temperature unit label
  String get temperatureUnit {
    switch (this) {
      case UnitPreference.metric:
        return '°C';
      case UnitPreference.imperial:
        return '°F';
    }
  }
}
