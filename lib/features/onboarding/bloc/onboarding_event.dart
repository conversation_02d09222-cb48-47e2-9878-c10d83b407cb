part of 'onboarding_bloc.dart';

/// {@template onboarding_event}
/// Base class for all onboarding events.
/// {@endtemplate}
abstract class OnboardingEvent extends Equatable {
  /// {@macro onboarding_event}
  const OnboardingEvent();

  @override
  List<Object?> get props => [];
}

/// {@template start_onboarding_event}
/// Event to start the onboarding process.
/// {@endtemplate}
class StartOnboarding extends OnboardingEvent {
  /// {@macro start_onboarding_event}
  const StartOnboarding();

  @override
  String toString() => 'StartOnboarding()';
}

/// {@template load_existing_profile_event}
/// Event to load an existing profile and continue onboarding.
/// {@endtemplate}
class LoadExistingProfile extends OnboardingEvent {
  /// {@macro load_existing_profile_event}
  const LoadExistingProfile();

  @override
  String toString() => 'LoadExistingProfile()';
}

/// {@template update_profile_event}
/// Event to update profile data during onboarding.
/// {@endtemplate}
class UpdateProfile extends OnboardingEvent {
  /// {@macro update_profile_event}
  const UpdateProfile(this.updates);

  /// Map of field updates to apply to the profile
  final Map<String, dynamic> updates;

  @override
  List<Object?> get props => [updates];

  @override
  String toString() => 'UpdateProfile(updates: ${updates.keys.join(', ')})';
}

/// {@template next_step_event}
/// Event to move to the next onboarding step.
/// {@endtemplate}
class NextStep extends OnboardingEvent {
  /// {@macro next_step_event}
  const NextStep();

  @override
  String toString() => 'NextStep()';
}

/// {@template previous_step_event}
/// Event to move to the previous onboarding step.
/// {@endtemplate}
class PreviousStep extends OnboardingEvent {
  /// {@macro previous_step_event}
  const PreviousStep();

  @override
  String toString() => 'PreviousStep()';
}

/// {@template go_to_step_event}
/// Event to go to a specific onboarding step.
/// {@endtemplate}
class GoToStep extends OnboardingEvent {
  /// {@macro go_to_step_event}
  const GoToStep(this.step);

  /// The step to navigate to
  final OnboardingStep step;

  @override
  List<Object?> get props => [step];

  @override
  String toString() => 'GoToStep(step: ${step.name})';
}

/// {@template validate_current_step_event}
/// Event to validate the current onboarding step.
/// {@endtemplate}
class ValidateCurrentStep extends OnboardingEvent {
  /// {@macro validate_current_step_event}
  const ValidateCurrentStep();

  @override
  String toString() => 'ValidateCurrentStep()';
}

/// {@template complete_onboarding_event}
/// Event to complete the onboarding process.
/// {@endtemplate}
class CompleteOnboarding extends OnboardingEvent {
  /// {@macro complete_onboarding_event}
  const CompleteOnboarding();

  @override
  String toString() => 'CompleteOnboarding()';
}

/// {@template reset_onboarding_event}
/// Event to reset the onboarding process.
/// {@endtemplate}
class ResetOnboarding extends OnboardingEvent {
  /// {@macro reset_onboarding_event}
  const ResetOnboarding({this.clearExistingData = false});

  /// Whether to clear existing profile data
  final bool clearExistingData;

  @override
  List<Object?> get props => [clearExistingData];

  @override
  String toString() => 'ResetOnboarding(clearExistingData: $clearExistingData)';
}
