part of 'onboarding_bloc.dart';

/// {@template onboarding_state}
/// Base class for all onboarding states.
/// {@endtemplate}
abstract class OnboardingState extends Equatable {
  /// {@macro onboarding_state}
  const OnboardingState({
    this.profile = const AsvsUserProfile(),
    this.error,
  });

  /// Current profile being built
  final AsvsUserProfile profile;

  /// Error message if any error occurred
  final String? error;

  @override
  List<Object?> get props => [profile, error];
}

/// {@template onboarding_initial_state}
/// Initial state when onboarding hasn't started.
/// {@endtemplate}
class OnboardingInitial extends OnboardingState {
  /// {@macro onboarding_initial_state}
  const OnboardingInitial({
    super.profile,
    super.error,
  });

  @override
  String toString() => 'OnboardingInitial()';
}

/// {@template onboarding_loading_state}
/// State when onboarding is loading or initializing.
/// {@endtemplate}
class OnboardingLoading extends OnboardingState {
  /// {@macro onboarding_loading_state}
  const OnboardingLoading({
    super.profile,
    super.error,
  });

  @override
  String toString() => 'OnboardingLoading()';
}

/// {@template onboarding_in_progress_state}
/// State when onboarding is in progress.
/// {@endtemplate}
class OnboardingInProgress extends OnboardingState {
  /// {@macro onboarding_in_progress_state}
  const OnboardingInProgress({
    required this.currentStep,
    required this.stepIndex,
    required this.totalSteps,
    super.profile,
    super.error,
    this.validationResult,
  });

  /// Current onboarding step
  final OnboardingStep currentStep;

  /// Current step index (0-based)
  final int stepIndex;

  /// Total number of steps
  final int totalSteps;

  /// Validation result for current step
  final ValidationResult? validationResult;

  /// Progress percentage (0-100)
  double get progress => totalSteps > 0 ? (stepIndex / totalSteps) * 100 : 0;

  /// Whether current step can proceed to next
  bool get canProceed => validationResult?.isValid ?? true;

  @override
  List<Object?> get props => [
        currentStep,
        stepIndex,
        totalSteps,
        validationResult,
        ...super.props,
      ];

  @override
  String toString() => 'OnboardingInProgress('
      'step: ${currentStep.name}, '
      'index: $stepIndex/$totalSteps, '
      'progress: ${progress.toStringAsFixed(1)}%)';
}

/// {@template onboarding_completing_state}
/// State when onboarding is being completed.
/// {@endtemplate}
class OnboardingCompleting extends OnboardingState {
  /// {@macro onboarding_completing_state}
  const OnboardingCompleting({
    super.profile,
    super.error,
  });

  @override
  String toString() => 'OnboardingCompleting()';
}

/// {@template onboarding_completed_state}
/// State when onboarding has been completed successfully.
/// {@endtemplate}
class OnboardingCompleted extends OnboardingState {
  /// {@macro onboarding_completed_state}
  const OnboardingCompleted({
    required super.profile,
    this.message,
    super.error,
  });

  /// Success message
  final String? message;

  @override
  List<Object?> get props => [message, ...super.props];

  @override
  String toString() => 'OnboardingCompleted(message: $message)';
}

/// {@template onboarding_error_state}
/// State when an error occurs during onboarding.
/// {@endtemplate}
class OnboardingError extends OnboardingState {
  /// {@macro onboarding_error_state}
  const OnboardingError({
    required String error,
    super.profile,
  }) : super(error: error);

  @override
  String toString() => 'OnboardingError(error: $error)';
}
