import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'onboarding_event.dart';
part 'onboarding_state.dart';

/// {@template onboarding_bloc}
/// BLoC that manages the onboarding flow state and operations.
///
/// Handles the multi-step onboarding process, profile updates,
/// validation, and completion logic.
/// {@endtemplate}
class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  /// {@macro onboarding_bloc}
  OnboardingBloc({
    required AsvsProfileRepository repository,
    required OnboardingService onboardingService,
  })  : _repository = repository,
        _onboardingService = onboardingService,
        super(const OnboardingInitial()) {
    // Register event handlers
    on<StartOnboarding>(_onStartOnboarding);
    on<LoadExistingProfile>(_onLoadExistingProfile);
    on<UpdateProfile>(_onUpdateProfile);
    on<NextStep>(_onNextStep);
    on<PreviousStep>(_onPreviousStep);
    on<GoToStep>(_onGoToStep);
    on<ValidateCurrentStep>(_onValidateCurrentStep);
    on<CompleteOnboarding>(_onCompleteOnboarding);
    on<ResetOnboarding>(_onResetOnboarding);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'OnboardingBloc initialized',
      ),
    );
  }

  final AsvsProfileRepository _repository;
  final OnboardingService _onboardingService;
  final LoggerService _logger = LoggerService();

  /// Current profile being built during onboarding
  AsvsUserProfile _currentProfile = const AsvsUserProfile();

  /// Handles starting the onboarding process
  Future<void> _onStartOnboarding(
    StartOnboarding event,
    Emitter<OnboardingState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Starting onboarding',
      ),
    );

    emit(const OnboardingLoading());

    try {
      // Initialize repository
      await _repository.initialize();

      // Check if user already has a profile
      final existingProfile = await _repository.loadProfile();
      if (existingProfile != null && existingProfile.isOnboardingComplete) {
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.onboardingModule,
            'User already has completed profile',
          ),
        );

        emit(
          OnboardingCompleted(
            profile: existingProfile,
            message: 'Welcome back! Your profile is already set up.',
          ),
        );
        return;
      }

      // Start fresh or continue existing onboarding
      _currentProfile = existingProfile ?? const AsvsUserProfile();
      final nextStep = _onboardingService.getNextStep(_currentProfile);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Onboarding started',
          'Next step: ${nextStep.name}',
        ),
      );

      emit(
        OnboardingInProgress(
          currentStep: nextStep,
          profile: _currentProfile,
          stepIndex: _getStepIndex(nextStep),
          totalSteps: OnboardingStep.values.length - 1, // Exclude complete
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to start onboarding: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        OnboardingError(
          error: 'Failed to start onboarding: $error',
          profile: _currentProfile,
        ),
      );
    }
  }

  /// Handles loading existing profile
  Future<void> _onLoadExistingProfile(
    LoadExistingProfile event,
    Emitter<OnboardingState> emit,
  ) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Loading existing profile',
      ),
    );

    emit(const OnboardingLoading());

    try {
      final profile = await _repository.loadProfile();
      if (profile != null) {
        _currentProfile = profile;
        final nextStep = _onboardingService.getNextStep(profile);

        if (nextStep == OnboardingStep.complete) {
          emit(
            OnboardingCompleted(
              profile: profile,
              message: 'Profile loaded successfully',
            ),
          );
        } else {
          emit(
            OnboardingInProgress(
              currentStep: nextStep,
              profile: profile,
              stepIndex: _getStepIndex(nextStep),
              totalSteps: OnboardingStep.values.length - 1,
            ),
          );
        }
      } else {
        emit(const OnboardingInitial());
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to load profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        OnboardingError(
          error: 'Failed to load profile: $error',
          profile: _currentProfile,
        ),
      );
    }
  }

  /// Handles profile updates
  Future<void> _onUpdateProfile(
    UpdateProfile event,
    Emitter<OnboardingState> emit,
  ) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Updating profile',
        'Field updates: ${event.updates.keys.join(', ')}',
      ),
    );

    try {
      // Apply updates to current profile
      _currentProfile = _applyProfileUpdates(_currentProfile, event.updates);

      // Create profile with calculations
      _currentProfile =
          _onboardingService.createProfileWithCalculations(_currentProfile);

      // Validate current step
      final validation = _onboardingService.validateProfile(_currentProfile);

      if (state is OnboardingInProgress) {
        final currentState = state as OnboardingInProgress;
        emit(
          OnboardingInProgress(
            currentStep: currentState.currentStep,
            profile: _currentProfile,
            stepIndex: currentState.stepIndex,
            totalSteps: currentState.totalSteps,
            validationResult: validation,
          ),
        );
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile updated successfully',
          'Completion: ${_currentProfile.completionPercentage}%',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to update profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        OnboardingError(
          error: 'Failed to update profile: $error',
          profile: _currentProfile,
        ),
      );
    }
  }

  /// Handles moving to next step
  Future<void> _onNextStep(
    NextStep event,
    Emitter<OnboardingState> emit,
  ) async {
    if (state is! OnboardingInProgress) return;

    final currentState = state as OnboardingInProgress;

    // Sync internal profile with current state profile
    _currentProfile = currentState.profile;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Moving to next step',
        'Current: ${currentState.currentStep.name}',
      ),
    );

    // Validate current step before proceeding
    final validation = _onboardingService.validateProfile(_currentProfile);
    if (!_isCurrentStepValid(currentState.currentStep, validation)) {
      emit(
        OnboardingInProgress(
          currentStep: currentState.currentStep,
          profile: _currentProfile,
          stepIndex: currentState.stepIndex,
          totalSteps: currentState.totalSteps,
          validationResult: validation,
          error: 'Please complete all required fields before proceeding',
        ),
      );
      return;
    }

    // Determine next step
    final nextStep = _getNextOnboardingStep(currentState.currentStep);
    if (nextStep == OnboardingStep.complete) {
      add(const CompleteOnboarding());
      return;
    }

    emit(
      OnboardingInProgress(
        currentStep: nextStep,
        profile: _currentProfile,
        stepIndex: _getStepIndex(nextStep),
        totalSteps: currentState.totalSteps,
      ),
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Moved to next step',
        'New step: ${nextStep.name}',
      ),
    );
  }

  /// Handles moving to previous step
  Future<void> _onPreviousStep(
    PreviousStep event,
    Emitter<OnboardingState> emit,
  ) async {
    if (state is! OnboardingInProgress) return;

    final currentState = state as OnboardingInProgress;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Moving to previous step',
        'Current: ${currentState.currentStep.name}',
      ),
    );

    final previousStep = _getPreviousOnboardingStep(currentState.currentStep);
    if (previousStep == null) return; // Already at first step

    emit(
      OnboardingInProgress(
        currentStep: previousStep,
        profile: _currentProfile,
        stepIndex: _getStepIndex(previousStep),
        totalSteps: currentState.totalSteps,
      ),
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Moved to previous step',
        'New step: ${previousStep.name}',
      ),
    );
  }

  /// Handles going to specific step
  Future<void> _onGoToStep(
    GoToStep event,
    Emitter<OnboardingState> emit,
  ) async {
    if (state is! OnboardingInProgress) return;

    final currentState = state as OnboardingInProgress;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Going to specific step',
        'Target: ${event.step.name}',
      ),
    );

    emit(
      OnboardingInProgress(
        currentStep: event.step,
        profile: _currentProfile,
        stepIndex: _getStepIndex(event.step),
        totalSteps: currentState.totalSteps,
      ),
    );
  }

  /// Handles step validation
  Future<void> _onValidateCurrentStep(
    ValidateCurrentStep event,
    Emitter<OnboardingState> emit,
  ) async {
    if (state is! OnboardingInProgress) return;

    final currentState = state as OnboardingInProgress;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Validating current step',
        'Step: ${currentState.currentStep.name}',
      ),
    );

    final validation = _onboardingService.validateProfile(_currentProfile);

    emit(
      OnboardingInProgress(
        currentStep: currentState.currentStep,
        profile: _currentProfile,
        stepIndex: currentState.stepIndex,
        totalSteps: currentState.totalSteps,
        validationResult: validation,
      ),
    );
  }

  /// Handles completing onboarding
  Future<void> _onCompleteOnboarding(
    CompleteOnboarding event,
    Emitter<OnboardingState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Completing onboarding',
      ),
    );

    emit(OnboardingCompleting(profile: _currentProfile));

    try {
      // Final validation
      final validation = _onboardingService.validateProfile(_currentProfile);
      if (!validation.isValid) {
        emit(
          OnboardingError(
            error: 'Profile validation failed: ${validation.errors.join(', ')}',
            profile: _currentProfile,
          ),
        );
        return;
      }

      // Mark as complete and save
      final completedProfile = _onboardingService.createProfileWithCalculations(
        _currentProfile.copyWith(isOnboardingComplete: true),
      );

      await _repository.saveProfile(completedProfile);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Onboarding completed successfully',
          'Profile completion: ${completedProfile.completionPercentage}%',
        ),
      );

      emit(
        OnboardingCompleted(
          profile: completedProfile,
          message:
              'Congratulations! Your profile has been set up successfully.',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to complete onboarding: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        OnboardingError(
          error: 'Failed to complete onboarding: $error',
          profile: _currentProfile,
        ),
      );
    }
  }

  /// Handles resetting onboarding
  Future<void> _onResetOnboarding(
    ResetOnboarding event,
    Emitter<OnboardingState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Resetting onboarding',
      ),
    );

    try {
      // Clear current profile
      _currentProfile = const AsvsUserProfile();

      // Delete existing profile if requested
      if (event.clearExistingData) {
        await _repository.deleteProfile();
      }

      emit(const OnboardingInitial());

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Onboarding reset successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to reset onboarding: $error',
        ),
        error,
        stackTrace,
      );

      emit(
        OnboardingError(
          error: 'Failed to reset onboarding: $error',
          profile: _currentProfile,
        ),
      );
    }
  }

  // Helper methods

  /// Applies profile updates from a map
  AsvsUserProfile _applyProfileUpdates(
    AsvsUserProfile profile,
    Map<String, dynamic> updates,
  ) {
    return profile.copyWith(
      firstName: updates['firstName'] as String?,
      lastName: updates['lastName'] as String?,
      dateOfBirth: updates['dateOfBirth'] as DateTime?,
      gender: updates['gender'] as Gender?,
      unitPreference: updates['unitPreference'] as UnitPreference?,
      heightCm: updates['heightCm'] as double?,
      weightKg: updates['weightKg'] as double?,
      smokingStatus: updates['smokingStatus'] as SmokingStatus?,
      exerciseFrequency: updates['exerciseFrequency'] as int?,
      sleepHours: updates['sleepHours'] as int?,
      medications: updates['medications'] as List<String>?,
      allergies: updates['allergies'] as List<String>?,
      medicalConditions: updates['medicalConditions'] as List<String>?,
      stressLevel: updates['stressLevel'] as int?,
      energyLevel: updates['energyLevel'] as int?,
      moodRating: updates['moodRating'] as int?,
    );
  }

  /// Gets the index of a step for progress tracking
  int _getStepIndex(OnboardingStep step) {
    return OnboardingStep.values.indexOf(step);
  }

  /// Gets the next onboarding step
  OnboardingStep _getNextOnboardingStep(OnboardingStep currentStep) {
    final currentIndex = OnboardingStep.values.indexOf(currentStep);
    if (currentIndex < OnboardingStep.values.length - 1) {
      return OnboardingStep.values[currentIndex + 1];
    }
    return OnboardingStep.complete;
  }

  /// Gets the previous onboarding step
  OnboardingStep? _getPreviousOnboardingStep(OnboardingStep currentStep) {
    final currentIndex = OnboardingStep.values.indexOf(currentStep);
    if (currentIndex > 0) {
      return OnboardingStep.values[currentIndex - 1];
    }
    return null;
  }

  /// Checks if current step is valid
  bool _isCurrentStepValid(OnboardingStep step, ValidationResult validation) {
    switch (step) {
      case OnboardingStep.personalInfo:
        return _currentProfile.hasBasicInfo;
      case OnboardingStep.physicalMeasurements:
        return _currentProfile.hasPhysicalMeasurements;
      case OnboardingStep.healthInfo:
        return true; // Optional step
      case OnboardingStep.asvsRatings:
        return true; // Optional step
      case OnboardingStep.complete:
        return validation.isValid;
    }
  }
}
