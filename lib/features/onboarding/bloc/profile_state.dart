part of 'profile_bloc.dart';

/// {@template profile_state}
/// Base class for all profile states.
/// {@endtemplate}
abstract class ProfileState extends Equatable {
  /// {@macro profile_state}
  const ProfileState({
    this.profile,
    this.error,
    this.message,
  });

  /// Current profile
  final AsvsUserProfile? profile;

  /// Error message if any error occurred
  final String? error;

  /// Success or info message
  final String? message;

  @override
  List<Object?> get props => [profile, error, message];
}

/// {@template profile_initial_state}
/// Initial state when profile hasn't been loaded.
/// {@endtemplate}
class ProfileInitial extends ProfileState {
  /// {@macro profile_initial_state}
  const ProfileInitial({
    super.profile,
    super.error,
    super.message,
  });

  @override
  String toString() => 'ProfileInitial()';
}

/// {@template profile_loading_state}
/// State when profile is being loaded.
/// {@endtemplate}
class ProfileLoading extends ProfileState {
  /// {@macro profile_loading_state}
  const ProfileLoading({
    super.profile,
    super.error,
    super.message,
  });

  @override
  String toString() => 'ProfileLoading()';
}

/// {@template profile_loaded_state}
/// State when profile has been loaded successfully.
/// {@endtemplate}
class ProfileLoaded extends ProfileState {
  /// {@macro profile_loaded_state}
  const ProfileLoaded({
    required AsvsUserProfile profile,
    this.validationResult,
    this.healthRiskAssessment,
    this.hasUnsavedChanges = false,
    super.error,
    super.message,
  }) : super(profile: profile);

  /// Validation result for the profile
  final ValidationResult? validationResult;

  /// Health risk assessment for the profile
  final HealthRiskAssessment? healthRiskAssessment;

  /// Whether the profile has unsaved changes
  final bool hasUnsavedChanges;

  /// Gets the non-null profile
  AsvsUserProfile get currentProfile => profile!;

  /// Whether the profile is valid
  bool get isValid => validationResult?.isValid ?? true;

  /// Profile completion percentage
  int get completionPercentage => profile?.completionPercentage ?? 0;

  /// Whether onboarding is complete
  bool get isOnboardingComplete => profile?.isOnboardingComplete ?? false;

  @override
  List<Object?> get props => [
        profile,
        validationResult,
        healthRiskAssessment,
        hasUnsavedChanges,
        error,
        message,
      ];

  @override
  String toString() => 'ProfileLoaded('
      'completion: $completionPercentage%, '
      'valid: $isValid, '
      'unsaved: $hasUnsavedChanges)';
}

/// {@template profile_saving_state}
/// State when profile is being saved.
/// {@endtemplate}
class ProfileSaving extends ProfileState {
  /// {@macro profile_saving_state}
  const ProfileSaving({
    required AsvsUserProfile profile,
    super.error,
    super.message,
  }) : super(profile: profile);

  @override
  String toString() => 'ProfileSaving()';
}

/// {@template profile_empty_state}
/// State when no profile exists.
/// {@endtemplate}
class ProfileEmpty extends ProfileState {
  /// {@macro profile_empty_state}
  const ProfileEmpty({
    super.profile,
    super.error,
    super.message,
  });

  @override
  String toString() => 'ProfileEmpty()';
}

/// {@template profile_error_state}
/// State when an error occurs with profile operations.
/// {@endtemplate}
class ProfileError extends ProfileState {
  /// {@macro profile_error_state}
  const ProfileError({
    required String error,
    super.profile,
    super.message,
  }) : super(error: error);

  @override
  String toString() => 'ProfileError(error: $error)';
}
