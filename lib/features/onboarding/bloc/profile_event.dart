part of 'profile_bloc.dart';

/// {@template profile_event}
/// Base class for all profile events.
/// {@endtemplate}
abstract class ProfileEvent extends Equatable {
  /// {@macro profile_event}
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

/// {@template load_profile_event}
/// Event to load the user's profile.
/// {@endtemplate}
class LoadProfile extends ProfileEvent {
  /// {@macro load_profile_event}
  const LoadProfile();

  @override
  String toString() => 'LoadProfile()';
}

/// {@template update_profile_event}
/// Event to update profile data.
/// {@endtemplate}
class UpdateProfile extends ProfileEvent {
  /// {@macro update_profile_event}
  const UpdateProfile(this.updates);

  /// Map of field updates to apply to the profile
  final Map<String, dynamic> updates;

  @override
  List<Object?> get props => [updates];

  @override
  String toString() => 'UpdateProfile(updates: ${updates.keys.join(', ')})';
}

/// {@template save_profile_event}
/// Event to save the current profile.
/// {@endtemplate}
class SaveProfile extends ProfileEvent {
  /// {@macro save_profile_event}
  const SaveProfile({this.requireValidation = true});

  /// Whether to require validation before saving
  final bool requireValidation;

  @override
  List<Object?> get props => [requireValidation];

  @override
  String toString() => 'SaveProfile(requireValidation: $requireValidation)';
}

/// {@template delete_profile_event}
/// Event to delete the user's profile.
/// {@endtemplate}
class DeleteProfile extends ProfileEvent {
  /// {@macro delete_profile_event}
  const DeleteProfile();

  @override
  String toString() => 'DeleteProfile()';
}

/// {@template refresh_profile_event}
/// Event to refresh the profile from storage.
/// {@endtemplate}
class RefreshProfile extends ProfileEvent {
  /// {@macro refresh_profile_event}
  const RefreshProfile();

  @override
  String toString() => 'RefreshProfile()';
}

/// {@template validate_profile_event}
/// Event to validate the current profile.
/// {@endtemplate}
class ValidateProfile extends ProfileEvent {
  /// {@macro validate_profile_event}
  const ValidateProfile();

  @override
  String toString() => 'ValidateProfile()';
}

/// {@template sync_profile_event}
/// Event to sync the profile with cloud storage.
/// {@endtemplate}
class SyncProfile extends ProfileEvent {
  /// {@macro sync_profile_event}
  const SyncProfile();

  @override
  String toString() => 'SyncProfile()';
}
