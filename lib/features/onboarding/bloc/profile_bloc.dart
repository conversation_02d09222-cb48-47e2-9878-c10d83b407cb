import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/gender.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:equatable/equatable.dart';

part 'profile_event.dart';
part 'profile_state.dart';

/// {@template profile_bloc}
/// BLoC that manages ASVS profile CRUD operations.
///
/// Handles loading, updating, saving, and deleting user profiles
/// with proper validation and error handling.
/// {@endtemplate}
class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  /// {@macro profile_bloc}
  ProfileBloc({
    required AsvsProfileRepository repository,
    required OnboardingService onboardingService,
  })  : _repository = repository,
        _onboardingService = onboardingService,
        super(const ProfileInitial()) {
    // Register event handlers
    on<LoadProfile>(_onLoadProfile);
    on<UpdateProfile>(_onUpdateProfile);
    on<SaveProfile>(_onSaveProfile);
    on<DeleteProfile>(_onDeleteProfile);
    on<RefreshProfile>(_onRefreshProfile);
    on<ValidateProfile>(_onValidateProfile);
    on<SyncProfile>(_onSyncProfile);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'ProfileBloc initialized',
      ),
    );
  }

  final AsvsProfileRepository _repository;
  final OnboardingService _onboardingService;
  final LoggerService _logger = LoggerService();

  /// Handles loading profile
  Future<void> _onLoadProfile(
    LoadProfile event,
    Emitter<ProfileState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Loading profile',
      ),
    );

    emit(const ProfileLoading());

    try {
      // Initialize repository
      await _repository.initialize();

      // Load profile
      final profile = await _repository.loadProfile();

      if (profile != null) {
        // Validate profile
        final validation = _onboardingService.validateProfile(profile);

        // Assess health risk
        final healthRisk = _onboardingService.assessHealthRisk(profile);

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.onboardingModule,
            'Profile loaded successfully',
            'Completion: ${profile.completionPercentage}%, '
                'Valid: ${validation.isValid}',
          ),
        );

        emit(
          ProfileLoaded(
            profile: profile,
            validationResult: validation,
            healthRiskAssessment: healthRisk,
          ),
        );
      } else {
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.onboardingModule,
            'No profile found',
          ),
        );

        emit(const ProfileEmpty());
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to load profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(ProfileError(error: 'Failed to load profile: $error'));
    }
  }

  /// Handles updating profile
  Future<void> _onUpdateProfile(
    UpdateProfile event,
    Emitter<ProfileState> emit,
  ) async {
    if (state is! ProfileLoaded) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Cannot update profile - no profile loaded',
        ),
      );
      return;
    }

    final currentState = state as ProfileLoaded;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Updating profile',
        'Updates: ${event.updates.keys.join(', ')}',
      ),
    );

    try {
      // Apply updates to profile
      final updatedProfile =
          _applyProfileUpdates(currentState.currentProfile, event.updates);

      // Create profile with calculations
      final profileWithCalculations =
          _onboardingService.createProfileWithCalculations(updatedProfile);

      // Validate updated profile
      final validation =
          _onboardingService.validateProfile(profileWithCalculations);

      // Assess health risk
      final healthRisk =
          _onboardingService.assessHealthRisk(profileWithCalculations);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile updated successfully',
          'Completion: ${profileWithCalculations.completionPercentage}%',
        ),
      );

      emit(
        ProfileLoaded(
          profile: profileWithCalculations,
          validationResult: validation,
          healthRiskAssessment: healthRisk,
          hasUnsavedChanges: true,
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to update profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(ProfileError(error: 'Failed to update profile: $error'));
    }
  }

  /// Handles saving profile
  Future<void> _onSaveProfile(
    SaveProfile event,
    Emitter<ProfileState> emit,
  ) async {
    if (state is! ProfileLoaded) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Cannot save profile - no profile loaded',
        ),
      );
      return;
    }

    final currentState = state as ProfileLoaded;

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Saving profile',
      ),
    );

    emit(ProfileSaving(profile: currentState.currentProfile));

    try {
      // Validate before saving
      final validation =
          _onboardingService.validateProfile(currentState.currentProfile);
      if (!validation.isValid && event.requireValidation) {
        emit(
          ProfileError(
            error: 'Profile validation failed: ${validation.errors.join(', ')}',
          ),
        );
        return;
      }

      // Save profile
      await _repository.saveProfile(currentState.currentProfile);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile saved successfully',
        ),
      );

      emit(
        ProfileLoaded(
          profile: currentState.currentProfile,
          validationResult: currentState.validationResult,
          healthRiskAssessment: currentState.healthRiskAssessment,
          message: 'Profile saved successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to save profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(ProfileError(error: 'Failed to save profile: $error'));
    }
  }

  /// Handles deleting profile
  Future<void> _onDeleteProfile(
    DeleteProfile event,
    Emitter<ProfileState> emit,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Deleting profile',
      ),
    );

    emit(const ProfileLoading());

    try {
      await _repository.deleteProfile();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile deleted successfully',
        ),
      );

      emit(const ProfileEmpty(message: 'Profile deleted successfully'));
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to delete profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(ProfileError(error: 'Failed to delete profile: $error'));
    }
  }

  /// Handles refreshing profile
  Future<void> _onRefreshProfile(
    RefreshProfile event,
    Emitter<ProfileState> emit,
  ) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Refreshing profile',
      ),
    );

    add(const LoadProfile());
  }

  /// Handles validating profile
  Future<void> _onValidateProfile(
    ValidateProfile event,
    Emitter<ProfileState> emit,
  ) async {
    if (state is! ProfileLoaded) return;

    final currentState = state as ProfileLoaded;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Validating profile',
      ),
    );

    try {
      final validation =
          _onboardingService.validateProfile(currentState.currentProfile);
      final healthRisk =
          _onboardingService.assessHealthRisk(currentState.currentProfile);

      emit(
        ProfileLoaded(
          profile: currentState.currentProfile,
          validationResult: validation,
          healthRiskAssessment: healthRisk,
          hasUnsavedChanges: currentState.hasUnsavedChanges,
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to validate profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(ProfileError(error: 'Failed to validate profile: $error'));
    }
  }

  /// Handles syncing profile
  Future<void> _onSyncProfile(
    SyncProfile event,
    Emitter<ProfileState> emit,
  ) async {
    if (state is! ProfileLoaded) return;

    final currentState = state as ProfileLoaded;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Syncing profile',
      ),
    );

    try {
      await _repository.syncProfile();

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.onboardingModule,
          'Profile synced successfully',
        ),
      );

      emit(
        ProfileLoaded(
          profile: currentState.currentProfile,
          validationResult: currentState.validationResult,
          healthRiskAssessment: currentState.healthRiskAssessment,
          hasUnsavedChanges: currentState.hasUnsavedChanges,
          message: 'Profile synced successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.onboardingModule,
          LoggingConstants.criticalError,
          'Failed to sync profile: $error',
        ),
        error,
        stackTrace,
      );

      emit(ProfileError(error: 'Failed to sync profile: $error'));
    }
  }

  // Helper methods

  /// Applies profile updates from a map
  AsvsUserProfile _applyProfileUpdates(
    AsvsUserProfile profile,
    Map<String, dynamic> updates,
  ) {
    return profile.copyWith(
      firstName: updates['firstName'] as String?,
      lastName: updates['lastName'] as String?,
      dateOfBirth: updates['dateOfBirth'] as DateTime?,
      gender: updates['gender'] as Gender?,
      unitPreference: updates['unitPreference'] as UnitPreference?,
      heightCm: updates['heightCm'] as double?,
      weightKg: updates['weightKg'] as double?,
      smokingStatus: updates['smokingStatus'] as SmokingStatus?,
      exerciseFrequency: updates['exerciseFrequency'] as int?,
      sleepHours: updates['sleepHours'] as int?,
      medications: updates['medications'] as List<String>?,
      allergies: updates['allergies'] as List<String>?,
      medicalConditions: updates['medicalConditions'] as List<String>?,
      stressLevel: updates['stressLevel'] as int?,
      energyLevel: updates['energyLevel'] as int?,
      moodRating: updates['moodRating'] as int?,
    );
  }
}
