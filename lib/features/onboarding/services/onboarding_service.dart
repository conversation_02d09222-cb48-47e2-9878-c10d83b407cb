import 'package:bloomg_flutter/features/onboarding/models/asvs_user_profile.dart';
import 'package:bloomg_flutter/features/onboarding/models/smoking_status.dart';
import 'package:bloomg_flutter/features/onboarding/models/unit_preference.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template onboarding_service}
/// Service for handling onboarding business logic and validation.
///
/// Provides methods for profile validation, calculations, and onboarding
/// flow management following the project's business rules.
/// {@endtemplate}
class OnboardingService {
  /// {@macro onboarding_service}
  OnboardingService();

  final LoggerService _logger = LoggerService();

  /// Validates a profile for completeness and data integrity
  ValidationResult validateProfile(AsvsUserProfile profile) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Validating profile',
        'Completion: ${profile.completionPercentage}%',
      ),
    );

    final errors = <String>[];
    final warnings = <String>[];

    // Validate personal information
    if (profile.firstName == null || profile.firstName!.trim().isEmpty) {
      errors.add('First name is required');
    }
    if (profile.lastName == null || profile.lastName!.trim().isEmpty) {
      errors.add('Last name is required');
    }
    if (profile.dateOfBirth == null) {
      errors.add('Date of birth is required');
    } else {
      final age = profile.age;
      if (age != null && age < 13) {
        errors.add('User must be at least 13 years old');
      }
      if (age != null && age > 120) {
        warnings.add('Please verify the date of birth');
      }
    }

    // Validate physical measurements
    if (profile.heightCm != null) {
      if (profile.heightCm! < 50 || profile.heightCm! > 300) {
        errors.add('Height must be between 50cm and 300cm');
      }
    }
    if (profile.weightKg != null) {
      if (profile.weightKg! < 20 || profile.weightKg! > 500) {
        errors.add('Weight must be between 20kg and 500kg');
      }
    }

    // Validate health information
    if (profile.exerciseFrequency != null) {
      if (profile.exerciseFrequency! < 0 || profile.exerciseFrequency! > 7) {
        errors.add('Exercise frequency must be between 0 and 7 days per week');
      }
    }
    if (profile.sleepHours != null) {
      if (profile.sleepHours! < 1 || profile.sleepHours! > 24) {
        errors.add('Sleep hours must be between 1 and 24 hours');
      }
    }

    // Validate ASVS ratings
    if (profile.stressLevel != null) {
      if (profile.stressLevel! < 1 || profile.stressLevel! > 10) {
        errors.add('Stress level must be between 1 and 10');
      }
    }
    if (profile.energyLevel != null) {
      if (profile.energyLevel! < 1 || profile.energyLevel! > 10) {
        errors.add('Energy level must be between 1 and 10');
      }
    }
    if (profile.moodRating != null) {
      if (profile.moodRating! < 1 || profile.moodRating! > 10) {
        errors.add('Mood rating must be between 1 and 10');
      }
    }

    final result = ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Profile validation completed',
        'Valid: ${result.isValid}, Errors: ${errors.length}, '
            'Warnings: ${warnings.length}',
      ),
    );

    return result;
  }

  /// Calculates BMI from height and weight
  double? calculateBmi(double? heightCm, double? weightKg) {
    if (heightCm == null || weightKg == null) return null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Calculating BMI',
        'Height: ${heightCm}cm, Weight: ${weightKg}kg',
      ),
    );

    final heightM = heightCm / 100;
    final bmi = weightKg / (heightM * heightM);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'BMI calculated',
        'BMI: ${bmi.toStringAsFixed(1)}',
      ),
    );

    return bmi;
  }

  /// Calculates age from date of birth
  int? calculateAge(DateTime? dateOfBirth) {
    if (dateOfBirth == null) return null;

    final now = DateTime.now();
    var age = now.year - dateOfBirth.year;

    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Age calculated',
        'DOB: ${dateOfBirth.toIso8601String()}, Age: $age',
      ),
    );

    return age;
  }

  /// Converts weight between metric and imperial units
  double convertWeight(double weight, UnitPreference from, UnitPreference to) {
    if (from == to) return weight;

    if (from == UnitPreference.metric && to == UnitPreference.imperial) {
      // kg to lbs
      return weight * 2.20462;
    } else if (from == UnitPreference.imperial && to == UnitPreference.metric) {
      // lbs to kg
      return weight / 2.20462;
    }

    return weight;
  }

  /// Converts height between metric and imperial units
  double convertHeight(double height, UnitPreference from, UnitPreference to) {
    if (from == to) return height;

    if (from == UnitPreference.metric && to == UnitPreference.imperial) {
      // cm to inches
      return height / 2.54;
    } else if (from == UnitPreference.imperial && to == UnitPreference.metric) {
      // inches to cm
      return height * 2.54;
    }

    return height;
  }

  /// Determines if onboarding is complete based on required fields
  bool isOnboardingComplete(AsvsUserProfile profile) {
    final hasBasicInfo = profile.firstName != null &&
        profile.firstName!.isNotEmpty &&
        profile.lastName != null &&
        profile.lastName!.isNotEmpty &&
        profile.dateOfBirth != null &&
        profile.gender != null;

    final hasPhysicalMeasurements =
        profile.heightCm != null && profile.weightKg != null;

    const hasUnitPreference = true; // Always has default value

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Checking onboarding completion',
        'Basic: $hasBasicInfo, Physical: $hasPhysicalMeasurements, '
            'Units: $hasUnitPreference',
      ),
    );

    return hasBasicInfo && hasPhysicalMeasurements && hasUnitPreference;
  }

  /// Creates a new profile with calculated fields
  AsvsUserProfile createProfileWithCalculations(AsvsUserProfile profile) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Creating profile with calculations',
      ),
    );

    final now = DateTime.now();
    final calculatedBmi = calculateBmi(profile.heightCm, profile.weightKg);
    final isComplete = isOnboardingComplete(profile);

    return profile.copyWith(
      bmi: calculatedBmi,
      createdAt: profile.createdAt ?? now,
      updatedAt: now,
      isOnboardingComplete: isComplete,
    );
  }

  /// Gets the next required step in the onboarding process
  OnboardingStep getNextStep(AsvsUserProfile profile) {
    if (!profile.hasBasicInfo) {
      return OnboardingStep.personalInfo;
    }
    if (!profile.hasPhysicalMeasurements) {
      return OnboardingStep.physicalMeasurements;
    }
    if (!profile.hasHealthInfo) {
      return OnboardingStep.healthInfo;
    }
    if (!profile.hasAsvsRatings) {
      return OnboardingStep.asvsRatings;
    }
    return OnboardingStep.complete;
  }

  /// Gets health risk assessment based on profile data
  HealthRiskAssessment assessHealthRisk(AsvsUserProfile profile) {
    var riskScore = 0;
    final factors = <String>[];

    // Age factor
    final age = profile.age;
    if (age != null) {
      if (age > 65) {
        riskScore += 2;
        factors.add('Advanced age');
      } else if (age > 45) {
        riskScore += 1;
        factors.add('Middle age');
      }
    }

    // BMI factor
    final bmi = profile.bmi ?? profile.calculatedBmi;
    if (bmi != null) {
      if (bmi >= 30) {
        riskScore += 3;
        factors.add('Obesity');
      } else if (bmi >= 25) {
        riskScore += 1;
        factors.add('Overweight');
      } else if (bmi < 18.5) {
        riskScore += 1;
        factors.add('Underweight');
      }
    }

    // Smoking factor
    if (profile.smokingStatus != null) {
      switch (profile.smokingStatus!) {
        case SmokingStatus.current:
          riskScore += 3;
          factors.add('Current smoker');
        case SmokingStatus.occasional:
          riskScore += 2;
          factors.add('Occasional smoker');
        case SmokingStatus.former:
          riskScore += 1;
          factors.add('Former smoker');
        case SmokingStatus.never:
          break;
      }
    }

    // Exercise factor
    if (profile.exerciseFrequency != null && profile.exerciseFrequency! < 2) {
      riskScore += 2;
      factors.add('Insufficient exercise');
    }

    // Sleep factor
    if (profile.sleepHours != null) {
      if (profile.sleepHours! < 6 || profile.sleepHours! > 9) {
        riskScore += 1;
        factors.add('Poor sleep duration');
      }
    }

    // Stress factor
    if (profile.stressLevel != null && profile.stressLevel! >= 7) {
      riskScore += 2;
      factors.add('High stress level');
    }

    HealthRiskLevel riskLevel;
    if (riskScore <= 2) {
      riskLevel = HealthRiskLevel.low;
    } else if (riskScore <= 5) {
      riskLevel = HealthRiskLevel.moderate;
    } else if (riskScore <= 8) {
      riskLevel = HealthRiskLevel.high;
    } else {
      riskLevel = HealthRiskLevel.veryHigh;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.onboardingModule,
        'Health risk assessment completed',
        'Score: $riskScore, Level: ${riskLevel.name}, '
            'Factors: ${factors.length}',
      ),
    );

    return HealthRiskAssessment(
      riskLevel: riskLevel,
      riskScore: riskScore,
      riskFactors: factors,
    );
  }
}

/// Result of profile validation
class ValidationResult {
  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
}

/// Onboarding steps
enum OnboardingStep {
  personalInfo,
  physicalMeasurements,
  healthInfo,
  asvsRatings,
  complete,
}

/// Health risk levels
enum HealthRiskLevel {
  low,
  moderate,
  high,
  veryHigh,
}

/// Health risk assessment result
class HealthRiskAssessment {
  const HealthRiskAssessment({
    required this.riskLevel,
    required this.riskScore,
    required this.riskFactors,
  });

  final HealthRiskLevel riskLevel;
  final int riskScore;
  final List<String> riskFactors;
}
