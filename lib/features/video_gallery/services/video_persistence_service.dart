import 'dart:io';

import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_upload_service.dart';
import 'package:bloomg_flutter/features/video_gallery/models/firebase_video_metadata.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template video_persistence_service}
/// Service for persisting successful face verification videos to Firebase Storage.
/// Handles uploading temporary videos to Firebase Storage and cleaning up local files.
/// {@endtemplate}
class VideoPersistenceService {
  /// {@macro video_persistence_service}
  VideoPersistenceService({
    VideoUploadService? uploadService,
    AuthRepository? authRepository,
  })  : _uploadService = uploadService ?? VideoUploadService(),
        _authRepository = authRepository;

  final LoggerService _logger = LoggerService();
  final VideoUploadService _uploadService;
  final AuthRepository? _authRepository;

  /// Saves a successful face verification video to Firebase Storage
  Future<FirebaseVideoMetadata?> saveSuccessfulVideo({
    required String temporaryVideoPath,
    required FaceCoverageStats coverageStats,
    required DateTime recordingTime,
  }) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Uploading successful video to Firebase Storage',
          'Temp path: $temporaryVideoPath, Quality: '
              '${coverageStats.qualityScore.toStringAsFixed(1)}%',
        ),
      );

      // Check if the temporary video meets quality threshold
      if (!coverageStats.meetsQualityThreshold) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video does not meet quality threshold, not uploading',
            'Quality: '
                '${coverageStats.qualityScore.toStringAsFixed(1)}%',
          ),
        );
        return null;
      }

      // Ensure the temporary video exists
      final tempFile = File(temporaryVideoPath);
      if (!await tempFile.exists()) {
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.videoGalleryModule,
            LoggingConstants.recoverableError,
            'Temporary video file not found',
            'Path: $temporaryVideoPath',
          ),
        );
        return null;
      }

      // Get current user ID for upload
      final userId = _getCurrentUserId();
      if (userId == null) {
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.videoGalleryModule,
            LoggingConstants.recoverableError,
            'No authenticated user found for video upload',
            'Path: $temporaryVideoPath',
          ),
        );
        return null;
      }

      // Upload video to Firebase Storage
      final videoMetadata = await _uploadService.uploadVideo(
        videoPath: temporaryVideoPath,
        coverageStats: coverageStats,
        userId: userId,
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video uploaded successfully to Firebase Storage',
          'Document ID: ${videoMetadata.id}, URL: ${videoMetadata.downloadUrl}',
        ),
      );

      return videoMetadata;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to upload video to Firebase Storage: $error',
          'Temp path: $temporaryVideoPath',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Gets the current authenticated user ID
  String? _getCurrentUserId() {
    if (_authRepository == null) {
      // If no auth repository provided, try to get from current user
      // This is a fallback - ideally auth repository should be injected
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'No auth repository provided, cannot get user ID',
        ),
      );
      return null;
    }

    final currentUser = _authRepository.currentUser;
    if (currentUser.isEmpty) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'No authenticated user found',
        ),
      );
      return null;
    }

    return currentUser.uid;
  }

  /// Cleans up temporary video files that are no longer needed
  Future<void> cleanupTemporaryVideo(String temporaryVideoPath) async {
    try {
      final tempFile = File(temporaryVideoPath);
      if (await tempFile.exists()) {
        await tempFile.delete();

        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Temporary video cleaned up',
            'Path: $temporaryVideoPath',
          ),
        );
      }
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Failed to cleanup temporary video',
          'Path: $temporaryVideoPath, Error: $error',
        ),
      );
      // Don't rethrow - cleanup failures are not critical
    }
  }
}
