import 'package:bloomg_flutter/features/video_gallery/models/firebase_video_metadata.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// {@template firebase_video_metadata_service}
/// Service for managing video metadata in Firestore.
/// Handles CRUD operations for face verification video metadata.
/// {@endtemplate}
class FirebaseVideoMetadataService {
  /// {@macro firebase_video_metadata_service}
  FirebaseVideoMetadataService({
    FirebaseFirestore? firestore,
  }) : _firestore = firestore ?? FirebaseFirestore.instance;

  final FirebaseFirestore _firestore;
  final LoggerService _logger = LoggerService();

  /// Collection name for video metadata
  static const String _collectionName = 'face_verification_videos';

  /// Gets the collection reference for video metadata
  CollectionReference<Map<String, dynamic>> get _collection =>
      _firestore.collection(_collectionName);

  /// Saves video metadata to Firestore
  Future<String> saveVideoMetadata(FirebaseVideoMetadata metadata) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Saving video metadata to Firestore',
          'User: ${metadata.userId}, File: ${metadata.fileName}',
        ),
      );

      final docRef = await _collection.add(metadata.toFirestore());

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video metadata saved successfully',
          'Document ID: ${docRef.id}, User: ${metadata.userId}',
        ),
      );

      return docRef.id;
    } catch (error, stackTrace) {
      // Enhanced error handling with specific error types
      String errorMessage;
      String errorType;

      if (error.toString().contains('permission-denied')) {
        errorType = 'Permission Denied';
        errorMessage = 'User ${metadata.userId} does not have permission to '
            'write to face_verification_videos collection. Check Firestore '
            'security rules and user authentication.';
      } else if (error.toString().contains('unauthenticated')) {
        errorType = 'Authentication Error';
        errorMessage = 'User is not authenticated. Please ensure Firebase '
            'Auth is properly configured and user is signed in.';
      } else if (error.toString().contains('invalid-argument')) {
        errorType = 'Invalid Data';
        errorMessage = 'Video metadata validation failed. Check that all '
            'required fields are present and properly formatted.';
      } else if (error.toString().contains('unavailable')) {
        errorType = 'Service Unavailable';
        errorMessage = 'Firestore service is temporarily unavailable. '
            'Please try again later.';
      } else {
        errorType = 'Unknown Error';
        errorMessage = 'Failed to save video metadata: $error';
      }

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.criticalError,
          '$errorType - $errorMessage',
          'User: ${metadata.userId}, File: ${metadata.fileName}',
        ),
        error,
        stackTrace,
      );

      // Throw a more descriptive error
      throw Exception('$errorType: $errorMessage');
    }
  }

  /// Updates existing video metadata in Firestore
  Future<void> updateVideoMetadata(
    String documentId,
    Map<String, dynamic> updates,
  ) async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Updating video metadata',
          'Document ID: $documentId, Updates: ${updates.keys.join(', ')}',
        ),
      );

      await _collection.doc(documentId).update(updates);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video metadata updated successfully',
          'Document ID: $documentId',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to update video metadata: $error',
          'Document ID: $documentId',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Gets all video metadata for a specific user
  Future<List<FirebaseVideoMetadata>> getUserVideos(String userId) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Fetching user videos from Firestore',
          'User ID: $userId',
        ),
      );

      final querySnapshot = await _collection
          .where('userId', isEqualTo: userId)
          .orderBy('uploadTimestamp', descending: true)
          .get();

      final videos =
          querySnapshot.docs.map(FirebaseVideoMetadata.fromFirestore).toList();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'User videos fetched successfully',
          'User ID: $userId, Count: ${videos.length}',
        ),
      );

      return videos;
    } catch (error, stackTrace) {
      // Enhanced error handling for query-specific issues
      String errorMessage;
      String errorType;

      if (error.toString().contains('failed-precondition') ||
          error.toString().contains('requires an index')) {
        errorType = 'Missing Index';
        errorMessage = 'Query requires a composite index for userId and '
            'uploadTimestamp fields. Please create the index in Firebase '
            'Console: https://console.firebase.google.com/project/ '
            'bloomg-flutter/firestore/indexes';
      } else if (error.toString().contains('permission-denied')) {
        errorType = 'Permission Denied';
        errorMessage = 'User $userId does not have permission to read '
            'face_verification_videos collection. Check Firestore security '
            'rules and user authentication.';
      } else if (error.toString().contains('unauthenticated')) {
        errorType = 'Authentication Error';
        errorMessage = 'User is not authenticated. Please ensure Firebase '
            'Auth is properly configured and user is signed in.';
      } else {
        errorType = 'Query Error';
        errorMessage = 'Failed to fetch user videos: $error';
      }

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          '$errorType - $errorMessage',
          'User ID: $userId',
        ),
        error,
        stackTrace,
      );

      // Throw a more descriptive error
      throw Exception('$errorType: $errorMessage');
    }
  }

  /// Gets a specific video metadata by document ID
  Future<FirebaseVideoMetadata?> getVideoMetadata(String documentId) async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Fetching video metadata',
          'Document ID: $documentId',
        ),
      );

      final docSnapshot = await _collection.doc(documentId).get();

      if (!docSnapshot.exists) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video metadata not found',
            'Document ID: $documentId',
          ),
        );
        return null;
      }

      final metadata = FirebaseVideoMetadata.fromFirestore(docSnapshot);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video metadata fetched successfully',
          'Document ID: $documentId',
        ),
      );

      return metadata;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to fetch video metadata: $error',
          'Document ID: $documentId',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Deletes video metadata from Firestore
  Future<void> deleteVideoMetadata(String documentId) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Deleting video metadata',
          'Document ID: $documentId',
        ),
      );

      await _collection.doc(documentId).delete();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video metadata deleted successfully',
          'Document ID: $documentId',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to delete video metadata: $error',
          'Document ID: $documentId',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Gets a stream of user videos for real-time updates
  Stream<List<FirebaseVideoMetadata>> getUserVideosStream(String userId) {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Creating user videos stream',
          'User ID: $userId',
        ),
      );

      return _collection
          .where('userId', isEqualTo: userId)
          .orderBy('uploadTimestamp', descending: true)
          .snapshots()
          .map((querySnapshot) {
        return querySnapshot.docs
            .map(FirebaseVideoMetadata.fromFirestore)
            .toList();
      });
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to create user videos stream: $error',
          'User ID: $userId',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }
}
