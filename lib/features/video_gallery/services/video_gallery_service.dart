import 'dart:io';

import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_upload_service.dart';
import 'package:bloomg_flutter/features/video_gallery/models/firebase_video_metadata.dart';
import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/services/firebase_video_metadata_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

/// {@template video_gallery_service}
/// Service for managing the video gallery operations.
/// Handles video file discovery, thumbnail generation, and file management.
/// Now integrated with Firebase Storage and Firestore for cloud-based video
/// management.
/// {@endtemplate}
class VideoGalleryService {
  /// {@macro video_gallery_service}
  VideoGalleryService({
    FirebaseVideoMetadataService? metadataService,
    VideoUploadService? uploadService,
    AuthRepository? authRepository,
  })  : _metadataService = metadataService ?? FirebaseVideoMetadataService(),
        _uploadService = uploadService ?? VideoUploadService(),
        _authRepository = authRepository;

  final LoggerService _logger = LoggerService();
  final FirebaseVideoMetadataService _metadataService;
  final VideoUploadService _uploadService;
  final AuthRepository? _authRepository;

  /// Cache for video items to improve performance
  final Map<String, List<VideoItem>> _videoCache = {};

  /// Cache expiry time (5 minutes)
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Last cache update time
  DateTime? _lastCacheUpdate;

  /// Gets all saved face verification videos from Firebase
  Future<List<VideoItem>> getVideos() async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Loading videos from Firebase',
        ),
      );

      // Get current user ID
      final userId = _getCurrentUserId();
      if (userId == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'No authenticated user found',
          ),
        );
        return [];
      }

      // Check cache first
      if (_isCacheValid(userId)) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Returning cached videos',
            'User: $userId, Count: ${_videoCache[userId]?.length ?? 0}',
          ),
        );
        return _videoCache[userId] ?? [];
      }

      // Fetch video metadata from Firestore
      final firebaseVideos = await _metadataService.getUserVideos(userId);

      // Convert Firebase metadata to VideoItem objects
      final videos = <VideoItem>[];
      for (final firebaseVideo in firebaseVideos) {
        try {
          final videoItem =
              await _convertFirebaseVideoToVideoItem(firebaseVideo);
          videos.add(videoItem);
        } catch (error) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.videoGalleryModule,
              'Failed to convert Firebase video to VideoItem',
              'Video ID: ${firebaseVideo.id}, Error: $error',
            ),
          );
        }
      }

      // Update cache
      _videoCache[userId] = videos;
      _lastCacheUpdate = DateTime.now();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Videos loaded successfully from Firebase',
          'User: $userId, Count: ${videos.length}',
        ),
      );

      return videos;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to load videos from Firebase: $error',
        ),
        error,
        stackTrace,
      );
      return [];
    }
  }

  /// Filters and sorts videos based on the provided filter
  List<VideoItem> filterVideos(List<VideoItem> videos, GalleryFilter filter) {
    var filteredVideos = videos.toList();

    // Apply quality filter
    if (filter.minQualityScore != null) {
      filteredVideos = filteredVideos
          .where((video) => video.qualityScore >= filter.minQualityScore!)
          .toList();
    }

    // Apply date range filter
    if (filter.dateRange != null) {
      filteredVideos = filteredVideos
          .where((video) => filter.dateRange!.contains(video.createdAt))
          .toList();
    }

    // Apply sorting
    switch (filter.sortBy) {
      case GallerySortBy.dateDescending:
        filteredVideos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      case GallerySortBy.dateAscending:
        filteredVideos.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      case GallerySortBy.qualityDescending:
        filteredVideos.sort((a, b) => b.qualityScore.compareTo(a.qualityScore));
      case GallerySortBy.qualityAscending:
        filteredVideos.sort((a, b) => a.qualityScore.compareTo(b.qualityScore));
      case GallerySortBy.sizeDescending:
        filteredVideos.sort((a, b) => b.fileSize.compareTo(a.fileSize));
      case GallerySortBy.sizeAscending:
        filteredVideos.sort((a, b) => a.fileSize.compareTo(b.fileSize));
    }

    return filteredVideos;
  }

  /// Deletes a video from Firebase Storage and Firestore
  Future<bool> deleteVideo(VideoItem video) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Deleting video from Firebase',
          'ID: ${video.id}',
        ),
      );

      // Get current user ID
      final userId = _getCurrentUserId();
      if (userId == null) {
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.videoGalleryModule,
            LoggingConstants.recoverableError,
            'No authenticated user found for video deletion',
            'Video ID: ${video.id}',
          ),
        );
        return false;
      }

      // Get the Firebase video metadata using the video ID
      final firebaseVideo = await _metadataService.getVideoMetadata(video.id);
      if (firebaseVideo == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Firebase video metadata not found',
            'Video ID: ${video.id}',
          ),
        );
        return false;
      }

      // Delete from Firebase Storage and Firestore using upload service
      await _uploadService.deleteVideo(firebaseVideo);

      // Clear cache for this user
      _videoCache.remove(userId);
      _lastCacheUpdate = null;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video deleted successfully from Firebase',
          'ID: ${video.id}',
        ),
      );

      return true;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to delete video from Firebase: $error',
          'ID: ${video.id}',
        ),
        error,
        stackTrace,
      );
      return false;
    }
  }

  /// Generates a thumbnail for a Firebase video
  Future<String?> generateThumbnail(VideoItem video) async {
    try {
      // For Firebase videos, the thumbnail URL should already be available
      // If not, we can generate one from the video URL
      if (video.thumbnailPath != null && video.thumbnailPath!.isNotEmpty) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Using existing thumbnail URL',
            'Video: ${video.id}, Thumbnail: ${video.thumbnailPath}',
          ),
        );
        return video.thumbnailPath;
      }

      // For Firebase videos, we use the video download URL to generate
      // thumbnails
      // Note: This requires the video URL to be accessible
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: video.filePath, // This should be the Firebase download URL
        thumbnailPath: await _getThumbnailDirectory(),
        imageFormat: ImageFormat.JPEG,
        maxHeight: 200,
        quality: 75,
      );

      if (thumbnailPath != null) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Thumbnail generated from Firebase video',
            'Video: ${video.id}, Thumbnail: $thumbnailPath',
          ),
        );
      }

      return thumbnailPath;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to generate thumbnail from Firebase video: $error',
          'Video: ${video.id}',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Gets storage statistics from Firebase
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final videos = await getVideos();
      final totalSize =
          videos.fold<int>(0, (sum, video) => sum + video.fileSize);
      final averageQuality = videos.isEmpty
          ? 0.0
          : videos.fold<double>(0, (sum, video) => sum + video.qualityScore) /
              videos.length;

      return {
        'totalVideos': videos.length,
        'totalSize': totalSize,
        'averageQuality': averageQuality,
        'oldestVideo': videos.isEmpty
            ? null
            : videos
                .reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b)
                .createdAt,
        'newestVideo': videos.isEmpty
            ? null
            : videos
                .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b)
                .createdAt,
      };
    } catch (error) {
      return {
        'totalVideos': 0,
        'totalSize': 0,
        'averageQuality': 0.0,
        'oldestVideo': null,
        'newestVideo': null,
      };
    }
  }

  /// Gets the current authenticated user ID
  String? _getCurrentUserId() {
    if (_authRepository == null) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'No auth repository provided, cannot get user ID',
        ),
      );
      return null;
    }

    final currentUser = _authRepository.currentUser;
    if (currentUser.isEmpty) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'No authenticated user found',
        ),
      );
      return null;
    }

    return currentUser.uid;
  }

  /// Checks if the cache is valid for the given user
  bool _isCacheValid(String userId) {
    if (_lastCacheUpdate == null || !_videoCache.containsKey(userId)) {
      return false;
    }

    final now = DateTime.now();
    final cacheAge = now.difference(_lastCacheUpdate!);
    return cacheAge < _cacheExpiry;
  }

  /// Converts FirebaseVideoMetadata to VideoItem
  Future<VideoItem> _convertFirebaseVideoToVideoItem(
    FirebaseVideoMetadata firebaseVideo,
  ) async {
    return VideoItem(
      id: firebaseVideo.id,
      filePath: firebaseVideo.downloadUrl, // Use download URL as file path
      fileName: firebaseVideo.fileName,
      createdAt: firebaseVideo.uploadTimestamp,
      duration: firebaseVideo.duration,
      fileSize: firebaseVideo.fileSize,
      qualityScore: firebaseVideo.qualityScore,
      thumbnailPath: firebaseVideo.thumbnailUrl, // Use thumbnail URL
    );
  }

  /// Clears the video cache for all users
  void clearCache() {
    _videoCache.clear();
    _lastCacheUpdate = null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Video cache cleared',
      ),
    );
  }

  /// Clears the video cache for a specific user
  void clearUserCache(String userId) {
    _videoCache.remove(userId);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'User video cache cleared',
        'User: $userId',
      ),
    );
  }

  /// Gets the thumbnail directory
  Future<String> _getThumbnailDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final thumbnailDir =
        Directory('${appDir.path}/face_verification_thumbnails');
    if (!thumbnailDir.existsSync()) {
      thumbnailDir.createSync(recursive: true);
    }
    return thumbnailDir.path;
  }
}
