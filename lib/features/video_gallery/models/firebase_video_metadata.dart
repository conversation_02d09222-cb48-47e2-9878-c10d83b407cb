import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// {@template firebase_video_metadata}
/// Metadata for face verification videos stored in Firestore.
/// Contains all necessary information for video management and display.
/// {@endtemplate}
class FirebaseVideoMetadata extends Equatable {
  /// {@macro firebase_video_metadata}
  const FirebaseVideoMetadata({
    required this.id,
    required this.userId,
    required this.fileName,
    required this.downloadUrl,
    required this.storagePath,
    required this.uploadTimestamp,
    required this.fileSize,
    required this.duration,
    required this.qualityScore,
    required this.faceCoverageStats,
    this.thumbnailUrl,
    this.thumbnailStoragePath,
    this.isProcessed = true,
    this.processingError,
  });

  /// Creates FirebaseVideoMetadata from Firestore document
  factory FirebaseVideoMetadata.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data()!;
    
    return FirebaseVideoMetadata(
      id: doc.id,
      userId: data['userId'] as String,
      fileName: data['fileName'] as String,
      downloadUrl: data['downloadUrl'] as String,
      storagePath: data['storagePath'] as String,
      uploadTimestamp: (data['uploadTimestamp'] as Timestamp).toDate(),
      fileSize: data['fileSize'] as int,
      duration: Duration(seconds: data['durationSeconds'] as int),
      qualityScore: (data['qualityScore'] as num).toDouble(),
      faceCoverageStats: FaceCoverageStats.fromJson(
        data['faceCoverageStats'] as Map<String, dynamic>,
      ),
      thumbnailUrl: data['thumbnailUrl'] as String?,
      thumbnailStoragePath: data['thumbnailStoragePath'] as String?,
      isProcessed: data['isProcessed'] as bool? ?? true,
      processingError: data['processingError'] as String?,
    );
  }

  /// Unique identifier for the video document
  final String id;

  /// User ID who uploaded the video
  final String userId;

  /// Original filename of the video
  final String fileName;

  /// Firebase Storage download URL for the video
  final String downloadUrl;

  /// Firebase Storage path for the video file
  final String storagePath;

  /// When the video was uploaded to Firebase
  final DateTime uploadTimestamp;

  /// File size in bytes
  final int fileSize;

  /// Duration of the video in seconds
  final Duration duration;

  /// Quality score from face verification (0-100)
  final double qualityScore;

  /// Detailed face coverage statistics
  final FaceCoverageStats faceCoverageStats;

  /// Firebase Storage download URL for the thumbnail (optional)
  final String? thumbnailUrl;

  /// Firebase Storage path for the thumbnail file (optional)
  final String? thumbnailStoragePath;

  /// Whether the video has been fully processed
  final bool isProcessed;

  /// Error message if processing failed
  final String? processingError;

  /// Creates a copy with updated fields
  FirebaseVideoMetadata copyWith({
    String? id,
    String? userId,
    String? fileName,
    String? downloadUrl,
    String? storagePath,
    DateTime? uploadTimestamp,
    int? fileSize,
    Duration? duration,
    double? qualityScore,
    FaceCoverageStats? faceCoverageStats,
    String? thumbnailUrl,
    String? thumbnailStoragePath,
    bool? isProcessed,
    String? processingError,
  }) {
    return FirebaseVideoMetadata(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fileName: fileName ?? this.fileName,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      storagePath: storagePath ?? this.storagePath,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      fileSize: fileSize ?? this.fileSize,
      duration: duration ?? this.duration,
      qualityScore: qualityScore ?? this.qualityScore,
      faceCoverageStats: faceCoverageStats ?? this.faceCoverageStats,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      thumbnailStoragePath: thumbnailStoragePath ?? this.thumbnailStoragePath,
      isProcessed: isProcessed ?? this.isProcessed,
      processingError: processingError ?? this.processingError,
    );
  }

  /// Converts to Firestore document data
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'fileName': fileName,
      'downloadUrl': downloadUrl,
      'storagePath': storagePath,
      'uploadTimestamp': Timestamp.fromDate(uploadTimestamp),
      'fileSize': fileSize,
      'durationSeconds': duration.inSeconds,
      'qualityScore': qualityScore,
      'faceCoverageStats': faceCoverageStats.toJson(),
      'thumbnailUrl': thumbnailUrl,
      'thumbnailStoragePath': thumbnailStoragePath,
      'isProcessed': isProcessed,
      'processingError': processingError,
    };
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        fileName,
        downloadUrl,
        storagePath,
        uploadTimestamp,
        fileSize,
        duration,
        qualityScore,
        faceCoverageStats,
        thumbnailUrl,
        thumbnailStoragePath,
        isProcessed,
        processingError,
      ];

  @override
  String toString() => 'FirebaseVideoMetadata('
      'id: $id, '
      'userId: $userId, '
      'fileName: $fileName, '
      'qualityScore: $qualityScore, '
      'uploadTimestamp: $uploadTimestamp'
      ')';
}
