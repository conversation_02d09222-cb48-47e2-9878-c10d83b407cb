import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

/// {@template video_player_widget}
/// A widget that displays a video player with controls for Firebase Storage
/// videos.
/// {@endtemplate}
class VideoPlayerWidget extends StatefulWidget {
  /// {@macro video_player_widget}
  const VideoPlayerWidget({
    required this.video,
    super.key,
  });

  /// The video item to play
  final VideoItem video;

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  final LoggerService _logger = LoggerService();
  bool _isInitialized = false;
  bool _hasError = false;
  String? _errorMessage;
  bool _showControls = true;
  int _retryCount = 0;
  static const int _maxRetries = 3;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  @override
  void dispose() {
    _controller.dispose();
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Video player controller disposed',
        'Video ID: ${widget.video.id}',
      ),
    );
    super.dispose();
  }

  /// Initializes the video player with the Firebase Storage URL
  Future<void> _initializeVideoPlayer() async {
    if (_isRetrying) return; // Prevent multiple simultaneous retries

    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Initializing video player',
          'Video ID: ${widget.video.id}, URL: ${widget.video.filePath}, '
              'Retry: $_retryCount/$_maxRetries',
        ),
      );

      // Validate video URL format
      final videoUrl = widget.video.filePath;
      if (!_isValidVideoUrl(videoUrl)) {
        throw Exception('Invalid video URL format: $videoUrl');
      }

      // Create controller with Firebase Storage URL and platform-specific
      // config
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl),
        httpHeaders: _getVideoHeaders(),
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: true,
        ),
      );

      // Initialize the controller with timeout
      await _controller.initialize().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Video initialization timeout after 30 seconds');
        },
      );

      // Set up listeners
      _controller.addListener(_onVideoPlayerStateChanged);

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _hasError = false;
          _errorMessage = null;
          _retryCount = 0; // Reset retry count on success
        });

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video player initialized successfully',
            'Video ID: ${widget.video.id}, '
                'Duration: ${_controller.value.duration}',
          ),
        );
      }
    } catch (error, stackTrace) {
      final errorMessage = _getDetailedErrorMessage(error);

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to initialize video player: $error',
          'Video ID: ${widget.video.id}, Retry: $_retryCount/$_maxRetries',
        ),
        error,
        stackTrace,
      );

      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = errorMessage;
        });
      }
    }
  }

  /// Handles video player state changes
  void _onVideoPlayerStateChanged() {
    if (!mounted) return;

    // Handle errors
    if (_controller.value.hasError) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Video player error: ${_controller.value.errorDescription}',
          'Video ID: ${widget.video.id}',
        ),
      );

      setState(() {
        _hasError = true;
        _errorMessage =
            _controller.value.errorDescription ?? 'Unknown video error';
      });
    }
  }

  /// Toggles play/pause state
  void _togglePlayPause() {
    if (!_isInitialized || _hasError) return;

    if (_controller.value.isPlaying) {
      _controller.pause();
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video paused',
          'Video ID: ${widget.video.id}',
        ),
      );
    } else {
      _controller.play();
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Video playing',
          'Video ID: ${widget.video.id}',
        ),
      );
    }
  }

  /// Toggles controls visibility
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.black,
      child: Stack(
        children: [
          // Video player or loading/error state
          Center(
            child: _buildVideoContent(),
          ),

          // Controls overlay
          if (_showControls && _isInitialized && !_hasError)
            _buildControlsOverlay(),

          // Tap detector for showing/hiding controls
          if (_isInitialized && !_hasError)
            Positioned.fill(
              child: GestureDetector(
                onTap: _toggleControls,
                behavior: HitTestBehavior.translucent,
              ),
            ),
        ],
      ),
    );
  }

  /// Builds the main video content area
  Widget _buildVideoContent() {
    if (_hasError) {
      return _buildErrorWidget();
    }

    if (!_isInitialized) {
      return _buildLoadingWidget();
    }

    return AspectRatio(
      aspectRatio: _controller.value.aspectRatio,
      child: VideoPlayer(_controller),
    );
  }

  /// Builds the loading widget
  Widget _buildLoadingWidget() {
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          color: AppColors.primary,
        ),
        SizedBox(height: 16),
        Text(
          'Loading video...',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  /// Builds the error widget
  Widget _buildErrorWidget() {
    final canRetry = _retryCount < _maxRetries && !_isRetrying;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          Icons.error_outline,
          color: Colors.red,
          size: 64,
        ),
        const SizedBox(height: 16),
        const Text(
          'Failed to load video',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            _errorMessage ?? 'Unknown error occurred',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        if (_retryCount > 0) ...[
          const SizedBox(height: 8),
          Text(
            'Retry attempt: $_retryCount/$_maxRetries',
            style: const TextStyle(
              color: Colors.white60,
              fontSize: 12,
            ),
          ),
        ],
        const SizedBox(height: 24),
        if (_isRetrying)
          const Column(
            children: [
              CircularProgressIndicator(
                color: AppColors.primary,
              ),
              SizedBox(height: 8),
              Text(
                'Retrying...',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          )
        else if (canRetry)
          ElevatedButton(
            onPressed: _retryInitialization,
            child: const Text('Retry'),
          )
        else
          Column(
            children: [
              ElevatedButton(
                onPressed: () {
                  // Reset retry count and try again
                  _retryCount = 0;
                  setState(() {
                    _hasError = false;
                    _isInitialized = false;
                  });
                  _initializeVideoPlayer();
                },
                child: const Text('Try Again'),
              ),
              const SizedBox(height: 8),
              const Text(
                'Maximum retries reached',
                style: TextStyle(
                  color: Colors.white60,
                  fontSize: 12,
                ),
              ),
            ],
          ),
      ],
    );
  }

  /// Builds the controls overlay
  Widget _buildControlsOverlay() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withValues(alpha: 0.8),
              Colors.transparent,
            ],
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Progress bar
            VideoProgressIndicator(
              _controller,
              allowScrubbing: true,
              colors: const VideoProgressColors(
                playedColor: AppColors.primary,
                bufferedColor: Colors.white30,
                backgroundColor: Colors.white10,
              ),
            ),
            const SizedBox(height: 8),

            // Controls row
            Row(
              children: [
                // Play/pause button
                IconButton(
                  onPressed: _togglePlayPause,
                  icon: Icon(
                    _controller.value.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),

                // Time display
                Expanded(
                  child: Text(
                    '${_formatDuration(_controller.value.position)} / ${_formatDuration(_controller.value.duration)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // Placeholder for future controls (volume, fullscreen, etc.)
                const SizedBox(width: 48),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Formats duration for display
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }

  /// Validates if the video URL is in a valid format
  bool _isValidVideoUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// Gets HTTP headers for video requests
  Map<String, String> _getVideoHeaders() {
    return {
      'Accept': 'video/mp4,video/*;q=0.9,*/*;q=0.8',
      'Accept-Encoding': 'identity',
      'Range': 'bytes=0-',
      'User-Agent': 'Flutter Video Player',
    };
  }

  /// Gets detailed error message based on error type
  String _getDetailedErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout')) {
      return 'Video loading timed out. Please check your connection.';
    } else if (errorString.contains('network') ||
        errorString.contains('connection')) {
      return 'Network error. Please check your internet connection.';
    } else if (errorString.contains('format') ||
        errorString.contains('codec') ||
        errorString.contains('damaged')) {
      return 'Video format not supported or file is corrupted.';
    } else if (errorString.contains('permission') ||
        errorString.contains('unauthorized')) {
      return 'Access denied. Please check video permissions.';
    } else if (errorString.contains('not found') ||
        errorString.contains('404')) {
      return 'Video not found. It may have been deleted.';
    } else {
      return 'Failed to load video. Please try again.';
    }
  }

  /// Retries video initialization with exponential backoff
  Future<void> _retryInitialization() async {
    if (_retryCount >= _maxRetries || _isRetrying) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Maximum retry attempts reached',
          'Video ID: ${widget.video.id}, Retries: $_retryCount',
        ),
      );
      return;
    }

    setState(() {
      _isRetrying = true;
      _hasError = false;
    });

    // Exponential backoff: 1s, 2s, 4s
    final delaySeconds = (1 << _retryCount).clamp(1, 8);
    await Future<void>.delayed(Duration(seconds: delaySeconds));

    _retryCount++;

    try {
      await _initializeVideoPlayer();
    } finally {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    }
  }
}
