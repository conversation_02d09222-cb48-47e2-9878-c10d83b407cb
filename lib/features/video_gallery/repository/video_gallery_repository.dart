import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template video_gallery_repository}
/// Repository for managing video gallery data operations.
/// Acts as an abstraction layer between the BLoC and the service layer.
/// {@endtemplate}
class VideoGalleryRepository {
  /// {@macro video_gallery_repository}
  VideoGalleryRepository({
    required VideoGalleryService videoGalleryService,
  }) : _videoGalleryService = videoGalleryService;

  final VideoGalleryService _videoGalleryService;
  final LoggerService _logger = LoggerService();

  /// Loads all videos from storage
  Future<List<VideoItem>> loadVideos() async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Loading videos from repository',
        ),
      );

      final videos = await _videoGalleryService.getVideos();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Videos loaded from repository',
          'Count: ${videos.length}',
        ),
      );

      return videos;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to load videos from repository: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Filters and sorts videos based on the provided filter
  List<VideoItem> filterVideos(List<VideoItem> videos, GalleryFilter filter) {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Filtering videos',
          'Filter: $filter, Input count: ${videos.length}',
        ),
      );

      final filteredVideos = _videoGalleryService.filterVideos(videos, filter);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Videos filtered',
          'Output count: ${filteredVideos.length}',
        ),
      );

      return filteredVideos;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to filter videos: $error',
        ),
        error,
        stackTrace,
      );
      // Return original list on error
      return videos;
    }
  }

  /// Deletes a video from storage
  Future<bool> deleteVideo(VideoItem video) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Deleting video from repository',
          'ID: ${video.id}',
        ),
      );

      final success = await _videoGalleryService.deleteVideo(video);

      if (success) {
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video deleted from repository',
            'ID: ${video.id}',
          ),
        );
      } else {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video deletion failed',
            'ID: ${video.id}',
          ),
        );
      }

      return success;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to delete video from repository: $error',
          'ID: ${video.id}',
        ),
        error,
        stackTrace,
      );
      return false;
    }
  }

  /// Generates a thumbnail for a video
  Future<VideoItem?> generateThumbnail(VideoItem video) async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Generating thumbnail',
          'Video ID: ${video.id}',
        ),
      );

      final thumbnailPath = await _videoGalleryService.generateThumbnail(video);

      if (thumbnailPath != null) {
        final updatedVideo = video.copyWith(thumbnailPath: thumbnailPath);

        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Thumbnail generated successfully',
            'Video ID: ${video.id}',
          ),
        );

        return updatedVideo;
      }

      return null;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to generate thumbnail: $error',
          'Video ID: ${video.id}',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Gets storage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Getting storage statistics',
        ),
      );

      final stats = await _videoGalleryService.getStorageStats();

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Storage statistics retrieved',
          'Total videos: ${stats['totalVideos']}',
        ),
      );

      return stats;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.recoverableError,
          'Failed to get storage statistics: $error',
        ),
        error,
        stackTrace,
      );
      return {
        'totalVideos': 0,
        'totalSize': 0,
        'averageQuality': 0.0,
        'oldestVideo': null,
        'newestVideo': null,
      };
    }
  }

  /// Refreshes the video list by reloading from storage
  Future<List<VideoItem>> refreshVideos() async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Refreshing video list',
      ),
    );

    return loadVideos();
  }
}
