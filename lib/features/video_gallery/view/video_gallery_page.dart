import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_gallery_view.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

/// {@template video_gallery_page}
/// Page that displays the video gallery with face verification videos.
/// {@endtemplate}
class VideoGalleryPage extends StatelessWidget {
  /// {@macro video_gallery_page}
  const VideoGalleryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => VideoGalleryBloc(
        repository: getIt<VideoGalleryRepository>(),
      )..add(const LoadVideos()),
      child: const VideoGalleryView(),
    );
  }
}

/// {@template video_gallery_view}
/// The main view for the video gallery.
/// {@endtemplate}
class VideoGalleryView extends StatelessWidget {
  /// {@macro video_gallery_view}
  const VideoGalleryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Gallery'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              _navigateToHome(context);
            },
            tooltip: 'Home',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<VideoGalleryBloc>().add(const RefreshVideos());
            },
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
            tooltip: 'Filter',
          ),
        ],
      ),
      body: const VideoGalleryContent(),
    );
  }

  /// Navigates to the home screen with logging
  void _navigateToHome(BuildContext context) {
    LoggerService().info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Home navigation',
        'User navigated to home from video gallery',
      ),
    );

    context.goToHome();
  }

  /// Shows the filter dialog
  void _showFilterDialog(BuildContext context) {
    // TODO(dev): Implement filter dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Filter dialog coming soon!'),
      ),
    );
  }
}

/// {@template video_gallery_content}
/// The main content area of the video gallery.
/// {@endtemplate}
class VideoGalleryContent extends StatelessWidget {
  /// {@macro video_gallery_content}
  const VideoGalleryContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VideoGalleryBloc, VideoGalleryState>(
      builder: (context, state) {
        if (state is VideoGalleryInitial || state is VideoGalleryLoading) {
          return const GalleryLoadingWidget();
        }

        if (state is VideoGalleryError) {
          return GalleryErrorWidget(
            message: state.message,
            onRetry: () {
              context.read<VideoGalleryBloc>().add(const LoadVideos());
            },
          );
        }

        if (state is VideoGalleryEmpty) {
          return EmptyGalleryWidget(
            onCreateVideo: () {
              context.go(AppRouter.faceVerificationPath);
            },
          );
        }

        // Show the video gallery grid
        return const VideoGalleryGrid();
      },
    );
  }
}

/// {@template video_gallery_grid}
/// Grid view displaying video thumbnails.
/// {@endtemplate}
class VideoGalleryGrid extends StatelessWidget {
  /// {@macro video_gallery_grid}
  const VideoGalleryGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VideoGalleryBloc, VideoGalleryState>(
      builder: (context, state) {
        final videos = state.filteredVideos;

        if (videos.isEmpty) {
          return const EmptyGalleryWidget();
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: videos.length,
            itemBuilder: (context, index) {
              final video = videos[index];
              return VideoThumbnailWidget(
                video: video,
                onTap: () {
                  context.goToVideoPlayer(video);
                },
                onDelete: () {
                  context.read<VideoGalleryBloc>().add(DeleteVideo(video));
                },
              );
            },
          ),
        );
      },
    );
  }
}
