import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_player_widget.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template video_player_page}
/// Page that displays a video player for face verification videos.
/// {@endtemplate}
class VideoPlayerPage extends StatelessWidget {
  /// {@macro video_player_page}
  const VideoPlayerPage({
    required this.video,
    super.key,
  });

  /// The video item to play
  final VideoItem video;

  static final LoggerService _logger = LoggerService();

  @override
  Widget build(BuildContext context) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Video player page opened',
        'Video ID: ${video.id}, File: ${video.fileName}',
      ),
    );

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.videoGalleryModule,
              'Video player page closed',
              'Video ID: ${video.id}',
            ),
          );
        }
      },
      child: ResponsiveBreakpoints.builder(
        child: Scaffold(
          backgroundColor: Colors.black,
          appBar: _buildAppBar(context),
          body: _buildBody(context),
        ),
        breakpoints: const [
          Breakpoint(start: 0, end: 450, name: MOBILE),
          Breakpoint(start: 451, end: 800, name: TABLET),
          Breakpoint(start: 801, end: 1920, name: DESKTOP),
          Breakpoint(start: 1921, end: double.infinity, name: '4K'),
        ],
      ),
    );
  }

  /// Handles back navigation safely with fallback
  void _handleSafeBackNavigation(BuildContext context) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.videoGalleryModule,
        'Back button pressed',
        'Video ID: ${video.id}',
      ),
    );

    try {
      // Try to pop the current route
      GoRouter.of(context).pop();
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Navigation pop successful',
          'Video ID: ${video.id}',
        ),
      );
    } catch (error) {
      // If pop fails, navigate to video gallery directly
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Navigation pop failed, using fallback',
          'Error: $error, Video ID: ${video.id}',
        ),
      );
      context.go(AppRouter.videoGalleryPath);
    }
  }

  /// Builds the app bar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => _handleSafeBackNavigation(context),
      ),
      title: Text(
        'Video Player',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info_outline),
          onPressed: () => _showVideoInfo(context),
          tooltip: 'Video Information',
        ),
      ],
    );
  }

  /// Builds the main body content
  Widget _buildBody(BuildContext context) {
    final isDesktop = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    if (isDesktop) {
      return _buildDesktopLayout(context);
    } else {
      return _buildMobileLayout(context);
    }
  }

  /// Builds the mobile layout (video player takes full screen)
  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        // Video player area
        Expanded(
          child: VideoPlayerWidget(video: video),
        ),

        // Video metadata section
        Container(
          color: Colors.black87,
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: _buildVideoMetadata(context),
        ),
      ],
    );
  }

  /// Builds the desktop layout (video player with side panel)
  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // Video player area
        Expanded(
          flex: 3,
          child: VideoPlayerWidget(video: video),
        ),

        // Side panel with metadata
        Container(
          width: 300,
          color: Colors.grey[900],
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Video Information',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: AppDimensions.spacingL),
              Expanded(
                child: _buildVideoMetadata(context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the video metadata section
  Widget _buildVideoMetadata(BuildContext context) {
    final dateFormat = DateFormat('MMM dd, yyyy • HH:mm');
    final fileSizeMB = (video.fileSize / (1024 * 1024)).toStringAsFixed(1);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildMetadataItem(
          context,
          'File Name',
          video.fileName,
          Icons.video_file,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildMetadataItem(
          context,
          'Created',
          dateFormat.format(video.createdAt),
          Icons.access_time,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildMetadataItem(
          context,
          'Duration',
          _formatDuration(video.duration),
          Icons.timer,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildMetadataItem(
          context,
          'File Size',
          '$fileSizeMB MB',
          Icons.storage,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildQualityScoreItem(context),
      ],
    );
  }

  /// Builds a metadata item row
  Widget _buildMetadataItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Colors.white70,
          size: 20,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white70,
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the quality score item with color coding
  Widget _buildQualityScoreItem(BuildContext context) {
    final qualityColor = _getQualityColor(video.qualityScore);
    final qualityText = _getQualityText(video.qualityScore);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.star,
          color: qualityColor,
          size: 20,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Quality Score',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white70,
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Text(
                    '${video.qualityScore.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: qualityColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: qualityColor.withValues(alpha: 0.5),
                      ),
                    ),
                    child: Text(
                      qualityText,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: qualityColor,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Shows video information dialog
  void _showVideoInfo(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Video Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${video.id}'),
            const SizedBox(height: 8),
            Text('File: ${video.fileName}'),
            const SizedBox(height: 8),
            Text('Quality: ${video.qualityScore.toStringAsFixed(1)}%'),
            const SizedBox(height: 8),
            Text('Duration: ${_formatDuration(video.duration)}'),
            const SizedBox(height: 8),
            Text(
              'Size: ${(video.fileSize / (1024 * 1024)).toStringAsFixed(1)} MB',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Gets the color for quality score
  Color _getQualityColor(double score) {
    if (score >= 80) return AppColors.success;
    if (score >= 70) return Colors.teal;
    if (score >= 50) return AppColors.warning;
    return AppColors.error;
  }

  /// Gets the text description for quality score
  String _getQualityText(double score) {
    if (score >= 80) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 50) return 'Moderate';
    return 'Poor';
  }

  /// Formats duration for display
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }
}
