import 'package:bloomg_flutter/features/face_verification/services/video_upload_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';

/// {@template upload_progress_dialog}
/// Dialog widget that displays video upload progress with status updates,
/// progress indicator, and retry functionality for failed uploads.
/// {@endtemplate}
class UploadProgressDialog extends StatefulWidget {
  /// {@macro upload_progress_dialog}
  const UploadProgressDialog({
    required this.uploadStream,
    this.onSuccess,
    this.onFailure,
    this.onCancel,
    super.key,
  });

  /// Stream of upload progress updates
  final Stream<UploadProgress> uploadStream;

  /// Callback when upload completes successfully
  final VoidCallback? onSuccess;

  /// Callback when upload fails
  final void Function(String error)? onFailure;

  /// Callback when upload is cancelled
  final VoidCallback? onCancel;

  @override
  State<UploadProgressDialog> createState() => _UploadProgressDialogState();
}

class _UploadProgressDialogState extends State<UploadProgressDialog>
    with SingleTickerProviderStateMixin {
  final LoggerService _logger = LoggerService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  UploadProgress? _currentProgress;
  bool _canDismiss = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();
    _listenToUploadProgress();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _listenToUploadProgress() {
    widget.uploadStream.listen(
      (progress) {
        if (mounted) {
          setState(() {
            _currentProgress = progress;
          });

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Upload progress update',
              'Status: ${progress.status}, Progress: ${progress.progress}',
            ),
          );

          // Handle completion states
          if (progress.status == UploadStatus.completed) {
            _handleSuccess();
          } else if (progress.status == UploadStatus.failed) {
            _handleFailure(progress.error ?? 'Unknown error occurred');
          }
        }
      },
      onError: (Object error) {
        if (mounted) {
          _handleFailure(error.toString());
        }
      },
    );
  }

  void _handleSuccess() {
    setState(() {
      _canDismiss = true;
    });

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Upload completed successfully',
      ),
    );

    // Auto-dismiss after 1 second and call success callback
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        Navigator.of(context).pop();
        widget.onSuccess?.call();
      }
    });
  }

  void _handleFailure(String error) {
    setState(() {
      _canDismiss = true;
    });

    _logger.error(
      LoggingConstants.formatError(
        LoggingConstants.faceVerificationModule,
        LoggingConstants.recoverableError,
        'Upload failed: $error',
      ),
    );

    widget.onFailure?.call(error);
  }

  void _handleCancel() {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Upload cancelled by user',
      ),
    );

    Navigator.of(context).pop();
    widget.onCancel?.call();
  }

  void _handleRetry() {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Upload retry requested',
      ),
    );

    Navigator.of(context).pop();
    // The parent should handle retry by creating a new dialog
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _canDismiss,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildProgressIndicator(),
                const SizedBox(height: 24),
                _buildStatusText(),
                const SizedBox(height: 24),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = _currentProgress?.progress ?? 0.0;
    final status = _currentProgress?.status ?? UploadStatus.starting;

    return SizedBox(
      width: 80,
      height: 80,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          SizedBox(
            width: 80,
            height: 80,
            child: CircularProgressIndicator(
              value: 1,
              strokeWidth: 4,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                Colors.grey.shade300,
              ),
            ),
          ),
          // Progress circle
          SizedBox(
            width: 80,
            height: 80,
            child: CircularProgressIndicator(
              value: status == UploadStatus.failed ? 0.0 : progress,
              strokeWidth: 4,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(status),
              ),
            ),
          ),
          // Center icon or percentage
          _buildCenterContent(status, progress),
        ],
      ),
    );
  }

  Widget _buildCenterContent(UploadStatus status, double progress) {
    switch (status) {
      case UploadStatus.completed:
        return const Icon(
          Icons.check,
          color: Colors.green,
          size: 32,
        );
      case UploadStatus.failed:
        return const Icon(
          Icons.error,
          color: Colors.red,
          size: 32,
        );
      case UploadStatus.cancelled:
        return const Icon(
          Icons.close,
          color: Colors.orange,
          size: 32,
        );
      case UploadStatus.starting:
      case UploadStatus.uploading:
      case UploadStatus.savingMetadata:
        return Text(
          '${(progress * 100).toInt()}%',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        );
    }
  }

  Color _getProgressColor(UploadStatus status) {
    switch (status) {
      case UploadStatus.completed:
        return Colors.green;
      case UploadStatus.failed:
        return Colors.red;
      case UploadStatus.cancelled:
        return Colors.orange;
      case UploadStatus.starting:
      case UploadStatus.uploading:
      case UploadStatus.savingMetadata:
        return Colors.blue;
    }
  }

  Widget _buildStatusText() {
    final status = _currentProgress?.status ?? UploadStatus.starting;
    final message = _currentProgress?.message ?? 'Preparing upload...';

    return Column(
      children: [
        Text(
          _getStatusTitle(status),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          message,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getStatusTitle(UploadStatus status) {
    switch (status) {
      case UploadStatus.starting:
        return 'Preparing Upload';
      case UploadStatus.uploading:
        return 'Uploading Video';
      case UploadStatus.savingMetadata:
        return 'Saving Metadata';
      case UploadStatus.completed:
        return 'Upload Complete!';
      case UploadStatus.failed:
        return 'Upload Failed';
      case UploadStatus.cancelled:
        return 'Upload Cancelled';
    }
  }

  Widget _buildActionButtons() {
    final status = _currentProgress?.status ?? UploadStatus.starting;

    if (status == UploadStatus.completed) {
      return const SizedBox.shrink(); // Auto-dismiss, no buttons needed
    }

    if (status == UploadStatus.failed) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          TextButton(
            onPressed: _handleCancel,
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _handleRetry,
            child: const Text('Retry'),
          ),
        ],
      );
    }

    // For uploading states, show cancel button
    if (status == UploadStatus.uploading ||
        status == UploadStatus.starting ||
        status == UploadStatus.savingMetadata) {
      return TextButton(
        onPressed: _handleCancel,
        child: const Text('Cancel'),
      );
    }

    return const SizedBox.shrink();
  }
}
