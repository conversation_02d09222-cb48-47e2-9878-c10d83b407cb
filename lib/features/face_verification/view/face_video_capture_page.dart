import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/core/router/app_router.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_upload_service.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/camera_preview_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/countdown_timer_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/recording_feedback_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/result_screen_widget.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/upload_progress_dialog.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_persistence_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_framework/responsive_framework.dart';

/// {@template face_video_capture_page}
/// Main page for face verification video capture.
///
/// Provides a full-screen camera interface with face detection guidance,
/// countdown timer, recording feedback, and result screens.
/// {@endtemplate}
class FaceVideoCapturePage extends StatefulWidget {
  /// {@macro face_video_capture_page}
  const FaceVideoCapturePage({
    super.key,
    this.videoPersistenceService,
    this.videoUploadService,
    this.videoGalleryService,
  });

  /// Optional video persistence service for dependency injection.
  /// If not provided, will create a new instance.
  final VideoPersistenceService? videoPersistenceService;

  /// Optional video upload service for dependency injection.
  /// If not provided, will create a new instance.
  final VideoUploadService? videoUploadService;

  /// Optional video gallery service for dependency injection.
  /// If not provided, will create a new instance.
  final VideoGalleryService? videoGalleryService;

  @override
  State<FaceVideoCapturePage> createState() => _FaceVideoCapturePageState();
}

class _FaceVideoCapturePageState extends State<FaceVideoCapturePage>
    with WidgetsBindingObserver {
  final LoggerService _logger = LoggerService();
  late final VideoPersistenceService _videoPersistenceService;
  late final VideoUploadService _videoUploadService;
  late final VideoGalleryService _videoGalleryService;

  /// Direct reference to the BLoC instance for safe access during
  /// lifecycle events
  FaceVideoCaptureBloc? _bloc;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize services with dependency injection support
    _videoPersistenceService =
        widget.videoPersistenceService ?? VideoPersistenceService();
    _videoUploadService = widget.videoUploadService ?? VideoUploadService();
    _videoGalleryService = widget.videoGalleryService ?? VideoGalleryService();

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face video capture page initialized',
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face video capture page disposed',
      ),
    );

    // Properly dispose resources before widget disposal
    _disposeResources();

    super.dispose();
  }

  /// Handles proper resource disposal before navigation or widget disposal
  Future<void> _disposeResources() async {
    try {
      // First dispose BLoC resources
      if (_bloc != null && !_bloc!.isClosed) {
        _bloc!.add(const DisposeResources());
        await _bloc!.close();
      }
      _bloc = null;

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Resources disposed successfully',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Resource disposal error: $error',
        ),
        error,
        stackTrace,
      );
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle app lifecycle changes for camera management
    switch (state) {
      case AppLifecycleState.paused:
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'App paused - disposing camera resources',
          ),
        );
        _safelyAccessBloc((bloc) => bloc.add(const DisposeResources()));
      case AppLifecycleState.resumed:
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'App resumed - reinitializing camera',
          ),
        );
        _safelyAccessBloc((bloc) => bloc.add(const InitializeCamera()));
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // No action needed for these states
        break;
    }
  }

  /// Safely access the BLoC with proper error handling
  void _safelyAccessBloc(void Function(FaceVideoCaptureBloc) action) {
    try {
      // First try to use the stored reference
      if (_bloc != null && !_bloc!.isClosed) {
        action(_bloc!);
        return;
      }

      // Fallback to context.read if mounted and context is available
      if (mounted) {
        final bloc = context.read<FaceVideoCaptureBloc>();
        action(bloc);
        return;
      }

      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'BLoC access skipped - widget not mounted or BLoC unavailable',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Failed to access FaceVideoCaptureBloc during lifecycle change: '
          '$error',
        ),
        error,
        stackTrace,
      );
    }
  }

  /// Handles back navigation with proper resource cleanup
  Future<void> _handleBackNavigation() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Back navigation initiated',
      ),
    );

    // Navigate back immediately - let dispose handle resource cleanup
    if (mounted && context.mounted) {
      try {
        context.pop();
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Navigation pop successful',
          ),
        );
      } catch (error) {
        // If pop fails, navigate to home page directly
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Navigation pop failed, using fallback',
            'Error: $error',
          ),
        );
        context.go(AppRouter.homePath);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        // Create and store the BLoC reference for safe lifecycle access
        _bloc = getIt<FaceVideoCaptureBloc>();
        _bloc!.add(const InitializeCamera());
        return _bloc!;
      },
      child: PopScope(
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) {
            _logger.info(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'System back button pressed - navigation completed',
              ),
            );
          }
        },
        child: ResponsiveBreakpoints.builder(
          child: Scaffold(
            backgroundColor: Colors.black,
            body: SafeArea(
              child: BlocConsumer<FaceVideoCaptureBloc, FaceVideoCaptureState>(
                listener: _handleStateChanges,
                builder: _buildContent,
              ),
            ),
          ),
          breakpoints: const [
            Breakpoint(start: 0, end: 450, name: MOBILE),
            Breakpoint(start: 451, end: 800, name: TABLET),
            Breakpoint(start: 801, end: 1920, name: DESKTOP),
            Breakpoint(start: 1921, end: double.infinity, name: '4K'),
          ],
        ),
      ),
    );
  }

  /// Handles state changes and shows appropriate feedback
  void _handleStateChanges(BuildContext context, FaceVideoCaptureState state) {
    switch (state.runtimeType) {
      case Error:
        final errorState = state as Error;
        _logger.error(
          LoggingConstants.formatError(
            LoggingConstants.faceVerificationModule,
            LoggingConstants.criticalError,
            'Face capture error: ${errorState.error}',
          ),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${errorState.error}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () {
                _safelyAccessBloc((bloc) => bloc.add(const ResetCapture()));
              },
            ),
          ),
        );

      case Success:
        final successState = state as Success;
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face verification completed successfully',
            'Quality score: '
                '${successState.coverageStats.qualityScore.toStringAsFixed(1)}',
          ),
        );

      case Failure:
        final failureState = state as Failure;
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face verification failed',
            'Reason: ${failureState.reason}',
          ),
        );

      default:
        break;
    }
  }

  /// Builds the main content based on current state
  Widget _buildContent(BuildContext context, FaceVideoCaptureState state) {
    switch (state.runtimeType) {
      case Initial:
      case CameraInitializing:
        return _buildLoadingScreen();

      case CameraReady:
        return _buildCameraReadyScreen(context, state);

      case CountdownInProgress:
        final countdownState = state as CountdownInProgress;
        return _buildCountdownScreen(context, countdownState);

      case Recording:
        final recordingState = state as Recording;
        return _buildRecordingScreen(context, recordingState);

      case Processing:
        return _buildProcessingScreen();

      case Success:
        final successState = state as Success;
        return _buildSuccessScreen(context, successState);

      case Failure:
        final failureState = state as Failure;
        return _buildFailureScreen(context, failureState);

      case Error:
        final errorState = state as Error;
        return _buildErrorScreen(context, errorState);

      default:
        return _buildLoadingScreen();
    }
  }

  /// Builds loading screen
  Widget _buildLoadingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            'Initializing camera...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds camera ready screen
  Widget _buildCameraReadyScreen(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    final cameraReadyState = state as CameraReady;

    return Stack(
      children: [
        // Camera preview with interactive overlay
        CameraPreviewWidget(
          onOverlayTap: cameraReadyState.canStartRecording
              ? () {
                  _safelyAccessBloc(
                    (bloc) => bloc.add(const StartCountdown()),
                  );
                }
              : null,
        ),

        // Instructions and start button
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Column(
            children: [
              // Error message if any
              if (state.errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    state.errorMessage!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],

              // Instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getInstructionText(cameraReadyState),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),

        // Back button
        Positioned(
          top: 20,
          left: 20,
          child: IconButton(
            onPressed: _handleBackNavigation,
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ],
    );
  }

  /// Gets instruction text based on camera ready state
  String _getInstructionText(CameraReady state) {
    if (state.canStartRecording) {
      return 'Perfect! Your face is detected. '
          'Tap anywhere on the screen to start recording.';
    } else {
      final coverage = state.currentDetection?.coveragePercentage ?? 0;
      if (state.currentDetection?.faceDetected ?? false) {
        if (state.currentDetection!.faceCount > 1) {
          return 'Multiple faces detected. '
              'Please ensure only one person is visible.';
        } else if (coverage < 85) {
          return 'Move closer to the camera for deep analysis quality. '
              'Face coverage: ${coverage.toStringAsFixed(0)}% (need 80%)';
        }
      }
      return 'Position your face closer in the center guide for optimal '
          'quality. Ensure good lighting and face the camera directly.';
    }
  }

  /// Builds countdown screen
  Widget _buildCountdownScreen(
    BuildContext context,
    CountdownInProgress state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Countdown timer
        Center(
          child: CountdownTimerWidget(
            remainingSeconds: state.remainingSeconds,
          ),
        ),

        // Face validation status
        Positioned(
          bottom: 100,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              state.faceValidationStatus,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds recording screen
  Widget _buildRecordingScreen(
    BuildContext context,
    Recording state,
  ) {
    return Stack(
      children: [
        // Camera preview
        const CameraPreviewWidget(),

        // Recording feedback
        RecordingFeedbackWidget(
          elapsedTime: state.elapsedTime,
          remainingTime: state.remainingTime,
          progress: state.progress,
          currentDetection: state.currentDetection,
        ),
      ],
    );
  }

  /// Builds processing screen
  Widget _buildProcessingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            'Processing video...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds success screen
  Widget _buildSuccessScreen(BuildContext context, Success state) {
    return ResultScreenWidget(
      isSuccess: true,
      title: 'Verification Complete!',
      message: 'Your face verification video has been recorded successfully.',
      coverageStats: state.coverageStats,
      onRetry: () {
        _safelyAccessBloc((bloc) => bloc.add(const ResetCapture()));
      },
      onContinue: () => _handleSuccessfulVerification(context, state),
    );
  }

  /// Handles successful verification by uploading video with progress dialog
  Future<void> _handleSuccessfulVerification(
    BuildContext context,
    Success state,
  ) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Processing successful verification',
        'Video path: ${state.videoPath}',
      ),
    );

    // Get current user ID for upload
    final authRepository = getIt<AuthRepository>();
    final currentUser = authRepository.currentUser;

    if (currentUser.isEmpty) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'No authenticated user found for video upload',
        ),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Authentication error. Please log in again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    // Start upload with progress tracking
    await _showUploadProgressDialog(
      context,
      state.videoPath,
      state.coverageStats,
      currentUser.uid,
    );
  }

  /// Shows upload progress dialog and handles the upload process
  Future<void> _showUploadProgressDialog(
    BuildContext context,
    String videoPath,
    FaceCoverageStats coverageStats,
    String userId,
  ) async {
    // Store context references before async operations
    final goRouter = GoRouter.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Create upload stream
    final uploadStream = _videoUploadService.uploadVideoWithProgress(
      videoPath: videoPath,
      coverageStats: coverageStats,
      userId: userId,
    );

    if (!mounted) return;

    // Show upload progress dialog
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => UploadProgressDialog(
        uploadStream: uploadStream,
        onSuccess: () {
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Video upload completed successfully',
              'Navigating to video gallery',
            ),
          );

          // Clean up temporary video file
          _videoPersistenceService.cleanupTemporaryVideo(videoPath);

          // Clear video gallery cache to ensure new video appears
          _videoGalleryService.clearCache();

          // Navigate to video gallery
          if (mounted) {
            goRouter.go(AppRouter.videoGalleryPath);
          }
        },
        onFailure: (error) {
          _logger.error(
            LoggingConstants.formatError(
              LoggingConstants.faceVerificationModule,
              LoggingConstants.recoverableError,
              'Video upload failed: $error',
            ),
          );

          // Show retry option
          _showUploadFailureDialog(
            context,
            error,
            () => _showUploadProgressDialog(
              context,
              videoPath,
              coverageStats,
              userId,
            ),
          );
        },
        onCancel: () {
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Video upload cancelled by user',
            ),
          );

          // Show cancellation message and navigate to gallery
          if (mounted) {
            scaffoldMessenger.showSnackBar(
              const SnackBar(
                content: Text('Upload cancelled. Video not saved.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
            goRouter.go(AppRouter.videoGalleryPath);
          }
        },
      ),
    );
  }

  /// Shows upload failure dialog with retry option
  void _showUploadFailureDialog(
    BuildContext context,
    String error,
    VoidCallback onRetry,
  ) {
    if (!mounted) return;

    showDialog<void>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Upload Failed'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Failed to upload your video to the cloud:'),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 16),
            const Text('Would you like to try again?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              // Navigate to gallery without saving
              if (mounted) {
                GoRouter.of(context).go(AppRouter.videoGalleryPath);
              }
            },
            child: const Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              onRetry();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Builds failure screen
  Widget _buildFailureScreen(BuildContext context, Failure state) {
    return ResultScreenWidget(
      isSuccess: false,
      title: 'Verification Failed',
      message: state.reason,
      coverageStats: state.coverageStats,
      onRetry: () {
        _safelyAccessBloc((bloc) => bloc.add(const ResetCapture()));
      },
      onBack: _handleBackNavigation,
    );
  }

  /// Builds error screen
  Widget _buildErrorScreen(BuildContext context, Error state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.error,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _handleBackNavigation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Go Back'),
                ),
                ElevatedButton(
                  onPressed: () {
                    _safelyAccessBloc(
                      (bloc) => bloc.add(const InitializeCamera()),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Try Again'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
