part of 'face_video_capture_bloc.dart';

/// {@template face_video_capture_state}
/// Base class for all face video capture states.
/// {@endtemplate}
abstract class FaceVideoCaptureState extends Equatable {
  /// {@macro face_video_capture_state}
  const FaceVideoCaptureState({
    this.config = const VideoCaptureConfig(),
    this.currentDetection,
    this.coverageStats = const FaceCoverageStats.empty(),
    this.errorMessage,
  });

  /// Configuration for video capture
  final VideoCaptureConfig config;

  /// Current face detection result
  final FaceDetectionResult? currentDetection;

  /// Statistics about face coverage during recording
  final FaceCoverageStats coverageStats;

  /// Error message if any error occurred
  final String? errorMessage;

  @override
  List<Object?> get props => [
        config,
        currentDetection,
        coverageStats,
        errorMessage,
      ];
}

/// {@template initial_state}
/// Initial state when the feature is first loaded.
/// {@endtemplate}
class Initial extends FaceVideoCaptureState {
  /// {@macro initial_state}
  const Initial({
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  @override
  String toString() => 'Initial()';
}

/// {@template camera_initializing_state}
/// State when camera and face detection are being initialized.
/// {@endtemplate}
class CameraInitializing extends FaceVideoCaptureState {
  /// {@macro camera_initializing_state}
  const CameraInitializing({
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  @override
  String toString() => 'CameraInitializing()';
}

/// {@template camera_ready_state}
/// State when camera is ready and user can start recording.
/// {@endtemplate}
class CameraReady extends FaceVideoCaptureState {
  /// {@macro camera_ready_state}
  const CameraReady({
    this.canStartRecording = false,
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// Whether recording can be started based on face detection requirements
  final bool canStartRecording;

  @override
  List<Object?> get props => [
        canStartRecording,
        ...super.props,
      ];

  @override
  String toString() => 'CameraReady(canStartRecording: $canStartRecording)';
}

/// {@template countdown_in_progress_state}
/// State during the countdown before recording starts.
/// {@endtemplate}
class CountdownInProgress extends FaceVideoCaptureState {
  /// {@macro countdown_in_progress_state}
  const CountdownInProgress({
    required this.remainingSeconds,
    this.faceValidationStatus = 'Validating face position...',
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// Number of seconds remaining in the countdown
  final int remainingSeconds;

  /// Status message for face validation during countdown
  final String faceValidationStatus;

  @override
  List<Object?> get props => [
        remainingSeconds,
        faceValidationStatus,
        ...super.props,
      ];

  @override
  String toString() => 'CountdownInProgress('
      'remainingSeconds: $remainingSeconds, '
      'faceValidationStatus: $faceValidationStatus)';
}

/// {@template recording_state}
/// State when video recording is in progress.
/// {@endtemplate}
class Recording extends FaceVideoCaptureState {
  /// {@macro recording_state}
  const Recording({
    required this.elapsedTime,
    required this.remainingTime,
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// Time elapsed since recording started
  final Duration elapsedTime;

  /// Time remaining in the recording
  final Duration remainingTime;

  /// Progress percentage (0.0 to 1.0)
  double get progress {
    final totalDuration = elapsedTime + remainingTime;
    if (totalDuration.inMilliseconds == 0) return 0;
    return elapsedTime.inMilliseconds / totalDuration.inMilliseconds;
  }

  @override
  List<Object?> get props => [
        elapsedTime,
        remainingTime,
        ...super.props,
      ];

  @override
  String toString() => 'Recording(elapsed: ${elapsedTime.inSeconds}s, '
      'remaining: ${remainingTime.inSeconds}s, '
      'progress: ${(progress * 100).toStringAsFixed(1)}%)';
}

/// {@template processing_state}
/// State when recorded video is being validated.
/// {@endtemplate}
class Processing extends FaceVideoCaptureState {
  /// {@macro processing_state}
  const Processing({
    this.expectedVideoPath,
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// Expected path to the video file being processed
  final String? expectedVideoPath;

  @override
  List<Object?> get props => [
        expectedVideoPath,
        ...super.props,
      ];

  @override
  String toString() => 'Processing(expectedVideoPath: $expectedVideoPath)';
}

/// {@template success_state}
/// State when recording was successful with good face coverage.
/// {@endtemplate}
class Success extends FaceVideoCaptureState {
  /// {@macro success_state}
  const Success({
    required this.videoPath,
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// Path to the recorded video file
  final String videoPath;

  @override
  List<Object?> get props => [
        videoPath,
        ...super.props,
      ];

  @override
  String toString() => 'Success(videoPath: $videoPath, '
      'qualityScore: ${coverageStats.qualityScore.toStringAsFixed(1)})';
}

/// {@template failure_state}
/// State when recording failed or had poor face coverage.
/// {@endtemplate}
class Failure extends FaceVideoCaptureState {
  /// {@macro failure_state}
  const Failure({
    required this.reason,
    this.videoPath,
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// Reason for the failure
  final String reason;

  /// Path to the recorded video file (if any)
  final String? videoPath;

  @override
  List<Object?> get props => [
        reason,
        videoPath,
        ...super.props,
      ];

  @override
  String toString() => 'Failure(reason: $reason, videoPath: $videoPath)';
}

/// {@template error_state}
/// State when an error occurred during camera or detection operations.
/// {@endtemplate}
class Error extends FaceVideoCaptureState {
  /// {@macro error_state}
  const Error({
    required this.error,
    super.config,
    super.currentDetection,
    super.coverageStats,
    super.errorMessage,
  });

  /// The error that occurred
  final String error;

  @override
  List<Object?> get props => [
        error,
        ...super.props,
      ];

  @override
  String toString() => 'Error(error: $error)';
}
