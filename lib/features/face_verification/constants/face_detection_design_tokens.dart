import 'package:flutter/services.dart';

/// {@template detection_state}
/// Enumeration of face detection states for better code clarity
/// {@endtemplate}
enum DetectionState {
  /// No face detected
  noFace,

  /// Multiple faces detected
  multipleFaces,

  /// Single face with excellent coverage (80%+)
  excellent,

  /// Single face with good coverage (70-79%)
  good,

  /// Single face with moderate coverage (50-69%)
  moderate,

  /// Single face with poor coverage (30-49%)
  poor,

  /// Single face with very poor coverage (<30%)
  veryPoor;

  /// Gets the semantic color for this detection state
  Color get color {
    switch (this) {
      case DetectionState.noFace:
        return FaceDetectionDesignTokens.noDetectionColor;
      case DetectionState.multipleFaces:
        return FaceDetectionDesignTokens.multipleFacesColor;
      case DetectionState.excellent:
        return FaceDetectionDesignTokens.excellentColor;
      case DetectionState.good:
        return FaceDetectionDesignTokens.goodColor;
      case DetectionState.moderate:
        return FaceDetectionDesignTokens.moderateColor;
      case DetectionState.poor:
      case DetectionState.veryPoor:
        return FaceDetectionDesignTokens.poorColor;
    }
  }

  /// Gets the pulse animation speed for this detection state
  double get pulseSpeed {
    switch (this) {
      case DetectionState.excellent:
        return 0.5; // Slow, calm pulse
      case DetectionState.good:
        return 0.7; // Moderate pulse
      case DetectionState.moderate:
        return 1; // Normal pulse
      case DetectionState.poor:
        return 1.3; // Faster pulse
      case DetectionState.veryPoor:
      case DetectionState.noFace:
      case DetectionState.multipleFaces:
        return 1.5; // Fastest pulse for attention
    }
  }

  /// Triggers appropriate haptic feedback for this detection state
  Future<void> triggerHapticFeedback() async {
    switch (this) {
      case DetectionState.excellent:
        await HapticFeedback.lightImpact();
      case DetectionState.good:
        await HapticFeedback.selectionClick();
      case DetectionState.moderate:
      case DetectionState.poor:
        await HapticFeedback.mediumImpact();
      case DetectionState.veryPoor:
      case DetectionState.noFace:
      case DetectionState.multipleFaces:
        await HapticFeedback.heavyImpact();
    }
  }
}

/// {@template face_detection_design_tokens}
/// Unified design system for face detection UI/UX consistency.
///
/// Provides semantic colors, thresholds, feedback messages, and animations
/// that ensure visual feedback matches message states across all components.
/// {@endtemplate}
class FaceDetectionDesignTokens {
  FaceDetectionDesignTokens._();

  // ===========================================================================
  // COVERAGE THRESHOLDS
  // ===========================================================================

  /// Excellent coverage threshold (80%+) - Perfect positioning for
  /// deep analysis
  static const double excellentThreshold = 80;

  /// Good coverage threshold (75%+) - Acceptable positioning
  static const double goodThreshold = 75;

  /// Moderate coverage threshold (50%+) - Needs improvement
  static const double moderateThreshold = 50;

  /// Poor coverage threshold (30%+) - Significant adjustment needed
  static const double poorThreshold = 30;

  // ===========================================================================
  // SEMANTIC COLORS
  // ===========================================================================

  /// Excellent state color - Success/Perfect positioning
  static const Color excellentColor = Color(0xFF4CAF50); // Green

  /// Good state color - Acceptable positioning
  static const Color goodColor = Color(0xFF009688); // Teal

  /// Moderate state color - Needs improvement
  static const Color moderateColor = Color(0xFFFFC107); // Amber

  /// Poor state color - Significant issues
  static const Color poorColor = Color(0xFFF44336); // Red

  /// No detection color - Default/neutral state
  static const Color noDetectionColor = Color(0xFFFFFFFF); // White

  /// Multiple faces color - Warning state
  static const Color multipleFacesColor = Color(0xFFFF9800); // Orange

  // ===========================================================================
  // UTILITY FUNCTIONS
  // ===========================================================================

  /// Determines the detection state based on face detection result
  static DetectionState getDetectionState({
    required bool faceDetected,
    required int faceCount,
    required double coveragePercentage,
  }) {
    if (!faceDetected) {
      return DetectionState.noFace;
    }

    if (faceCount > 1) {
      return DetectionState.multipleFaces;
    }

    if (coveragePercentage >= excellentThreshold) {
      return DetectionState.excellent;
    } else if (coveragePercentage >= goodThreshold) {
      return DetectionState.good;
    } else if (coveragePercentage >= moderateThreshold) {
      return DetectionState.moderate;
    } else if (coveragePercentage >= poorThreshold) {
      return DetectionState.poor;
    } else {
      return DetectionState.veryPoor;
    }
  }

  /// Gets progressive color based on coverage
  /// percentage with smooth interpolation
  static Color getProgressiveColor(double coveragePercentage) {
    if (coveragePercentage >= excellentThreshold) {
      return excellentColor;
    } else if (coveragePercentage >= goodThreshold) {
      // Interpolate between good and excellent
      final progress = (coveragePercentage - goodThreshold) /
          (excellentThreshold - goodThreshold);
      return Color.lerp(goodColor, excellentColor, progress) ?? goodColor;
    } else if (coveragePercentage >= moderateThreshold) {
      // Interpolate between moderate and good
      final progress = (coveragePercentage - moderateThreshold) /
          (goodThreshold - moderateThreshold);
      return Color.lerp(moderateColor, goodColor, progress) ?? moderateColor;
    } else if (coveragePercentage >= poorThreshold) {
      // Interpolate between poor and moderate
      final progress = (coveragePercentage - poorThreshold) /
          (moderateThreshold - poorThreshold);
      return Color.lerp(poorColor, moderateColor, progress) ?? poorColor;
    } else {
      return poorColor;
    }
  }

  /// Gets unified feedback message based on detection state
  /// Enhanced with specific distance enforcement messages
  static String getFeedbackMessage(DetectionState state) {
    switch (state) {
      case DetectionState.noFace:
        return 'Position your face in the center guide';
      case DetectionState.multipleFaces:
        return 'Multiple faces detected - ensure only one person';
      case DetectionState.excellent:
        return 'Perfect! Hold this position for recording';
      case DetectionState.good:
        return 'Great! Move slightly closer for optimal quality';
      case DetectionState.moderate:
        return 'Move closer - face needs to be larger';
      case DetectionState.poor:
        return 'Face too small - move much closer to camera';
      case DetectionState.veryPoor:
        return 'Face too distant - move closer to fill guide';
    }
  }

  /// Gets dynamic pulse speed based on detection quality
  static double getDynamicPulseSpeed(double coveragePercentage) {
    if (coveragePercentage >= excellentThreshold) {
      return 0.5; // Slow, calm pulse for perfect positioning
    } else if (coveragePercentage >= goodThreshold) {
      return 0.7; // Moderate pulse for good positioning
    } else if (coveragePercentage >= moderateThreshold) {
      return 1; // Normal pulse for moderate positioning
    } else {
      return 1.5; // Fast pulse for poor positioning
    }
  }

  /// Triggers appropriate haptic feedback based on detection state
  static Future<void> triggerHapticFeedback(DetectionState state) async {
    switch (state) {
      case DetectionState.excellent:
        await HapticFeedback.lightImpact();
      case DetectionState.good:
        await HapticFeedback.selectionClick();
      case DetectionState.moderate:
      case DetectionState.poor:
        await HapticFeedback.mediumImpact();
      case DetectionState.veryPoor:
      case DetectionState.noFace:
      case DetectionState.multipleFaces:
        await HapticFeedback.heavyImpact();
    }
  }

  // ===========================================================================
  // ANIMATION CONSTANTS
  // ===========================================================================

  /// Default pulse animation duration
  static const Duration defaultPulseDuration = Duration(milliseconds: 1000);

  /// Color transition animation duration
  static const Duration colorTransitionDuration = Duration(milliseconds: 300);

  /// Feedback update debounce duration
  static const Duration feedbackDebounceDuration = Duration(milliseconds: 100);

  // ===========================================================================
  // ACCESSIBILITY CONSTANTS
  // ===========================================================================

  /// Semantic label for excellent detection state
  static const String excellentSemanticLabel =
      'Perfect face positioning detected';

  /// Semantic label for good detection state
  static const String goodSemanticLabel = 'Good face positioning detected';

  /// Semantic label for moderate detection state
  static const String moderateSemanticLabel =
      'Moderate face positioning, adjustment needed';

  /// Semantic label for poor detection state
  static const String poorSemanticLabel =
      'Poor face positioning, significant adjustment needed';

  /// Semantic label for no face detection
  static const String noFaceSemanticLabel =
      'No face detected, position face in guide';

  /// Semantic label for multiple faces detection
  static const String multipleFacesSemanticLabel =
      'Multiple faces detected, ensure only one person';

  /// Gets semantic label for detection state
  static String getSemanticLabel(DetectionState state) {
    switch (state) {
      case DetectionState.excellent:
        return excellentSemanticLabel;
      case DetectionState.good:
        return goodSemanticLabel;
      case DetectionState.moderate:
        return moderateSemanticLabel;
      case DetectionState.poor:
      case DetectionState.veryPoor:
        return poorSemanticLabel;
      case DetectionState.noFace:
        return noFaceSemanticLabel;
      case DetectionState.multipleFaces:
        return multipleFacesSemanticLabel;
    }
  }
}
