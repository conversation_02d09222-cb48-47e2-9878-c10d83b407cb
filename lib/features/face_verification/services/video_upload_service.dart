import 'dart:async';
import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/video_gallery/models/firebase_video_metadata.dart';
import 'package:bloomg_flutter/features/video_gallery/services/firebase_video_metadata_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:firebase_storage/firebase_storage.dart';

/// Upload status for tracking video upload progress
enum UploadStatus {
  /// Upload is starting
  starting,

  /// Upload is in progress
  uploading,

  /// Upload is saving metadata
  savingMetadata,

  /// Upload completed successfully
  completed,

  /// Upload failed
  failed,

  /// Upload was cancelled
  cancelled,
}

/// Upload progress data containing status and progress percentage
class UploadProgress {
  const UploadProgress({
    required this.status,
    required this.progress,
    this.message,
    this.error,
  });

  /// Current upload status
  final UploadStatus status;

  /// Progress percentage (0.0 to 1.0)
  final double progress;

  /// Optional status message
  final String? message;

  /// Error message if status is failed
  final String? error;

  @override
  String toString() => 'UploadProgress('
      'status: $status, '
      'progress: ${(progress * 100).toStringAsFixed(1)}%, '
      'message: $message'
      ')';
}

/// {@template video_upload_service}
/// Service for uploading face verification videos to Firebase Storage.
/// Handles video uploads, progress tracking,
///  retry mechanisms, and metadata storage.
/// {@endtemplate}
class VideoUploadService {
  /// {@macro video_upload_service}
  VideoUploadService({
    LoggerService? logger,
    FirebaseStorage? storage,
    FirebaseVideoMetadataService? metadataService,
  })  : _logger = logger ?? LoggerService(),
        _storage = storage ?? FirebaseStorage.instance,
        _metadataService = metadataService ?? FirebaseVideoMetadataService();

  final LoggerService _logger;
  final FirebaseStorage _storage;
  final FirebaseVideoMetadataService _metadataService;

  /// Current upload task for progress tracking and cancellation
  UploadTask? _currentUploadTask;

  /// Stream controller for upload progress
  StreamController<UploadProgress>? _progressController;

  /// Upload video file to Firebase Storage with metadata and progress tracking
  ///
  /// [videoPath] - Local path to the video file
  /// [coverageStats] - Face coverage statistics to include as metadata
  /// [userId] - User ID for organizing uploads
  ///
  /// Returns a stream of upload progress and the final video metadata
  Stream<UploadProgress> uploadVideoWithProgress({
    required String videoPath,
    required FaceCoverageStats coverageStats,
    required String userId,
  }) async* {
    _progressController = StreamController<UploadProgress>.broadcast();

    try {
      // Emit starting status
      yield const UploadProgress(
        status: UploadStatus.starting,
        progress: 0,
        message: 'Preparing video for upload...',
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Starting video upload with progress tracking',
          'User: $userId, Path: $videoPath',
        ),
      );

      // Validate video file exists and is readable
      final videoFile = File(videoPath);
      if (!videoFile.existsSync()) {
        yield UploadProgress(
          status: UploadStatus.failed,
          progress: 0,
          error: 'Video file not found: $videoPath',
        );
        return;
      }

      // Check file size and validate
      final fileSize = await videoFile.length();
      if (fileSize == 0) {
        yield UploadProgress(
          status: UploadStatus.failed,
          progress: 0,
          error: 'Video file is empty: $videoPath',
        );
        return;
      }

      // Check file size limit (50MB max)
      const maxFileSize = 50 * 1024 * 1024; // 50MB
      if (fileSize > maxFileSize) {
        yield UploadProgress(
          status: UploadStatus.failed,
          progress: 0,
          error: 'Video file too large: ${fileSize}B > ${maxFileSize}B',
        );
        return;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video file validated',
          'Size: ${fileSize}B',
        ),
      );

      // Generate unique filename and storage path
      final fileName = _generateFileName(userId);
      final storagePath = 'face_verification_videos/$userId/$fileName';

      // Create Firebase Storage reference
      final storageRef = _storage.ref().child(storagePath);

      // Create metadata for Firebase Storage
      final storageMetadata = SettableMetadata(
        contentType: 'video/mp4',
        customMetadata: _createVideoMetadata(coverageStats),
      );

      // Emit uploading status
      yield const UploadProgress(
        status: UploadStatus.uploading,
        progress: 0,
        message: 'Uploading video to Firebase Storage...',
      );

      // Log authentication and upload details for debugging
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Starting Firebase Storage upload',
          'User: $userId, Path: $storagePath, File size: ${fileSize}B',
        ),
      );

      // Start upload to Firebase Storage
      _currentUploadTask = storageRef.putFile(videoFile, storageMetadata);

      // Track upload progress and wait for completion
      var uploadCompleted = false;
      await for (final snapshot in _currentUploadTask!.snapshotEvents) {
        // Skip processing if upload is already completed to prevent loop
        if (uploadCompleted) {
          break;
        }

        // Check task state first to prevent processing completed uploads
        if (snapshot.state == TaskState.success) {
          // Emit final progress update for successful completion
          yield const UploadProgress(
            status: UploadStatus.uploading,
            progress: 1,
            message: 'Upload complete!',
          );

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Upload progress',
              '100.0% - Upload completed successfully',
            ),
          );

          uploadCompleted = true;
          break;
        } else if (snapshot.state == TaskState.error) {
          uploadCompleted = true;
          throw Exception('Upload failed during Firebase Storage upload');
        } else if (snapshot.state == TaskState.canceled) {
          uploadCompleted = true;
          throw Exception('Upload was canceled');
        }

        // Only emit progress updates for running uploads
        if (snapshot.state == TaskState.running) {
          final progress = snapshot.totalBytes > 0
              ? snapshot.bytesTransferred / snapshot.totalBytes
              : 0.0;

          yield UploadProgress(
            status: UploadStatus.uploading,
            progress: progress,
            message: 'Uploading... ${(progress * 100).toStringAsFixed(1)}%',
          );

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Upload progress',
              '${(progress * 100).toStringAsFixed(1)}%',
            ),
          );
        }
      }

      // Upload completed successfully, get download URL

      // Get download URL
      final downloadUrl =
          await _currentUploadTask!.snapshot.ref.getDownloadURL();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video uploaded to Firebase Storage',
          'URL: $downloadUrl',
        ),
      );

      // Emit saving metadata status
      yield const UploadProgress(
        status: UploadStatus.savingMetadata,
        progress: 0.9,
        message: 'Saving video metadata...',
      );

      // Create video metadata for Firestore
      final videoMetadata = FirebaseVideoMetadata(
        id: '', // Will be set by Firestore
        userId: userId,
        fileName: fileName,
        downloadUrl: downloadUrl,
        storagePath: storagePath,
        uploadTimestamp: DateTime.now(),
        fileSize: fileSize,
        duration: coverageStats.recordingDuration,
        qualityScore: coverageStats.qualityScore,
        faceCoverageStats: coverageStats,
      );

      // Save metadata to Firestore with error handling
      String? documentId;
      var completionMessage = 'Upload completed successfully!';

      try {
        documentId = await _metadataService.saveVideoMetadata(videoMetadata);

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Video upload completed successfully',
            'Document ID: $documentId, User: $userId',
          ),
        );
      } catch (firestoreError, stackTrace) {
        // Log Firestore error but don't fail the entire upload
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Failed to save video metadata to Firestore',
            'Error: $firestoreError, User: $userId, URL: $downloadUrl',
          ),
        );

        // Check if it's a database not found error
        final errorMessage = firestoreError.toString().toLowerCase();
        if (errorMessage.contains('database') &&
            errorMessage.contains('does not exist')) {
          _logger.error(
            LoggingConstants.formatError(
              LoggingConstants.faceVerificationModule,
              LoggingConstants.criticalError,
              'Firestore database not found - please enable Cloud Firestore',
              'Project: bloomg-flutter, User: $userId',
            ),
            firestoreError,
            stackTrace,
          );

          completionMessage = 'Video uploaded successfully! '
              'Note: Metadata saving failed - please enable Cloud Firestore.';
        } else {
          _logger.error(
            LoggingConstants.formatError(
              LoggingConstants.faceVerificationModule,
              LoggingConstants.recoverableError,
              'Firestore metadata save failed: $firestoreError',
              'User: $userId, URL: $downloadUrl',
            ),
            firestoreError,
            stackTrace,
          );

          completionMessage = 'Video uploaded successfully! '
              'Note: Metadata saving failed but video is accessible.';
        }
      }

      // Emit completion status (always complete,
      // even if metadata saving failed)
      yield UploadProgress(
        status: UploadStatus.completed,
        progress: 1,
        message: completionMessage,
      );
    } catch (e, stackTrace) {
      // Enhanced error logging for Firebase Storage authorization issues
      var errorDetails = 'User: $userId, Path: $videoPath';
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('unauthorized') ||
          errorMessage.contains('permission') ||
          errorMessage.contains('auth')) {
        errorDetails += ', Auth Error: Check Firebase Storage rules and '
            'user authentication';
      }

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video upload failed: $e',
          errorDetails,
        ),
        e,
        stackTrace,
      );

      yield UploadProgress(
        status: UploadStatus.failed,
        progress: 0,
        error: 'Failed to upload video: $e',
      );
    } finally {
      _currentUploadTask = null;
      await _progressController?.close();
      _progressController = null;
    }
  }

  /// Upload video file to Firebase Storage with metadata
  ///
  /// [videoPath] - Local path to the video file
  /// [coverageStats] - Face coverage statistics to include as metadata
  /// [userId] - User ID for organizing uploads
  ///
  /// Returns the Firebase video metadata with download URL
  Future<FirebaseVideoMetadata> uploadVideo({
    required String videoPath,
    required FaceCoverageStats coverageStats,
    required String userId,
  }) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Starting video upload',
        'User: $userId, Path: $videoPath',
      ),
    );

    try {
      // Validate video file exists and is readable
      final videoFile = File(videoPath);
      if (!await videoFile.exists()) {
        throw VideoUploadException('Video file not found: $videoPath');
      }

      // Check file size and validate
      final fileSize = await videoFile.length();
      if (fileSize == 0) {
        throw VideoUploadException('Video file is empty: $videoPath');
      }

      // Check file size limit (50MB max)
      const maxFileSize = 50 * 1024 * 1024; // 50MB
      if (fileSize > maxFileSize) {
        throw VideoUploadException(
          'Video file too large: ${fileSize}B > ${maxFileSize}B',
        );
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video file validated',
          'Size: ${fileSize}B',
        ),
      );

      // Generate unique filename and storage path
      final fileName = _generateFileName(userId);
      final storagePath = 'face_verification_videos/$userId/$fileName';

      // Create Firebase Storage reference
      final storageRef = _storage.ref().child(storagePath);

      // Create metadata for Firebase Storage
      final storageMetadata = SettableMetadata(
        contentType: 'video/mp4',
        customMetadata: _createVideoMetadata(coverageStats),
      );

      // Log authentication and upload details for debugging
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Starting Firebase Storage upload',
          'User: $userId, Path: $storagePath, File size: ${fileSize}B',
        ),
      );

      // Start upload to Firebase Storage
      _currentUploadTask = storageRef.putFile(videoFile, storageMetadata);

      // Track upload progress
      _currentUploadTask!.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Upload progress',
            '${(progress * 100).toStringAsFixed(1)}%',
          ),
        );
      });

      // Wait for upload to complete
      final taskSnapshot = await _currentUploadTask!;
      final downloadUrl = await taskSnapshot.ref.getDownloadURL();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video uploaded to Firebase Storage',
          'URL: $downloadUrl',
        ),
      );

      // Create video metadata for Firestore
      final videoMetadata = FirebaseVideoMetadata(
        id: '', // Will be set by Firestore
        userId: userId,
        fileName: fileName,
        downloadUrl: downloadUrl,
        storagePath: storagePath,
        uploadTimestamp: DateTime.now(),
        fileSize: fileSize,
        duration: coverageStats.recordingDuration,
        qualityScore: coverageStats.qualityScore,
        faceCoverageStats: coverageStats,
      );

      // Save metadata to Firestore
      final documentId =
          await _metadataService.saveVideoMetadata(videoMetadata);
      final finalMetadata = videoMetadata.copyWith(id: documentId);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video upload completed successfully',
          'Document ID: $documentId, User: $userId',
        ),
      );

      return finalMetadata;
    } catch (e, stackTrace) {
      // Enhanced error logging for Firebase Storage authorization issues
      var errorDetails = 'User: $userId, Path: $videoPath';
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('unauthorized') ||
          errorMessage.contains('permission') ||
          errorMessage.contains('auth')) {
        errorDetails += ', Auth Error: Check Firebase Storage rules and '
            'user authentication';
      }

      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Video upload failed: $e',
          errorDetails,
        ),
        e,
        stackTrace,
      );
      throw VideoUploadException('Failed to upload video: $e');
    } finally {
      _currentUploadTask = null;
    }
  }

  /// Get upload progress stream for tracking
  ///
  /// Returns a stream of upload progress values (0.0 to 1.0)
  Stream<double> getUploadProgress() {
    if (_currentUploadTask == null) {
      return Stream.value(0);
    }

    return _currentUploadTask!.snapshotEvents.map((TaskSnapshot snapshot) {
      if (snapshot.totalBytes == 0) return 0.0;
      return snapshot.bytesTransferred / snapshot.totalBytes;
    });
  }

  /// Cancel ongoing upload
  Future<void> cancelUpload() async {
    if (_currentUploadTask == null) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'No upload task to cancel',
        ),
      );
      return;
    }

    try {
      await _currentUploadTask!.cancel();
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Upload cancelled successfully',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Failed to cancel upload: $e',
        ),
        e,
      );
    } finally {
      _currentUploadTask = null;
    }
  }

  /// Delete uploaded video from Firebase Storage and Firestore
  ///
  /// [videoMetadata] - The video metadata containing storage
  ///  path and document ID
  Future<void> deleteVideo(FirebaseVideoMetadata videoMetadata) async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Deleting video',
        'Document ID: ${videoMetadata.id}, Path: ${videoMetadata.storagePath}',
      ),
    );

    try {
      // Delete from Firebase Storage
      final storageRef = _storage.ref().child(videoMetadata.storagePath);
      await storageRef.delete();

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video deleted from Firebase Storage',
          'Path: ${videoMetadata.storagePath}',
        ),
      );

      // Delete thumbnail if it exists
      if (videoMetadata.thumbnailStoragePath != null) {
        try {
          final thumbnailRef =
              _storage.ref().child(videoMetadata.thumbnailStoragePath!);
          await thumbnailRef.delete();
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Thumbnail deleted from Firebase Storage',
              'Path: ${videoMetadata.thumbnailStoragePath}',
            ),
          );
        } catch (e) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Failed to delete thumbnail (continuing)',
              'Error: $e',
            ),
          );
        }
      }

      // Delete metadata from Firestore
      await _metadataService.deleteVideoMetadata(videoMetadata.id);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Video deleted successfully',
          'Document ID: ${videoMetadata.id}',
        ),
      );
    } catch (e, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Video deletion failed: $e',
          'Document ID: ${videoMetadata.id}',
        ),
        e,
        stackTrace,
      );
      throw VideoUploadException('Failed to delete video: $e');
    }
  }

  /// Generate unique filename for video upload
  String _generateFileName(String userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'face_verification_${userId}_$timestamp.mp4';
  }

  /// Create metadata map for video upload
  Map<String, String> _createVideoMetadata(FaceCoverageStats coverageStats) {
    return {
      'upload_timestamp': DateTime.now().toIso8601String(),
      'total_frames': coverageStats.totalFrames.toString(),
      'frames_with_face': coverageStats.framesWithFace.toString(),
      'frames_with_valid_coverage':
          coverageStats.framesWithValidCoverage.toString(),
      'average_coverage': coverageStats.averageCoverage.toStringAsFixed(2),
      'minimum_coverage': coverageStats.minimumCoverage.toStringAsFixed(2),
      'maximum_coverage': coverageStats.maximumCoverage.toStringAsFixed(2),
      'app_version': '1.0.0', // TODO(dev): Get from package info
      'platform': Platform.operatingSystem,
    };
  }

  /// Get user's upload history from Firestore
  Future<List<FirebaseVideoMetadata>> getUserUploadHistory(
    String userId,
  ) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Fetching upload history',
          'User ID: $userId',
        ),
      );

      final videos = await _metadataService.getUserVideos(userId);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Upload history fetched successfully',
          'User ID: $userId, Count: ${videos.length}',
        ),
      );

      return videos;
    } catch (e, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Failed to fetch upload history: $e',
          'User ID: $userId',
        ),
        e,
        stackTrace,
      );
      throw VideoUploadException('Failed to fetch upload history: $e');
    }
  }
}

/// Exception thrown when video upload operations fail
class VideoUploadException implements Exception {
  const VideoUploadException(this.message);
  final String message;

  @override
  String toString() => 'VideoUploadException: $message';
}
