import 'dart:ui' as ui;

import 'package:bloomg_flutter/features/face_verification/constants/face_detection_design_tokens.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template face_detection_service}
/// Service that handles face detection using Google ML Kit.
/// Provides real-time face detection and coverage analysis.
/// {@endtemplate}
class FaceDetectionService {
  /// {@macro face_detection_service}
  FaceDetectionService();

  final LoggerService _logger = LoggerService();

  FaceDetector? _faceDetector;
  bool _isInitialized = false;

  // Face guide configuration (oval area in center of screen)
  static const double _faceGuideWidthRatio = 0.7; // 70% of screen width
  static const double _faceGuideHeightRatio = 0.8; // 80% of screen height
  static const double _minimumCoverageThreshold =
      80; // 80% coverage required for deep analysis

  // Face quality thresholds
  static const double _maxHeadRotationAngle = 15; // degrees

  // ENHANCED FACE SIZE VALIDATION THRESHOLDS
  // Updated to enforce stricter distance requirements per requirements
  static const double _minFaceSizeRatio =
      0.22; // 22% of screen height minimum (was 15%)
  static const double _optimalFaceSizeRatio =
      0.30; // 30% of screen height optimal (was 25%)
  static const double _maxFaceSizeRatio = 0.75; // 75% of screen

  // Distance enforcement constants
  static const double _distantFaceThreshold =
      0.18; // 18% - faces below this are too distant
  static const double _veryDistantFaceThreshold =
      0.12; // 12% - extremely distant faces

  static const double _minEyeOpenProbability = 0.5; // 50% confidence
  static const int _maxFrameProcessingMs = 100; // 100ms max processing time

  // Frame processing statistics
  int _totalFramesProcessed = 0;
  int _framesWithValidFace = 0;
  int _inputImageConversionFailures =
      0; // Fixed: removed final to allow increments
  int _mlKitProcessingFailures = 0;
  DateTime? _lastFrameProcessTime;

  // Enhanced diagnostic tracking
  int _consecutiveFailures = 0;
  int _maxConsecutiveFailures = 0;
  final List<int> _recentProcessingTimes = [];
  static const int _maxRecentTimesTracked = 10;

  // Frame-by-frame diagnostic tracking
  int _frameSequenceNumber = 0;
  int _inputImageConversionSuccesses = 0;
  int _mlKitProcessingSuccesses = 0;
  final List<String> _recentFailureReasons = [];
  static const int _maxRecentFailuresTracked = 20;

  // Processing time breakdown tracking
  final List<int> _inputImageConversionTimes = [];
  final List<int> _mlKitProcessingTimes = [];
  final List<int> _resultConversionTimes = [];
  static const int _maxTimeBreakdownTracked = 15;

  /// Whether the service is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Adds a failure reason to the recent failures tracking list
  void _addFailureReason(String reason) {
    _recentFailureReasons.add(reason);
    if (_recentFailureReasons.length > _maxRecentFailuresTracked) {
      _recentFailureReasons.removeAt(0);
    }
  }

  /// Tracks successful InputImage conversions for diagnostic purposes
  void trackConversionSuccess() {
    _inputImageConversionSuccesses++;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'InputImage conversion success tracked',
        'Total conversion successes: $_inputImageConversionSuccesses',
      ),
    );
  }

  /// Initializes the face detection service and ML Kit detector
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service already initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service initialization started',
      ),
    );

    try {
      // Configure face detector options for optimal real-time performance
      // CRITICAL FIX: Use FAST mode with tracking for better consistency
      final options = FaceDetectorOptions(
        enableTracking: true, // Enable for better frame-to-frame consistency
        minFaceSize:
            0.15, // Larger than default 0.1 to avoid tiny false positives
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Creating FaceDetector with optimized options',
          'Mode: FAST, Tracking: enabled, MinFaceSize: 0.15, '
              'Contours/Landmarks/Classification: disabled',
        ),
      );

      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialization completed',
          'FaceDetector created successfully with optimized real-time options',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Processes an InputImage and detects faces using ML Kit
  ///
  /// This method performs real face detection on the provided InputImage
  /// and returns comprehensive face analysis results.
  Future<FaceDetectionResult?> processImage(InputImage inputImage) async {
    if (!_isInitialized) {
      throw StateError('Face detection service not initialized');
    }

    final startTime = DateTime.now();
    _frameSequenceNumber++;

    // Enhanced frame-by-frame diagnostic logging
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Starting frame processing',
        'Frame #$_frameSequenceNumber, '
            'InputImage: ${inputImage.metadata?.size}, '
            'Format: ${inputImage.metadata?.format}',
      ),
    );

    try {
      // Enhanced InputImage validation and debugging
      final metadata = inputImage.metadata;
      if (metadata == null) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'InputImage validation failed',
            'Metadata is null',
          ),
        );
        return null;
      }

      final size = metadata.size;
      final format = metadata.format;
      final rotation = metadata.rotation;
      final bytesPerRow = metadata.bytesPerRow;

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection processing started',
          'Size: ${size.width}x${size.height}, Format: $format, '
              'Rotation: $rotation, BytesPerRow: $bytesPerRow',
        ),
      );

      // Validate InputImage parameters
      if (size.width <= 0 || size.height <= 0) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'InputImage validation failed',
            'Invalid dimensions: ${size.width}x${size.height}',
          ),
        );
        return null;
      }

      // Process image with ML Kit face detector
      final mlKitStartTime = DateTime.now();
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Calling ML Kit processImage',
          'Frame #$_frameSequenceNumber - Starting ML Kit processing',
        ),
      );

      final faces = await _faceDetector!.processImage(inputImage);

      final mlKitProcessingTime =
          DateTime.now().difference(mlKitStartTime).inMilliseconds;
      final totalProcessingTime =
          DateTime.now().difference(startTime).inMilliseconds;

      // Track ML Kit processing success/failure
      if (faces.isNotEmpty) {
        _mlKitProcessingSuccesses++;
      } else {
        _mlKitProcessingFailures++;
        _addFailureReason('ML Kit returned no faces');
      }

      // Track processing time breakdown
      _mlKitProcessingTimes.add(mlKitProcessingTime);
      if (_mlKitProcessingTimes.length > _maxTimeBreakdownTracked) {
        _mlKitProcessingTimes.removeAt(0);
      }

      _totalFramesProcessed++;
      _lastFrameProcessTime = DateTime.now();

      // Enhanced ML Kit processing diagnostics
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'ML Kit processing completed',
          'Frame #$_frameSequenceNumber - Faces: ${faces.length}, '
              'ML Kit time: ${mlKitProcessingTime}ms, '
              'Total time: ${totalProcessingTime}ms',
        ),
      );

      // Enhanced diagnostic tracking
      _recentProcessingTimes.add(totalProcessingTime);
      if (_recentProcessingTimes.length > _maxRecentTimesTracked) {
        _recentProcessingTimes.removeAt(0);
      }

      // Track consecutive failures/successes
      if (faces.isEmpty) {
        _consecutiveFailures++;
        if (_consecutiveFailures > _maxConsecutiveFailures) {
          _maxConsecutiveFailures = _consecutiveFailures;
        }
      } else {
        _consecutiveFailures = 0;
      }

      // Enhanced logging for face detection results
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'ML Kit face detection raw results',
          'Faces detected: ${faces.length}, '
              'Processing time: ${totalProcessingTime}ms',
        ),
      );

      // Log individual face details if faces are detected
      if (faces.isNotEmpty) {
        for (var i = 0; i < faces.length; i++) {
          final face = faces[i];
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Face $i details',
              'BoundingBox: ${face.boundingBox}, '
                  'HeadEulerAngleY: ${face.headEulerAngleY}, '
                  'HeadEulerAngleZ: ${face.headEulerAngleZ}',
            ),
          );
        }
      }

      // Log performance metrics
      if (totalProcessingTime > _maxFrameProcessingMs) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection processing slow',
            'Time: ${totalProcessingTime}ms, '
                'Threshold: ${_maxFrameProcessingMs}ms',
          ),
        );
      }

      // Convert ML Kit results to our format
      final result = _convertFacesToResult(faces, inputImage);

      if (result.isValidDetection) {
        _framesWithValidFace++;
      }

      // Enhanced statistics logging with failure tracking
      final currentValidRate = _totalFramesProcessed > 0
          ? (_framesWithValidFace / _totalFramesProcessed) * 100
          : 0.0;
      final failureRate = _totalFramesProcessed > 0
          ? ((_inputImageConversionFailures + _mlKitProcessingFailures) /
                  _totalFramesProcessed) *
              100
          : 0.0;

      // Calculate average processing time from recent frames
      final avgProcessingTime = _recentProcessingTimes.isNotEmpty
          ? _recentProcessingTimes.reduce((a, b) => a + b) /
              _recentProcessingTimes.length
          : 0.0;

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection completed',
          'Faces: ${faces.length}, Coverage: '
              '${result.coveragePercentage.toStringAsFixed(1)}%, '
              'Valid: ${result.isValidDetection}, '
              'Time: ${totalProcessingTime}ms '
              '(avg: ${avgProcessingTime.toStringAsFixed(1)}ms), '
              'Stats: $_framesWithValidFace/$_totalFramesProcessed '
              '(${currentValidRate.toStringAsFixed(1)}%), '
              'Failures: '
              '${_inputImageConversionFailures + _mlKitProcessingFailures} '
              '(${failureRate.toStringAsFixed(1)}%), '
              'Consecutive failures: $_consecutiveFailures '
              '(max: $_maxConsecutiveFailures)',
        ),
      );

      return result;
    } catch (error, stackTrace) {
      _mlKitProcessingFailures++;
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection processing failed: $error, '
          'Total ML Kit failures: $_mlKitProcessingFailures',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Converts ML Kit Face detection results to FaceDetectionResult format
  FaceDetectionResult _convertFacesToResult(
    List<Face> faces,
    InputImage inputImage,
  ) {
    final timestamp = DateTime.now();
    final imageSize = inputImage.metadata?.size;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Converting ML Kit results to FaceDetectionResult',
        'Faces: ${faces.length}, ImageSize: $imageSize',
      ),
    );

    if (faces.isEmpty) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'No faces detected',
          'Returning empty result',
        ),
      );
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );
    }

    final faceCount = faces.length;
    final primaryFace = faces.first;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Processing primary face',
        'BoundingBox: ${primaryFace.boundingBox}',
      ),
    );

    // Analyze face quality
    final faceQuality = _analyzeFaceQuality(primaryFace, imageSize);

    // Calculate face coverage within guide area
    double coveragePercentage = 0;
    if (imageSize != null) {
      coveragePercentage = _calculateRealFaceCoverage(
        primaryFace.boundingBox,
        Size(imageSize.width, imageSize.height),
      );

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face coverage calculated',
          'Coverage: ${coveragePercentage.toStringAsFixed(1)}%',
        ),
      );
    } else {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Cannot calculate coverage - no image size',
          'ImageSize is null',
        ),
      );
    }

    // Convert ML Kit bounding box to our format
    final boundingBox = FaceBoundingBox(
      left: primaryFace.boundingBox.left,
      top: primaryFace.boundingBox.top,
      width: primaryFace.boundingBox.width,
      height: primaryFace.boundingBox.height,
    );

    final result = FaceDetectionResult(
      faceDetected: true,
      faceCount: faceCount,
      coveragePercentage: coveragePercentage,
      timestamp: timestamp,
      boundingBox: boundingBox,
      confidence: faceQuality.overallConfidence,
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'FaceDetectionResult created',
        'Detected: ${result.faceDetected}, Count: ${result.faceCount}, '
            'Coverage: ${result.coveragePercentage.toStringAsFixed(1)}%, '
            'Confidence: ${result.confidence?.toStringAsFixed(2)}',
      ),
    );

    return result;
  }

  /// Analyzes the quality of a detected face
  FaceQuality _analyzeFaceQuality(Face face, ui.Size? imageSize) {
    var overallConfidence = 0.8; // Base confidence for ML Kit detection
    final issues = <String>[];

    // Check head rotation angles
    final headEulerAngleX = face.headEulerAngleX ?? 0;
    final headEulerAngleY = face.headEulerAngleY ?? 0;
    final headEulerAngleZ = face.headEulerAngleZ ?? 0;

    if (headEulerAngleX.abs() > _maxHeadRotationAngle ||
        headEulerAngleY.abs() > _maxHeadRotationAngle ||
        headEulerAngleZ.abs() > _maxHeadRotationAngle) {
      issues.add('Head rotation too high');
      overallConfidence -= 0.2;
    }

    // Check face size relative to image
    if (imageSize != null) {
      final faceArea = face.boundingBox.width * face.boundingBox.height;
      final imageArea = imageSize.width * imageSize.height;
      final faceSizeRatio = faceArea / imageArea;

      if (faceSizeRatio < _minFaceSizeRatio) {
        issues.add('Face too small');
        overallConfidence -= 0.3;
      } else if (faceSizeRatio > _maxFaceSizeRatio) {
        issues.add('Face too large');
        overallConfidence -= 0.2;
      }
    }

    // Check eye openness if available
    final leftEyeOpenProbability = face.leftEyeOpenProbability;
    final rightEyeOpenProbability = face.rightEyeOpenProbability;

    if (leftEyeOpenProbability != null && rightEyeOpenProbability != null) {
      if (leftEyeOpenProbability < _minEyeOpenProbability ||
          rightEyeOpenProbability < _minEyeOpenProbability) {
        issues.add('Eyes not sufficiently open');
        overallConfidence -= 0.1;
      }
    }

    return FaceQuality(
      overallConfidence: overallConfidence.clamp(0.0, 1.0),
      issues: issues,
      headRotationX: headEulerAngleX,
      headRotationY: headEulerAngleY,
      headRotationZ: headEulerAngleZ,
      leftEyeOpenProbability: leftEyeOpenProbability,
      rightEyeOpenProbability: rightEyeOpenProbability,
    );
  }

  /// Validates if the face coverage meets the minimum threshold
  bool validateCoverage(FaceDetectionResult result) {
    return result.faceDetected &&
        result.faceCount == 1 &&
        result.coveragePercentage >= _minimumCoverageThreshold;
  }

  /// Calculates real face coverage percentage within the oval guide area
  ///
  /// Returns the percentage of the detected face that is within the oval guide.
  /// This is calculated as: (intersection area / face area) * 100
  double _calculateRealFaceCoverage(ui.Rect faceBounds, Size screenSize) {
    // Calculate face guide area (centered oval)
    final guideWidth = screenSize.width * _faceGuideWidthRatio;
    final guideHeight = screenSize.height * _faceGuideHeightRatio;
    final guideLeft = (screenSize.width - guideWidth) / 2;
    final guideTop = (screenSize.height - guideHeight) / 2;

    // For oval intersection, we approximate using ellipse area calculation
    // Convert face rectangle to center and dimensions
    final faceCenterX = faceBounds.left + (faceBounds.width / 2);
    final faceCenterY = faceBounds.top + (faceBounds.height / 2);
    final faceWidth = faceBounds.width;
    final faceHeight = faceBounds.height;

    // Calculate guide oval center and radii
    final guideCenterX = guideLeft + (guideWidth / 2);
    final guideCenterY = guideTop + (guideHeight / 2);
    final guideRadiusX = guideWidth / 2;
    final guideRadiusY = guideHeight / 2;

    // Calculate face area for proper coverage calculation
    final faceArea = faceWidth * faceHeight;

    // Validate face area to prevent division by zero
    if (faceArea <= 0) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Invalid face area detected',
          'Face area: $faceArea, returning 0% coverage',
        ),
      );
      return 0;
    }

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face coverage calculation started',
        'Face: center($faceCenterX, $faceCenterY) '
            'size(${faceWidth.toStringAsFixed(1)}, '
            '${faceHeight.toStringAsFixed(1)}) '
            'area=${faceArea.toStringAsFixed(1)}, '
            'Guide: center($guideCenterX, $guideCenterY) '
            'radii(${guideRadiusX.toStringAsFixed(1)}, '
            '${guideRadiusY.toStringAsFixed(1)})',
      ),
    );

    // Calculate intersection area between face rectangle and guide oval
    final intersectionArea = _calculateRectangleOvalIntersection(
      faceCenterX,
      faceCenterY,
      faceWidth,
      faceHeight,
      guideCenterX,
      guideCenterY,
      guideRadiusX,
      guideRadiusY,
    );

    // Calculate intersection coverage (percentage of face within guide)
    final intersectionCoverage = (intersectionArea / faceArea) * 100.0;

    // CRITICAL FIX: Factor in face size to prevent small faces from
    // showing 100% coverage
    // Calculate face size ratio relative to screen height
    final faceSizeRatio = faceHeight / screenSize.height;

    // Calculate face size factor - small faces get heavily penalized
    final faceSizeFactor = _calculateFaceSizeFactor(faceSizeRatio);

    // Final coverage combines intersection coverage with face size adequacy
    final rawCoveragePercentage = intersectionCoverage * faceSizeFactor;

    // Clamp coverage to valid range (0-100%)
    final coveragePercentage = rawCoveragePercentage.clamp(0.0, 100.0);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face coverage calculation completed',
        'Intersection area: ${intersectionArea.toStringAsFixed(1)}, '
            'Face area: ${faceArea.toStringAsFixed(1)}, '
            'Intersection coverage: '
            '${intersectionCoverage.toStringAsFixed(1)}%, '
            'Face size ratio: '
            '${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
            'Size factor: ${faceSizeFactor.toStringAsFixed(2)}, '
            'Final coverage: ${coveragePercentage.toStringAsFixed(1)}%',
      ),
    );

    return coveragePercentage;
  }

  /// Calculates intersection area between a rectangle and an oval
  /// using high-resolution sampling
  double _calculateRectangleOvalIntersection(
    double rectCenterX,
    double rectCenterY,
    double rectWidth,
    double rectHeight,
    double ovalCenterX,
    double ovalCenterY,
    double ovalRadiusX,
    double ovalRadiusY,
  ) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Calculating rectangle-oval intersection',
        'Rect: center($rectCenterX, $rectCenterY) '
            'size(${rectWidth.toStringAsFixed(1)}, '
            '${rectHeight.toStringAsFixed(1)}), '
            'Oval: center($ovalCenterX, $ovalCenterY) '
            'radii(${ovalRadiusX.toStringAsFixed(1)}, '
            '${ovalRadiusY.toStringAsFixed(1)})',
      ),
    );

    // Use high-resolution sampling for accurate intersection calculation
    // 50x50 grid = 2500 sample points (25x more accurate than before)
    const samplesPerSide = 50;
    const totalSamplePoints = samplesPerSide * samplesPerSide;

    final rectLeft = rectCenterX - (rectWidth / 2);
    final rectTop = rectCenterY - (rectHeight / 2);
    final rectRight = rectCenterX + (rectWidth / 2);
    final rectBottom = rectCenterY + (rectHeight / 2);

    var pointsInside = 0;

    // Sample points uniformly across the rectangle
    for (var row = 0; row < samplesPerSide; row++) {
      for (var col = 0; col < samplesPerSide; col++) {
        // Calculate sample point coordinates with proper spacing
        final x = rectLeft + (rectWidth * col / (samplesPerSide - 1));
        final y = rectTop + (rectHeight * row / (samplesPerSide - 1));

        // Ensure point is within rectangle bounds (edge case handling)
        if (x >= rectLeft &&
            x <= rectRight &&
            y >= rectTop &&
            y <= rectBottom) {
          // Check if point is inside oval using ellipse equation
          final normalizedX = (x - ovalCenterX) / ovalRadiusX;
          final normalizedY = (y - ovalCenterY) / ovalRadiusY;

          if ((normalizedX * normalizedX) + (normalizedY * normalizedY) <= 1) {
            pointsInside++;
          }
        }
      }
    }

    final intersectionArea =
        (pointsInside / totalSamplePoints) * (rectWidth * rectHeight);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Rectangle-oval intersection calculated',
        'Points inside: $pointsInside/$totalSamplePoints, '
            'Intersection area: ${intersectionArea.toStringAsFixed(1)}, '
            'Rectangle area: ${(rectWidth * rectHeight).toStringAsFixed(1)}',
      ),
    );

    return intersectionArea;
  }

  /// Calculates face size factor to enforce strict distance requirements
  ///
  /// Returns a factor between 0.0 and 1.0 where:
  /// - Very distant faces (< 12% screen height) get 5-15% coverage
  /// - Distant faces (12-18% screen height) get 15-25% coverage
  /// - Small faces (18-22% screen height) get 25-40% coverage
  /// - Acceptable faces (22%+ screen height) get 40%+ coverage
  /// - Optimal faces (30%+ screen height) get 80%+ coverage
  double _calculateFaceSizeFactor(double faceSizeRatio) {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Calculating face size factor with strict distance enforcement',
        'Face size ratio: '
            '${(faceSizeRatio * 100).toStringAsFixed(1)}% of screen height',
      ),
    );

    // VERY DISTANT FACES: Show 5-15% coverage to encourage moving closer
    if (faceSizeRatio < _veryDistantFaceThreshold) {
      final factor = (faceSizeRatio / _veryDistantFaceThreshold) * 0.15;
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Very distant face detected - severe penalty',
          'Size ratio: ${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Very distant threshold: '
              '${(_veryDistantFaceThreshold * 100).toStringAsFixed(1)}%, '
              'Factor: ${factor.toStringAsFixed(3)} '
              '(${(factor * 100).toStringAsFixed(1)}% max coverage)',
        ),
      );
      return factor.clamp(0.05, 0.15); // 5-15% coverage range
    }

    // DISTANT FACES: Show 15-25% coverage with "Move closer" feedback
    if (faceSizeRatio < _distantFaceThreshold) {
      final progress = (faceSizeRatio - _veryDistantFaceThreshold) /
          (_distantFaceThreshold - _veryDistantFaceThreshold);
      final factor = 0.15 + (progress * 0.10); // 15% to 25%
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Distant face detected - strong penalty',
          'Size ratio: ${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Distant threshold: '
              '${(_distantFaceThreshold * 100).toStringAsFixed(1)}%, '
              'Progress: ${(progress * 100).toStringAsFixed(1)}%, '
              'Factor: ${factor.toStringAsFixed(3)} '
              '(${(factor * 100).toStringAsFixed(1)}% max coverage)',
        ),
      );
      return factor.clamp(0.15, 0.25); // 15-25% coverage range
    }

    // SMALL FACES: Show 25-40% coverage - still need to move closer
    if (faceSizeRatio < _minFaceSizeRatio) {
      final progress = (faceSizeRatio - _distantFaceThreshold) /
          (_minFaceSizeRatio - _distantFaceThreshold);
      final factor = 0.25 + (progress * 0.15); // 25% to 40%
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Small face detected - moderate penalty',
          'Size ratio: ${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Min threshold: '
              '${(_minFaceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Progress: ${(progress * 100).toStringAsFixed(1)}%, '
              'Factor: ${factor.toStringAsFixed(3)} '
              '(${(factor * 100).toStringAsFixed(1)}% max coverage)',
        ),
      );
      return factor.clamp(0.25, 0.40); // 25-40% coverage range
    }

    // ACCEPTABLE SIZE: Show 40-80% coverage - getting close to good
    if (faceSizeRatio < _optimalFaceSizeRatio) {
      final progress = (faceSizeRatio - _minFaceSizeRatio) /
          (_optimalFaceSizeRatio - _minFaceSizeRatio);
      final factor = 0.40 + (progress * 0.40); // 40% to 80%
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Acceptable face size - reduced penalty',
          'Size ratio: ${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Optimal threshold: '
              '${(_optimalFaceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Progress: ${(progress * 100).toStringAsFixed(1)}%, '
              'Factor: ${factor.toStringAsFixed(3)} '
              '(${(factor * 100).toStringAsFixed(1)}% max coverage)',
        ),
      );
      return factor.clamp(0.40, 0.80); // 40-80% coverage range
    }

    // OPTIMAL SIZE: Show 80-100% coverage - good for recording
    if (faceSizeRatio <= _maxFaceSizeRatio) {
      final factor = 0.80 +
          ((faceSizeRatio - _optimalFaceSizeRatio) /
              (_maxFaceSizeRatio - _optimalFaceSizeRatio) *
              0.20);
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Optimal face size - high factor',
          'Size ratio: ${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Optimal: ${(_optimalFaceSizeRatio * 100).toStringAsFixed(1)}%, '
              'Factor: ${factor.toStringAsFixed(3)} '
              '(${(factor * 100).toStringAsFixed(1)}% max coverage)',
        ),
      );
      return factor.clamp(0.80, 1.0); // 80-100% coverage range
    }

    // OVERSIZED FACES: Penalty for faces too large
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Oversized face detected - applying penalty',
        'Size ratio: ${(faceSizeRatio * 100).toStringAsFixed(1)}%, '
            'Max allowed: ${(_maxFaceSizeRatio * 100).toStringAsFixed(1)}%',
      ),
    );
    return 0.60; // 60% penalty for oversized faces
  }

  /// Gets the current face detection configuration
  Map<String, dynamic> getConfiguration() {
    return {
      'isInitialized': _isInitialized,
      'faceGuideWidthRatio': _faceGuideWidthRatio,
      'faceGuideHeightRatio': _faceGuideHeightRatio,
      'minimumCoverageThreshold': _minimumCoverageThreshold,
      'detectorOptions': _faceDetector != null
          ? {
              'enableContours': false,
              'enableLandmarks': false,
              'enableClassification': false,
              'enableTracking': true,
              'minFaceSize': 0.15,
              'performanceMode': 'fast',
            }
          : null,
    };
  }

  /// Updates the face detection configuration
  Future<void> updateConfiguration(Map<String, dynamic> config) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection configuration update requested',
        'Config: $config',
      ),
    );

    // In a real implementation, this would update detector settings
    // For now, we just log the configuration change
  }

  /// Generates real-time feedback message based on face
  /// detection result using unified design tokens
  String generateFeedbackMessage(FaceDetectionResult result) {
    final state = FaceDetectionDesignTokens.getDetectionState(
      faceDetected: result.faceDetected,
      faceCount: result.faceCount,
      coveragePercentage: result.coveragePercentage,
    );

    return FaceDetectionDesignTokens.getFeedbackMessage(state);
  }

  /// Gets frame processing statistics
  Map<String, dynamic> getProcessingStats() {
    final validFramePercentage = _totalFramesProcessed > 0
        ? (_framesWithValidFace / _totalFramesProcessed) * 100
        : 0.0;

    return {
      'totalFramesProcessed': _totalFramesProcessed,
      'framesWithValidFace': _framesWithValidFace,
      'validFramePercentage': validFramePercentage,
      'lastProcessTime': _lastFrameProcessTime?.toIso8601String(),
      'isInitialized': _isInitialized,
      'faceDetectorAvailable': _faceDetector != null,
    };
  }

  /// Diagnostic method to test face detection service health
  Future<Map<String, dynamic>> runDiagnostics() async {
    final diagnostics = <String, dynamic>{};

    try {
      diagnostics['serviceInitialized'] = _isInitialized;
      diagnostics['faceDetectorCreated'] = _faceDetector != null;
      diagnostics['processingStats'] = getProcessingStats();
      diagnostics['configuration'] = getConfiguration();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service diagnostics',
          'Initialized: $_isInitialized, '
              'Detector: ${_faceDetector != null}, '
              'Frames processed: $_totalFramesProcessed',
        ),
      );

      diagnostics['status'] = 'healthy';
    } catch (error) {
      diagnostics['status'] = 'error';
      diagnostics['error'] = error.toString();

      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service diagnostics failed',
          'Error: $error',
        ),
      );
    }

    return diagnostics;
  }

  /// Tracks InputImage conversion failures for diagnostic purposes
  void trackConversionFailure() {
    _inputImageConversionFailures++;
    _logger.warning(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'InputImage conversion failure tracked',
        'Total conversion failures: $_inputImageConversionFailures',
      ),
    );
  }

  /// Resets frame processing statistics
  void resetStats() {
    _totalFramesProcessed = 0;
    _framesWithValidFace = 0;
    _lastFrameProcessTime = null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection statistics reset',
      ),
    );
  }

  /// Validates recording quality based on frame statistics
  bool validateRecordingQuality({double minimumValidFramePercentage = 80.0}) {
    final stats = getProcessingStats();
    final validFramePercentage = stats['validFramePercentage'] as double;

    final isValid = validFramePercentage >= minimumValidFramePercentage;

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Recording quality validation',
        'Valid frames: ${validFramePercentage.toStringAsFixed(1)}%, '
            'Required: $minimumValidFramePercentage%, '
            'Result: ${isValid ? 'PASS' : 'FAIL'}',
      ),
    );

    return isValid;
  }

  /// Gets comprehensive diagnostic statistics for debugging purposes
  Map<String, dynamic> getComprehensiveDiagnostics() {
    final totalConversions =
        _inputImageConversionSuccesses + _inputImageConversionFailures;
    final totalMlKitProcessing =
        _mlKitProcessingSuccesses + _mlKitProcessingFailures;

    final conversionSuccessRate = totalConversions > 0
        ? (_inputImageConversionSuccesses / totalConversions) * 100
        : 0.0;

    final mlKitSuccessRate = totalMlKitProcessing > 0
        ? (_mlKitProcessingSuccesses / totalMlKitProcessing) * 100
        : 0.0;

    final avgInputImageConversionTime = _inputImageConversionTimes.isNotEmpty
        ? _inputImageConversionTimes.reduce((a, b) => a + b) /
            _inputImageConversionTimes.length
        : 0.0;

    final avgMlKitProcessingTime = _mlKitProcessingTimes.isNotEmpty
        ? _mlKitProcessingTimes.reduce((a, b) => a + b) /
            _mlKitProcessingTimes.length
        : 0.0;

    final avgResultConversionTime = _resultConversionTimes.isNotEmpty
        ? _resultConversionTimes.reduce((a, b) => a + b) /
            _resultConversionTimes.length
        : 0.0;

    final avgRecentProcessingTime = _recentProcessingTimes.isNotEmpty
        ? _recentProcessingTimes.reduce((a, b) => a + b) /
            _recentProcessingTimes.length
        : 0.0;

    return {
      'frameSequenceNumber': _frameSequenceNumber,
      'conversionStats': {
        'successes': _inputImageConversionSuccesses,
        'failures': _inputImageConversionFailures,
        'total': totalConversions,
        'successRate': conversionSuccessRate,
      },
      'mlKitStats': {
        'successes': _mlKitProcessingSuccesses,
        'failures': _mlKitProcessingFailures,
        'total': totalMlKitProcessing,
        'successRate': mlKitSuccessRate,
      },
      'performanceStats': {
        'avgInputImageConversionTime': avgInputImageConversionTime,
        'avgMlKitProcessingTime': avgMlKitProcessingTime,
        'avgResultConversionTime': avgResultConversionTime,
        'avgRecentProcessingTime': avgRecentProcessingTime,
        'consecutiveFailures': _consecutiveFailures,
        'maxConsecutiveFailures': _maxConsecutiveFailures,
      },
      'recentFailures': List<String>.from(_recentFailureReasons),
      'processingTimes': {
        'inputImageConversion': List<int>.from(_inputImageConversionTimes),
        'mlKitProcessing': List<int>.from(_mlKitProcessingTimes),
        'resultConversion': List<int>.from(_resultConversionTimes),
        'recent': List<int>.from(_recentProcessingTimes),
      },
    };
  }

  /// Generates a periodic diagnostic summary with detailed analysis
  void logPeriodicDiagnosticSummary() {
    final diagnostics = getComprehensiveDiagnostics();
    final conversionStats =
        diagnostics['conversionStats'] as Map<String, dynamic>;
    final mlKitStats = diagnostics['mlKitStats'] as Map<String, dynamic>;
    final performanceStats =
        diagnostics['performanceStats'] as Map<String, dynamic>;

    final conversionRate =
        (conversionStats['successRate'] as double).toStringAsFixed(1);
    final mlKitRate = (mlKitStats['successRate'] as double).toStringAsFixed(1);
    final inputTime =
        (performanceStats['avgInputImageConversionTime'] as double)
            .toStringAsFixed(1);
    final mlKitTime = (performanceStats['avgMlKitProcessingTime'] as double)
        .toStringAsFixed(1);
    final resultTime = (performanceStats['avgResultConversionTime'] as double)
        .toStringAsFixed(1);
    final recentTime = (performanceStats['avgRecentProcessingTime'] as double)
        .toStringAsFixed(1);

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Periodic diagnostic summary',
        'Frame #${diagnostics['frameSequenceNumber']}, '
            'Conversion: ${conversionStats['successes']}/'
            '${conversionStats['total']} ($conversionRate%), '
            'ML Kit: ${mlKitStats['successes']}/'
            '${mlKitStats['total']} ($mlKitRate%), '
            'Avg times: Input=${inputTime}ms, ML Kit=${mlKitTime}ms, '
            'Result=${resultTime}ms, Recent=${recentTime}ms, '
            'Consecutive failures: ${performanceStats['consecutiveFailures']} '
            '(max: ${performanceStats['maxConsecutiveFailures']})',
      ),
    );
  }

  /// Analyzes failure patterns and provides insights for debugging
  Map<String, dynamic> analyzeFailurePatterns() {
    final totalFailures =
        _inputImageConversionFailures + _mlKitProcessingFailures;
    final recentFailureCount = _recentFailureReasons.length;

    // Count failure types
    final failureTypeCounts = <String, int>{};
    for (final reason in _recentFailureReasons) {
      failureTypeCounts[reason] = (failureTypeCounts[reason] ?? 0) + 1;
    }

    // Find most common failure type
    String? mostCommonFailure;
    var maxCount = 0;
    for (final entry in failureTypeCounts.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        mostCommonFailure = entry.key;
      }
    }

    final analysis = {
      'totalFailures': totalFailures,
      'recentFailureCount': recentFailureCount,
      'failureTypeCounts': failureTypeCounts,
      'mostCommonFailure': mostCommonFailure,
      'mostCommonFailureCount': maxCount,
      'consecutiveFailures': _consecutiveFailures,
      'maxConsecutiveFailures': _maxConsecutiveFailures,
      'failureRate': _totalFramesProcessed > 0
          ? (totalFailures / _totalFramesProcessed) * 100
          : 0.0,
    };

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Failure pattern analysis',
        'Total failures: $totalFailures, Recent: $recentFailureCount, '
            'Most common: $mostCommonFailure ($maxCount times), '
            'Consecutive: $_consecutiveFailures '
            '(max: $_maxConsecutiveFailures), '
            'Rate: ${(analysis['failureRate']! as double).toStringAsFixed(1)}%',
      ),
    );

    return analysis;
  }

  /// Gets ML Kit processing performance metrics
  Map<String, dynamic> getMlKitPerformanceMetrics() {
    final totalProcessed = _mlKitProcessingSuccesses + _mlKitProcessingFailures;
    final successRate = totalProcessed > 0
        ? (_mlKitProcessingSuccesses / totalProcessed) * 100
        : 0.0;

    final avgProcessingTime = _mlKitProcessingTimes.isNotEmpty
        ? _mlKitProcessingTimes.reduce((a, b) => a + b) /
            _mlKitProcessingTimes.length
        : 0.0;

    final minProcessingTime = _mlKitProcessingTimes.isNotEmpty
        ? _mlKitProcessingTimes.reduce((a, b) => a < b ? a : b)
        : 0;

    final maxProcessingTime = _mlKitProcessingTimes.isNotEmpty
        ? _mlKitProcessingTimes.reduce((a, b) => a > b ? a : b)
        : 0;

    final metrics = {
      'totalProcessed': totalProcessed,
      'successes': _mlKitProcessingSuccesses,
      'failures': _mlKitProcessingFailures,
      'successRate': successRate,
      'avgProcessingTime': avgProcessingTime,
      'minProcessingTime': minProcessingTime,
      'maxProcessingTime': maxProcessingTime,
      'recentProcessingTimes': List<int>.from(_mlKitProcessingTimes),
    };

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'ML Kit performance metrics',
        'Processed: $totalProcessed, Success rate: '
            '${successRate.toStringAsFixed(1)}%, '
            'Avg time: ${avgProcessingTime.toStringAsFixed(1)}ms, '
            'Range: ${minProcessingTime}ms-${maxProcessingTime}ms',
      ),
    );

    return metrics;
  }

  /// Gets conversion success rate analysis
  Map<String, dynamic> getConversionSuccessRateAnalysis() {
    final totalConversions =
        _inputImageConversionSuccesses + _inputImageConversionFailures;
    final successRate = totalConversions > 0
        ? (_inputImageConversionSuccesses / totalConversions) * 100
        : 0.0;

    final avgConversionTime = _inputImageConversionTimes.isNotEmpty
        ? _inputImageConversionTimes.reduce((a, b) => a + b) /
            _inputImageConversionTimes.length
        : 0.0;

    final analysis = {
      'totalConversions': totalConversions,
      'successes': _inputImageConversionSuccesses,
      'failures': _inputImageConversionFailures,
      'successRate': successRate,
      'avgConversionTime': avgConversionTime,
      'recentConversionTimes': List<int>.from(_inputImageConversionTimes),
    };

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Conversion success rate analysis',
        'Total: $totalConversions, Success rate: '
            '${successRate.toStringAsFixed(1)}%, '
            'Avg time: ${avgConversionTime.toStringAsFixed(1)}ms',
      ),
    );

    return analysis;
  }

  /// Disposes of the face detection service and releases resources
  Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal skipped - not initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service disposal started',
      ),
    );

    try {
      await _faceDetector?.close();
      _faceDetector = null;
      _isInitialized = false;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection service disposal error: $error',
        ),
        error,
        stackTrace,
      );
      // Don't rethrow disposal errors
    }
  }
}

/// {@template size}
/// Simple size class for width and height dimensions.
/// {@endtemplate}
class Size {
  /// {@macro size}
  const Size(this.width, this.height);

  /// Width dimension
  final double width;

  /// Height dimension
  final double height;

  @override
  String toString() => 'Size($width, $height)';
}

/// {@template face_quality}
/// Represents the quality analysis of a detected face.
/// {@endtemplate}
class FaceQuality {
  /// {@macro face_quality}
  const FaceQuality({
    required this.overallConfidence,
    required this.issues,
    this.headRotationX,
    this.headRotationY,
    this.headRotationZ,
    this.leftEyeOpenProbability,
    this.rightEyeOpenProbability,
  });

  /// Overall confidence score (0.0-1.0)
  final double overallConfidence;

  /// List of quality issues detected
  final List<String> issues;

  /// Head rotation angle X (up/down tilt)
  final double? headRotationX;

  /// Head rotation angle Y (left/right turn)
  final double? headRotationY;

  /// Head rotation angle Z (side tilt)
  final double? headRotationZ;

  /// Left eye open probability
  final double? leftEyeOpenProbability;

  /// Right eye open probability
  final double? rightEyeOpenProbability;

  /// Whether the face quality is acceptable
  bool get isAcceptable => overallConfidence >= 0.6 && issues.isEmpty;

  @override
  String toString() {
    return 'FaceQuality('
        'confidence: ${overallConfidence.toStringAsFixed(2)}, '
        'issues: $issues'
        ')';
  }
}
