import 'package:bloomg_flutter/auth/cubit/auth_cubit.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/auth/repository/firebase_auth_repository.dart';
import 'package:bloomg_flutter/core/storage/hive_service.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/bloc/profile_bloc.dart';
import 'package:bloomg_flutter/features/onboarding/repository/asvs_profile_repository.dart';
import 'package:bloomg_flutter/features/onboarding/services/onboarding_service.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:bloomg_flutter/features/video_gallery/services/firebase_video_metadata_service.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

/// Global service locator instance.
final GetIt getIt = GetIt.instance;

/// {@template injection}
/// Dependency injection setup for the application.
/// {@endtemplate}
class Injection {
  /// Private constructor to prevent instantiation.
  Injection._();

  static final LoggerService _logger = LoggerService();

  /// Initializes all dependencies.
  static Future<void> initialize() async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Dependency injection initialization started',
      ),
    );

    try {
      // Initialize storage service first
      await _initializeStorage();

      // Register repositories
      _registerRepositories();

      // Register face verification dependencies
      _registerFaceVerificationDependencies();

      // Register onboarding dependencies
      _registerOnboardingDependencies();

      // Register cubits
      _registerCubits();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Dependency injection initialization completed',
        ),
      );
    } catch (e, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.appModule,
          LoggingConstants.criticalError,
          'Dependency injection initialization failed: $e',
          'Application may not function properly',
        ),
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Initializes storage services.
  static Future<void> _initializeStorage() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Initializing storage services',
      ),
    );

    // Initialize Hive service
    final hiveService = HiveService();
    await hiveService.initialize();

    // Register as singleton
    getIt.registerSingleton<HiveService>(hiveService);

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Storage services initialized',
      ),
    );
  }

  /// Registers repository dependencies.
  static void _registerRepositories() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering repositories',
      ),
    );

    // Register AuthRepository
    getIt.registerLazySingleton<AuthRepository>(
      () => FirebaseAuthRepository(
        hiveService: getIt<HiveService>(),
      ),
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Repositories registered',
      ),
    );
  }

  /// Registers face verification dependencies.
  static void _registerFaceVerificationDependencies() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering face verification dependencies',
      ),
    );

    // Register face verification repositories
    getIt
      ..registerLazySingleton<FaceDetectionRepository>(
        FaceDetectionRepository.new,
      )
      ..registerLazySingleton<VideoStorageRepository>(
        VideoStorageRepository.new,
      )
      ..registerLazySingleton<VideoValidationService>(
        VideoValidationService.new,
      )

      // Register video gallery services
      ..registerLazySingleton<FirebaseVideoMetadataService>(
        FirebaseVideoMetadataService.new,
      )
      ..registerLazySingleton<VideoGalleryService>(
        () => VideoGalleryService(
          metadataService: getIt<FirebaseVideoMetadataService>(),
          authRepository: getIt<AuthRepository>(),
        ),
      )
      ..registerLazySingleton<VideoGalleryRepository>(
        () => VideoGalleryRepository(
          videoGalleryService: getIt<VideoGalleryService>(),
        ),
      )

      // Register face verification BLoC
      ..registerFactory<FaceVideoCaptureBloc>(
        () => FaceVideoCaptureBloc(
          faceDetectionRepository: getIt<FaceDetectionRepository>(),
          videoStorageRepository: getIt<VideoStorageRepository>(),
          videoValidationService: getIt<VideoValidationService>(),
        ),
      );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Face verification dependencies registered',
      ),
    );
  }

  /// Registers onboarding dependencies.
  static void _registerOnboardingDependencies() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering onboarding dependencies',
      ),
    );

    // Register Firebase services
    getIt
      ..registerLazySingleton<FirebaseFirestore>(
        () => FirebaseFirestore.instance,
      )
      ..registerLazySingleton<FirebaseAuth>(
        () => FirebaseAuth.instance,
      )

      // Register onboarding service
      ..registerLazySingleton<OnboardingService>(
        OnboardingService.new,
      )

      // Register ASVS profile repository
      ..registerLazySingleton<AsvsProfileRepository>(
        () => FirebaseAsvsProfileRepository(
          hiveService: getIt<HiveService>(),
          firestore: getIt<FirebaseFirestore>(),
          firebaseAuth: getIt<FirebaseAuth>(),
        ),
      )

      // Register onboarding BLoCs
      ..registerFactory<OnboardingBloc>(
        () => OnboardingBloc(
          repository: getIt<AsvsProfileRepository>(),
          onboardingService: getIt<OnboardingService>(),
        ),
      )
      ..registerFactory<ProfileBloc>(
        () => ProfileBloc(
          repository: getIt<AsvsProfileRepository>(),
          onboardingService: getIt<OnboardingService>(),
        ),
      );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Onboarding dependencies registered',
      ),
    );
  }

  /// Registers cubit dependencies.
  static void _registerCubits() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Registering cubits',
      ),
    );

    // Register AuthCubit as singleton to maintain global state
    getIt.registerLazySingleton<AuthCubit>(
      () => AuthCubit(getIt<AuthRepository>()),
    );

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Cubits registered',
      ),
    );
  }

  /// Resets all dependencies (useful for testing).
  static Future<void> reset() async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Resetting dependency injection',
      ),
    );

    // Close Hive service if registered
    if (getIt.isRegistered<HiveService>()) {
      await getIt<HiveService>().close();
    }

    // Reset GetIt
    await getIt.reset();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.appModule,
        'Dependency injection reset completed',
      ),
    );
  }
}
