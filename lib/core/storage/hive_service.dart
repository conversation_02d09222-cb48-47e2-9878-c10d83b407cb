import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/features/onboarding/models/models.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// {@template hive_service}
/// Service for managing local data storage using Hive.
/// {@endtemplate}
class HiveService {
  /// Creates the singleton instance of HiveService.
  factory HiveService() {
    _instance ??= HiveService._();
    return _instance!;
  }

  /// {@macro hive_service}
  HiveService._();

  static HiveService? _instance;
  static final LoggerService _logger = LoggerService();

  // Box names
  static const String _userBoxName = 'user_box';
  static const String _authBoxName = 'auth_box';
  static const String _asvsProfileBoxName = 'asvs_profile_box';

  // Box instances
  Box<UserModel>? _userBox;
  Box<dynamic>? _authBox;
  Box<AsvsUserProfile>? _asvsProfileBox;

  /// Initializes Hive and registers adapters.
  Future<void> initialize() async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Hive initialization started',
        ),
      );

      // Initialize Hive Flutter
      await Hive.initFlutter();

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(UserModelAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(AsvsUserProfileAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(UnitPreferenceAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(GenderAdapter());
      }
      if (!Hive.isAdapterRegistered(4)) {
        Hive.registerAdapter(SmokingStatusAdapter());
      }

      // Open boxes
      await _openBoxes();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Hive initialization completed successfully',
        ),
      );
    } catch (e, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.appModule,
          LoggingConstants.criticalError,
          'Hive initialization failed: $e',
          'Storage service unavailable',
        ),
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Opens all required Hive boxes.
  Future<void> _openBoxes() async {
    try {
      _userBox = await Hive.openBox<UserModel>(_userBoxName);
      _authBox = await Hive.openBox<dynamic>(_authBoxName);
      _asvsProfileBox =
          await Hive.openBox<AsvsUserProfile>(_asvsProfileBoxName);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Hive boxes opened successfully',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.appModule,
          LoggingConstants.criticalError,
          'Failed to open Hive boxes: $e',
          'Storage boxes unavailable',
        ),
        e,
      );
      rethrow;
    }
  }

  /// Gets the user box.
  Box<UserModel> get userBox {
    if (_userBox == null || !_userBox!.isOpen) {
      throw StateError('User box is not initialized or closed');
    }
    return _userBox!;
  }

  /// Gets the auth box.
  Box<dynamic> get authBox {
    if (_authBox == null || !_authBox!.isOpen) {
      throw StateError('Auth box is not initialized or closed');
    }
    return _authBox!;
  }

  /// Gets the ASVS profile box.
  Box<AsvsUserProfile> get asvsProfileBox {
    if (_asvsProfileBox == null || !_asvsProfileBox!.isOpen) {
      throw StateError('ASVS profile box is not initialized or closed');
    }
    return _asvsProfileBox!;
  }

  /// Saves the current user to local storage.
  Future<void> saveUser(UserModel user) async {
    try {
      await userBox.put('current_user', user);
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'User saved to local storage: ${user.email}',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to save user: $e',
          'User: ${user.email}',
        ),
        e,
      );
      rethrow;
    }
  }

  /// Gets the current user from local storage.
  UserModel? getCurrentUser() {
    try {
      final user = userBox.get('current_user');
      if (user != null) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.authModule,
            'User loaded from local storage: ${user.email}',
          ),
        );
      }
      return user;
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to get current user: $e',
          'Returning null',
        ),
        e,
      );
      return null;
    }
  }

  /// Removes the current user from local storage.
  Future<void> removeUser() async {
    try {
      await userBox.delete('current_user');
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'User removed from local storage',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to remove user: $e',
          'Local storage cleanup failed',
        ),
        e,
      );
      rethrow;
    }
  }

  /// Saves authentication token or session data.
  Future<void> saveAuthData(String key, dynamic value) async {
    try {
      await authBox.put(key, value);
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'Auth data saved: $key',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to save auth data: $e',
          'Key: $key',
        ),
        e,
      );
      rethrow;
    }
  }

  /// Gets authentication data by key.
  T? getAuthData<T>(String key) {
    try {
      return authBox.get(key) as T?;
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to get auth data: $e',
          'Key: $key',
        ),
        e,
      );
      return null;
    }
  }

  /// Removes authentication data by key.
  Future<void> removeAuthData(String key) async {
    try {
      await authBox.delete(key);
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'Auth data removed: $key',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to remove auth data: $e',
          'Key: $key',
        ),
        e,
      );
      rethrow;
    }
  }

  /// Clears all authentication data.
  Future<void> clearAllAuthData() async {
    try {
      await Future.wait([
        userBox.clear(),
        authBox.clear(),
      ]);
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.authModule,
          'All authentication data cleared',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.authModule,
          LoggingConstants.recoverableError,
          'Failed to clear auth data: $e',
          'Partial cleanup may have occurred',
        ),
        e,
      );
      rethrow;
    }
  }

  /// Closes all Hive boxes.
  Future<void> close() async {
    try {
      await Future.wait([
        if (_userBox?.isOpen ?? false) _userBox!.close(),
        if (_authBox?.isOpen ?? false) _authBox!.close(),
      ]);
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.appModule,
          'Hive boxes closed',
        ),
      );
    } catch (e) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.appModule,
          LoggingConstants.recoverableError,
          'Failed to close Hive boxes: $e',
          'Some boxes may remain open',
        ),
        e,
      );
    }
  }
}
